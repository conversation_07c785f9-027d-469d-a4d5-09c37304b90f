-- Learning Paths System Migration
-- Creates comprehensive learning path management with video associations

-- Create learning_paths table
CREATE TABLE IF NOT EXISTS learning_paths (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Basic information
  title TEXT NOT NULL,
  description TEXT,
  difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')) DEFAULT 'beginner',
  estimated_duration_hours INTEGER,
  
  -- Content organization
  skills TEXT[] DEFAULT '{}',
  categories TEXT[] DEFAULT '{}',
  prerequisites TEXT[] DEFAULT '{}',
  learning_objectives TEXT[] DEFAULT '{}',
  target_audience TEXT,
  
  -- Status and visibility
  is_active BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  
  -- Metadata
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Search optimization
  search_vector tsvector GENERATED ALWAYS AS (
    to_tsvector('english', 
      COALESCE(title, '') || ' ' || 
      COALESCE(description, '') || ' ' || 
      COALESCE(target_audience, '') || ' ' ||
      COALESCE(array_to_string(skills, ' '), '') || ' ' ||
      COALESCE(array_to_string(categories, ' '), '')
    )
  ) STORED
);

-- Create learning_path_videos junction table
CREATE TABLE IF NOT EXISTS learning_path_videos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

  -- Relationships
  learning_path_id UUID NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  course_id UUID NOT NULL, -- Will reference course_catalog when it exists
  
  -- Ordering
  order_index INTEGER NOT NULL,
  
  -- Optional metadata for this specific video in the path
  custom_title TEXT, -- Override video title for this path
  custom_description TEXT, -- Additional context for this video in the path
  is_optional BOOLEAN DEFAULT false,
  estimated_completion_time INTEGER, -- Override duration for this path context
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique ordering within each path
  UNIQUE(learning_path_id, order_index),
  -- Prevent duplicate videos in same path
  UNIQUE(learning_path_id, course_id)
);

-- Create learning_path_enrollments table
CREATE TABLE IF NOT EXISTS learning_path_enrollments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationships
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  learning_path_id UUID NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  
  -- Progress tracking
  status TEXT CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused')) DEFAULT 'not_started',
  progress_percentage DECIMAL(5,2) DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  current_video_index INTEGER DEFAULT 1,
  
  -- Timestamps
  enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  last_accessed_at TIMESTAMP WITH TIME ZONE,
  
  -- Prevent duplicate enrollments
  UNIQUE(user_id, learning_path_id)
);

-- Create learning_path_video_progress table
CREATE TABLE IF NOT EXISTS learning_path_video_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationships
  enrollment_id UUID NOT NULL REFERENCES learning_path_enrollments(id) ON DELETE CASCADE,
  learning_path_video_id UUID NOT NULL REFERENCES learning_path_videos(id) ON DELETE CASCADE,
  
  -- Progress details
  status TEXT CHECK (status IN ('not_started', 'in_progress', 'completed', 'skipped')) DEFAULT 'not_started',
  completion_percentage DECIMAL(5,2) DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
  time_spent_minutes INTEGER DEFAULT 0,
  
  -- Timestamps
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent duplicate progress records
  UNIQUE(enrollment_id, learning_path_video_id)
);

-- Create course_catalog table if it doesn't exist (referenced by views)
CREATE TABLE IF NOT EXISTS course_catalog (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  duration_minutes INTEGER DEFAULT 0,
  provider TEXT,
  url TEXT,
  difficulty_level TEXT DEFAULT 'beginner',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add missing columns to existing learning_paths table
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS title TEXT DEFAULT 'Untitled Learning Path';
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false;
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert'));
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS search_vector tsvector;
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS skills JSONB DEFAULT '[]'::jsonb;
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS categories JSONB DEFAULT '[]'::jsonb;
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS estimated_duration_hours INTEGER DEFAULT 0;
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS prerequisites JSONB DEFAULT '[]'::jsonb;
ALTER TABLE learning_paths ADD COLUMN IF NOT EXISTS learning_objectives JSONB DEFAULT '[]'::jsonb;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_learning_paths_active ON learning_paths(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_learning_paths_featured ON learning_paths(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_learning_paths_difficulty ON learning_paths(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_learning_paths_search ON learning_paths USING gin(search_vector);
CREATE INDEX IF NOT EXISTS idx_learning_paths_skills ON learning_paths USING gin(skills);
CREATE INDEX IF NOT EXISTS idx_learning_paths_categories ON learning_paths USING gin(categories);

CREATE INDEX IF NOT EXISTS idx_learning_path_videos_path_order ON learning_path_videos(learning_path_id, order_index);
CREATE INDEX IF NOT EXISTS idx_learning_path_videos_course ON learning_path_videos(course_id);

CREATE INDEX IF NOT EXISTS idx_learning_path_enrollments_user ON learning_path_enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_path_enrollments_path ON learning_path_enrollments(learning_path_id);
CREATE INDEX IF NOT EXISTS idx_learning_path_enrollments_status ON learning_path_enrollments(status);

CREATE INDEX IF NOT EXISTS idx_learning_path_video_progress_enrollment ON learning_path_video_progress(enrollment_id);
CREATE INDEX IF NOT EXISTS idx_learning_path_video_progress_video ON learning_path_video_progress(learning_path_video_id);

-- Create function to update learning path progress
CREATE OR REPLACE FUNCTION update_learning_path_progress()
RETURNS TRIGGER AS $$
BEGIN
  -- Update enrollment progress based on video completions
  UPDATE learning_path_enrollments 
  SET 
    progress_percentage = (
      SELECT COALESCE(
        (COUNT(*) FILTER (WHERE lpvp.status = 'completed')::DECIMAL / 
         NULLIF(COUNT(*), 0)) * 100, 
        0
      )
      FROM learning_path_video_progress lpvp
      JOIN learning_path_videos lpv ON lpv.id = lpvp.learning_path_video_id
      WHERE lpvp.enrollment_id = NEW.enrollment_id
    ),
    status = CASE 
      WHEN (
        SELECT COUNT(*) FILTER (WHERE lpvp.status = 'completed')
        FROM learning_path_video_progress lpvp
        JOIN learning_path_videos lpv ON lpv.id = lpvp.learning_path_video_id
        WHERE lpvp.enrollment_id = NEW.enrollment_id
      ) = (
        SELECT COUNT(*)
        FROM learning_path_videos lpv
        JOIN learning_path_enrollments lpe ON lpe.learning_path_id = lpv.learning_path_id
        WHERE lpe.id = NEW.enrollment_id
      ) THEN 'completed'
      WHEN (
        SELECT COUNT(*) FILTER (WHERE lpvp.status IN ('in_progress', 'completed'))
        FROM learning_path_video_progress lpvp
        WHERE lpvp.enrollment_id = NEW.enrollment_id
      ) > 0 THEN 'in_progress'
      ELSE 'not_started'
    END,
    completed_at = CASE 
      WHEN (
        SELECT COUNT(*) FILTER (WHERE lpvp.status = 'completed')
        FROM learning_path_video_progress lpvp
        JOIN learning_path_videos lpv ON lpv.id = lpvp.learning_path_video_id
        WHERE lpvp.enrollment_id = NEW.enrollment_id
      ) = (
        SELECT COUNT(*)
        FROM learning_path_videos lpv
        JOIN learning_path_enrollments lpe ON lpe.learning_path_id = lpv.learning_path_id
        WHERE lpe.id = NEW.enrollment_id
      ) THEN NOW()
      ELSE NULL
    END
  WHERE id = NEW.enrollment_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update learning path progress
DROP TRIGGER IF EXISTS trigger_update_learning_path_progress ON learning_path_video_progress;
CREATE TRIGGER trigger_update_learning_path_progress
  AFTER INSERT OR UPDATE OR DELETE ON learning_path_video_progress
  FOR EACH ROW
  EXECUTE FUNCTION update_learning_path_progress();

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS trigger_learning_paths_updated_at ON learning_paths;
CREATE TRIGGER trigger_learning_paths_updated_at
  BEFORE UPDATE ON learning_paths
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_learning_path_videos_updated_at ON learning_path_videos;
CREATE TRIGGER trigger_learning_path_videos_updated_at
  BEFORE UPDATE ON learning_path_videos
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create comprehensive learning path analytics view
CREATE OR REPLACE VIEW learning_path_analytics AS
SELECT 
  lp.id,
  lp.title,
  lp.difficulty_level,
  lp.estimated_duration_hours,
  lp.is_active,
  lp.is_featured,
  lp.created_at,
  COUNT(DISTINCT lpv.id) as total_videos,
  COUNT(DISTINCT lpe.user_id) as total_enrollments,
  COUNT(DISTINCT CASE WHEN lpe.status = 'completed' THEN lpe.user_id END) as completions,
  COUNT(DISTINCT CASE WHEN lpe.status = 'in_progress' THEN lpe.user_id END) as active_learners,
  AVG(lpe.progress_percentage) as avg_progress,
  CASE 
    WHEN COUNT(DISTINCT lpe.user_id) > 0 
    THEN (COUNT(DISTINCT CASE WHEN lpe.status = 'completed' THEN lpe.user_id END)::DECIMAL / COUNT(DISTINCT lpe.user_id)) * 100
    ELSE 0 
  END as completion_rate,
  SUM(COALESCE(cc.duration_minutes, 0)) as total_duration_minutes
FROM learning_paths lp
LEFT JOIN learning_path_videos lpv ON lpv.learning_path_id = lp.id
LEFT JOIN course_catalog cc ON cc.id = lpv.course_id
LEFT JOIN learning_path_enrollments lpe ON lpe.learning_path_id = lp.id
GROUP BY lp.id, lp.title, lp.difficulty_level, lp.estimated_duration_hours, lp.is_active, lp.is_featured, lp.created_at;

-- Add RLS policies
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_path_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_path_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_path_video_progress ENABLE ROW LEVEL SECURITY;

-- Learning paths policies (public read, admin write)
CREATE POLICY "Anyone can view active learning paths" ON learning_paths
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage learning paths" ON learning_paths
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Learning path videos policies
CREATE POLICY "Anyone can view learning path videos" ON learning_path_videos
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage learning path videos" ON learning_path_videos
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Enrollment policies
CREATE POLICY "Users can view their own enrollments" ON learning_path_enrollments
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can enroll themselves" ON learning_path_enrollments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own enrollments" ON learning_path_enrollments
  FOR UPDATE USING (auth.uid() = user_id);

-- Progress policies
CREATE POLICY "Users can view their own progress" ON learning_path_video_progress
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM learning_path_enrollments lpe 
      WHERE lpe.id = enrollment_id AND lpe.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own progress" ON learning_path_video_progress
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM learning_path_enrollments lpe 
      WHERE lpe.id = enrollment_id AND lpe.user_id = auth.uid()
    )
  );

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON learning_paths TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON learning_path_videos TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON learning_path_enrollments TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON learning_path_video_progress TO authenticated;
GRANT SELECT ON learning_path_analytics TO authenticated;

-- Comments
COMMENT ON TABLE learning_paths IS 'Official learning paths created by admins to organize educational content';
COMMENT ON TABLE learning_path_videos IS 'Videos included in learning paths with ordering and metadata';
COMMENT ON TABLE learning_path_enrollments IS 'User enrollments in learning paths with progress tracking';
COMMENT ON TABLE learning_path_video_progress IS 'Individual video progress within learning path enrollments';
COMMENT ON VIEW learning_path_analytics IS 'Analytics view for learning path performance and engagement metrics';
