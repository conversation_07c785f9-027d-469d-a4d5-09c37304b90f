-- Fix Learning Center Schema Issues
-- This migration fixes all the Learning Center database problems

-- ============================================================================
-- 1. CREATE MISSING PROFILES TABLE
-- ============================================================================

-- Create profiles table that the frontend expects
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    display_name TEXT,
    bio TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    UNIQUE(user_id)
);

-- ============================================================================
-- 2. FIX LEARNING_PROGRESS TABLE STRUCTURE
-- ============================================================================

-- Add missing columns to learning_progress table
ALTER TABLE public.learning_progress ADD COLUMN IF NOT EXISTS course_catalog_id UUID;
ALTER TABLE public.learning_progress ADD COLUMN IF NOT EXISTS last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Create the foreign key relationship that the frontend expects
-- First, let's see if we need to add the constraint
DO $$
BEGIN
    -- Add foreign key constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'learning_progress_course_id_fkey'
        AND table_name = 'learning_progress'
    ) THEN
        -- Add the foreign key constraint to course_catalog
        ALTER TABLE public.learning_progress 
        ADD CONSTRAINT learning_progress_course_id_fkey 
        FOREIGN KEY (course_catalog_id) REFERENCES public.course_catalog(id) ON DELETE CASCADE;
    END IF;
END $$;

-- ============================================================================
-- 3. ENSURE COURSE_CATALOG TABLE HAS CORRECT STRUCTURE
-- ============================================================================

-- Add missing columns to course_catalog if they don't exist
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS skills JSONB DEFAULT '[]'::jsonb;
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS categories JSONB DEFAULT '[]'::jsonb;
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS difficulty_level TEXT DEFAULT 'beginner';
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS rating DECIMAL(3,2) DEFAULT 0.0;
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS provider TEXT DEFAULT 'youtube';
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS thumbnail_url TEXT;
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS course_url TEXT;
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS external_id TEXT;
ALTER TABLE public.course_catalog ADD COLUMN IF NOT EXISTS duration_minutes INTEGER DEFAULT 0;

-- ============================================================================
-- 4. CREATE PROPER RLS POLICIES FOR LEARNING CENTER
-- ============================================================================

-- Enable RLS on all learning tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.course_catalog ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.learning_progress ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Public can view active courses" ON public.course_catalog;
DROP POLICY IF EXISTS "Anyone can view active courses" ON public.course_catalog;
DROP POLICY IF EXISTS "Admins can manage courses" ON public.course_catalog;
DROP POLICY IF EXISTS "Users can view their own learning progress" ON public.learning_progress;

-- Add user_id column to profiles table if it doesn't exist
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Update user_id column to match id for existing records
UPDATE public.profiles SET user_id = id WHERE user_id IS NULL;

-- Create simple, working policies for profiles
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT USING (id = auth.uid() OR user_id = auth.uid());

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT WITH CHECK (id = auth.uid() OR user_id = auth.uid());

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE USING (id = auth.uid() OR user_id = auth.uid());

-- Create policies for course_catalog (public read access)
CREATE POLICY "course_catalog_public_read" ON public.course_catalog
    FOR SELECT USING (is_active = true);

CREATE POLICY "course_catalog_admin_manage" ON public.course_catalog
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND u.is_admin = true
        )
    );

-- Create policies for learning_progress
CREATE POLICY "learning_progress_select_own" ON public.learning_progress
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "learning_progress_insert_own" ON public.learning_progress
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "learning_progress_update_own" ON public.learning_progress
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "learning_progress_delete_own" ON public.learning_progress
    FOR DELETE USING (user_id = auth.uid());

-- ============================================================================
-- 5. CREATE PROPER INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes for profiles
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON public.profiles(user_id);

-- Indexes for course_catalog
CREATE INDEX IF NOT EXISTS idx_course_catalog_provider ON public.course_catalog(provider);
CREATE INDEX IF NOT EXISTS idx_course_catalog_active ON public.course_catalog(is_active);
CREATE INDEX IF NOT EXISTS idx_course_catalog_difficulty ON public.course_catalog(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_course_catalog_rating ON public.course_catalog(rating);

-- Indexes for learning_progress
CREATE INDEX IF NOT EXISTS idx_learning_progress_user_id ON public.learning_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_course_catalog_id ON public.learning_progress(course_catalog_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_last_accessed ON public.learning_progress(last_accessed_at);

-- ============================================================================
-- 6. CREATE SAMPLE DATA FOR TESTING
-- ============================================================================

-- Insert sample course catalog data for testing
INSERT INTO public.course_catalog (
    external_id, provider, title, description, duration_minutes, 
    difficulty_level, thumbnail_url, course_url, skills, categories, 
    rating, is_active, is_featured
) VALUES 
(
    'dQw4w9WgXcQ', 'youtube', 'Introduction to Web Development', 
    'Learn the basics of HTML, CSS, and JavaScript in this comprehensive tutorial.',
    45, 'beginner', 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    '["HTML", "CSS", "JavaScript", "Web Development"]'::jsonb,
    '["Programming", "Web Development", "Frontend"]'::jsonb,
    4.5, true, true
),
(
    'jNQXAC9IVRw', 'youtube', 'Advanced React Patterns',
    'Master advanced React patterns and best practices for scalable applications.',
    60, 'advanced', 'https://img.youtube.com/vi/jNQXAC9IVRw/maxresdefault.jpg',
    'https://www.youtube.com/watch?v=jNQXAC9IVRw',
    '["React", "JavaScript", "Frontend", "Component Design"]'::jsonb,
    '["Programming", "Web Development", "Frontend", "React"]'::jsonb,
    4.8, true, false
),
(
    'kJQP7kiw5Fk', 'youtube', 'Database Design Fundamentals',
    'Learn how to design efficient and scalable database schemas.',
    50, 'intermediate', 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg',
    'https://www.youtube.com/watch?v=kJQP7kiw5Fk',
    '["Database Design", "SQL", "Data Modeling"]'::jsonb,
    '["Database", "Backend", "Data"]'::jsonb,
    4.3, true, false
);

-- ============================================================================
-- 7. CREATE PROFILE RECORD FOR CURRENT USER
-- ============================================================================

-- Insert profile record for authenticated users
INSERT INTO public.profiles (id, user_id, display_name, bio, avatar_url)
SELECT
    auth.uid(),
    auth.uid(),
    COALESCE((SELECT display_name FROM public.users WHERE id = auth.uid()), 'User'),
    '',
    null
WHERE auth.uid() IS NOT NULL
ON CONFLICT (id) DO UPDATE SET
    user_id = COALESCE(EXCLUDED.user_id, profiles.user_id),
    display_name = COALESCE(EXCLUDED.display_name, profiles.display_name),
    updated_at = now();

-- ============================================================================
-- 8. GRANT PERMISSIONS
-- ============================================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.profiles TO authenticated;
GRANT SELECT ON public.course_catalog TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.course_catalog TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.learning_progress TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- 9. CREATE TRIGGERS FOR UPDATED_AT
-- ============================================================================

-- Create trigger for profiles updated_at
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 10. VERIFICATION QUERIES
-- ============================================================================

-- Test that we can query profiles without errors
SELECT 'Testing profiles table' as test_name;
SELECT COUNT(*) as profile_count FROM public.profiles;

-- Test that we can query course_catalog without errors
SELECT 'Testing course_catalog table' as test_name;
SELECT COUNT(*) as course_count FROM public.course_catalog WHERE is_active = true;

-- Test that we can query learning_progress without errors
SELECT 'Testing learning_progress table' as test_name;
SELECT COUNT(*) as progress_count FROM public.learning_progress;

-- Test the foreign key relationship
SELECT 'Testing course_catalog and learning_progress relationship' as test_name;

-- ============================================================================
-- 11. FINAL STATUS
-- ============================================================================

SELECT '🎓 LEARNING CENTER SCHEMA FIXES APPLIED' as status;
SELECT 'Created missing profiles table' as fix_1;
SELECT 'Fixed learning_progress foreign key relationships' as fix_2;
SELECT 'Added proper RLS policies for all learning tables' as fix_3;
SELECT 'Added sample course data for testing' as fix_4;
SELECT 'Learning Center should now work without errors' as fix_5;
