-- Fix Learning Queue Policies - Drop existing ones first

-- Drop all existing policies for learning_queues
DROP POLICY IF EXISTS "Users can view own queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can view public queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can view shared queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can create own queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can update own queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can delete own queues" ON learning_queues;

-- Drop all existing policies for learning_queue_items
DROP POLICY IF EXISTS "Users can view items in accessible queues" ON learning_queue_items;
DROP POLICY IF EXISTS "Users can manage items in own queues" ON learning_queue_items;

-- Drop all existing policies for learning_queue_shares
DROP POLICY IF EXISTS "Users can view shares involving them" ON learning_queue_shares;
DROP POLICY IF EXISTS "Users can create shares for own queues" ON learning_queue_shares;

-- Drop all existing policies for learning_queue_templates
DROP POLICY IF EXISTS "Anyone can view active templates" ON learning_queue_templates;

-- Drop all existing policies for learning_queue_template_items
DROP POLICY IF EXISTS "Anyone can view template items" ON learning_queue_template_items;

-- Now recreate all policies
-- Learning queues: Users can manage their own queues and view public ones
CREATE POLICY "Users can view own queues" ON learning_queues
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view public queues" ON learning_queues
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view shared queues" ON learning_queues
  FOR SELECT USING (
    id IN (
      SELECT queue_id FROM learning_queue_shares WHERE shared_with = auth.uid()
    )
  );

CREATE POLICY "Users can create own queues" ON learning_queues
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own queues" ON learning_queues
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own queues" ON learning_queues
  FOR DELETE USING (auth.uid() = user_id);

-- Learning queue items: Follow queue permissions
CREATE POLICY "Users can view items in accessible queues" ON learning_queue_items
  FOR SELECT USING (
    queue_id IN (
      SELECT id FROM learning_queues 
      WHERE user_id = auth.uid() 
         OR is_public = true 
         OR id IN (SELECT queue_id FROM learning_queue_shares WHERE shared_with = auth.uid())
    )
  );

CREATE POLICY "Users can manage items in own queues" ON learning_queue_items
  FOR ALL USING (
    queue_id IN (SELECT id FROM learning_queues WHERE user_id = auth.uid())
  );

-- Queue sharing policies
CREATE POLICY "Users can view shares involving them" ON learning_queue_shares
  FOR SELECT USING (shared_with = auth.uid() OR shared_by = auth.uid());

CREATE POLICY "Users can create shares for own queues" ON learning_queue_shares
  FOR INSERT WITH CHECK (
    shared_by = auth.uid() AND
    queue_id IN (SELECT id FROM learning_queues WHERE user_id = auth.uid())
  );

-- Template policies (public read, admin write)
CREATE POLICY "Anyone can view active templates" ON learning_queue_templates
  FOR SELECT USING (is_active = true);

CREATE POLICY "Anyone can view template items" ON learning_queue_template_items
  FOR SELECT USING (
    template_id IN (
      SELECT id FROM learning_queue_templates WHERE is_active = true
    )
  );
