-- Fix Duplicate Users and Query Issues
-- This migration fixes the 406 and 400 errors

-- ============================================================================
-- 1. FIX DUPLICATE USER RECORDS
-- ============================================================================

-- First, let's see what we have
SELECT 'Checking for duplicate users' as status;

-- Add missing columns to users table first
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT now();
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Remove duplicate user records, keeping only the first one
DELETE FROM public.users
WHERE id IN (
    SELECT id FROM (
        SELECT id, ROW_NUMBER() OVER (PARTITION BY id ORDER BY COALESCE(created_at, now())) as rn
        FROM public.users
    ) t
    WHERE t.rn > 1
);

-- Ensure there's exactly one user record for the authenticated user
INSERT INTO public.users (id, email, display_name, is_premium, bio, social_links, stats, created_at, updated_at)
SELECT 
    id,
    email,
    COALESCE(raw_user_meta_data->>'display_name', email, 'User') as display_name,
    false as is_premium,
    '' as bio,
    '{}' as social_links,
    '{}' as stats,
    created_at,
    updated_at
FROM auth.users
WHERE id NOT IN (SELECT id FROM public.users)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    display_name = COALESCE(EXCLUDED.display_name, users.display_name),
    updated_at = now();

-- ============================================================================
-- 2. ENHANCE POLICIES TO SUPPORT COMPLEX QUERIES
-- ============================================================================

-- Drop the overly restrictive policies and create more flexible ones
DROP POLICY IF EXISTS "projects_select_own" ON public.projects;
DROP POLICY IF EXISTS "contributions_select_own" ON public.contributions;

-- Create more flexible policies that support the frontend queries
CREATE POLICY "projects_select_accessible" ON public.projects
    FOR SELECT USING (
        created_by = auth.uid() OR
        id IN (
            SELECT project_id FROM public.project_contributors 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "contributions_select_accessible" ON public.contributions
    FOR SELECT USING (
        user_id = auth.uid() OR
        project_id IN (
            SELECT id FROM public.projects 
            WHERE created_by = auth.uid()
        )
    );

-- ============================================================================
-- 3. CREATE MISSING INDEXES FOR PERFORMANCE
-- ============================================================================

-- Add indexes to support the complex queries
CREATE INDEX IF NOT EXISTS idx_project_contributors_user_project ON public.project_contributors(user_id, project_id);
CREATE INDEX IF NOT EXISTS idx_contributions_user_project ON public.contributions(user_id, project_id);
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON public.projects(created_by);
CREATE INDEX IF NOT EXISTS idx_projects_updated_at ON public.projects(updated_at);
CREATE INDEX IF NOT EXISTS idx_contributions_updated_at ON public.contributions(updated_at);

-- ============================================================================
-- 4. CREATE FUNCTIONS TO SUPPORT COMPLEX QUERIES
-- ============================================================================

-- Function to get user's accessible projects (for OR queries)
CREATE OR REPLACE FUNCTION get_user_accessible_projects(user_uuid UUID)
RETURNS TABLE(project_id UUID) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT p.id
    FROM public.projects p
    WHERE p.created_by = user_uuid
    
    UNION
    
    SELECT DISTINCT pc.project_id
    FROM public.project_contributors pc
    WHERE pc.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's contributions with project info
CREATE OR REPLACE FUNCTION get_user_contributions_with_projects(user_uuid UUID)
RETURNS TABLE(
    id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    status TEXT,
    contribution_type TEXT,
    hours_worked DECIMAL,
    project_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.created_at,
        c.updated_at,
        c.status,
        c.contribution_type,
        c.hours_worked,
        p.name as project_name
    FROM public.contributions c
    JOIN public.projects p ON c.project_id = p.id
    WHERE c.user_id = user_uuid
    ORDER BY c.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 5. CREATE VIEWS FOR COMMON QUERIES
-- ============================================================================

-- View for user's projects (both owned and contributed to)
CREATE OR REPLACE VIEW user_accessible_projects AS
SELECT DISTINCT
    p.id,
    p.name,
    p.title,
    p.description,
    p.status,
    p.created_by,
    p.team_id,
    p.created_at,
    p.updated_at,
    p.is_active,
    CASE 
        WHEN p.created_by = auth.uid() THEN 'owner'
        ELSE 'contributor'
    END as user_role
FROM public.projects p
WHERE p.created_by = auth.uid()
   OR p.id IN (
       SELECT project_id FROM public.project_contributors 
       WHERE user_id = auth.uid()
   );

-- View for user's contributions with project names
CREATE OR REPLACE VIEW user_contributions_with_projects AS
SELECT 
    c.id,
    c.created_at,
    c.updated_at,
    c.status,
    c.contribution_type,
    c.hours_worked,
    c.user_id,
    c.project_id,
    p.name as project_name
FROM public.contributions c
JOIN public.projects p ON c.project_id = p.id
WHERE c.user_id = auth.uid();

-- ============================================================================
-- 6. GRANT PERMISSIONS ON NEW FUNCTIONS AND VIEWS
-- ============================================================================

-- Grant permissions on functions
GRANT EXECUTE ON FUNCTION get_user_accessible_projects(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_contributions_with_projects(UUID) TO authenticated;

-- Grant permissions on views
GRANT SELECT ON user_accessible_projects TO authenticated;
GRANT SELECT ON user_contributions_with_projects TO authenticated;

-- ============================================================================
-- 7. TEST THE FIXES
-- ============================================================================

-- Test user query (should return exactly one row now)
SELECT 'Testing user query - should return exactly one row' as test_name;
SELECT COUNT(*) as user_count FROM public.users WHERE id = auth.uid();

-- Test projects query (should work with new policies)
SELECT 'Testing projects query - should work now' as test_name;
SELECT COUNT(*) as project_count FROM public.projects WHERE created_by = auth.uid();

-- Test contributions query (should work with new policies)
SELECT 'Testing contributions query - should work now' as test_name;
SELECT COUNT(*) as contribution_count FROM public.contributions WHERE user_id = auth.uid();

-- Test the new view
SELECT 'Testing user accessible projects view' as test_name;
SELECT COUNT(*) as accessible_project_count FROM user_accessible_projects;

-- ============================================================================
-- 8. FINAL STATUS
-- ============================================================================

SELECT '🎯 DUPLICATE USERS AND QUERY FIXES APPLIED' as status;
SELECT 'Duplicate user records removed' as fix_1;
SELECT 'Enhanced RLS policies for complex queries' as fix_2;
SELECT 'Added performance indexes' as fix_3;
SELECT 'Created helper functions and views' as fix_4;
SELECT 'Users table 406 errors should be fixed' as fix_5;
SELECT 'Projects table 400 errors should be fixed' as fix_6;
