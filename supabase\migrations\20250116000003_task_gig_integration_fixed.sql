-- Task-Gig Integration System (Fixed)
-- Allows tasks to be converted to gigs and vice versa
-- This version checks for existing tables and uses correct table names

-- Check if collaboration_request_applications table exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'collaboration_request_applications') THEN
        CREATE TABLE collaboration_request_applications (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            request_id UUID NOT NULL REFERENCES collaboration_requests(id) ON DELETE CASCADE,
            applicant_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),
            application_message TEXT,
            proposed_rate DECIMAL(10,2),
            proposed_timeline_days INTEGER,
            portfolio_links TEXT[],
            relevant_experience TEXT,
            applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            reviewed_at TIMESTAMP WITH TIME ZONE,
            reviewed_by UUID REFERENCES auth.users(id),
            review_notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(request_id, applicant_id)
        );
        
        -- Add indexes
        CREATE INDEX idx_collaboration_request_applications_request ON collaboration_request_applications(request_id);
        CREATE INDEX idx_collaboration_request_applications_applicant ON collaboration_request_applications(applicant_id);
        CREATE INDEX idx_collaboration_request_applications_status ON collaboration_request_applications(status);
        
        -- Enable RLS
        ALTER TABLE collaboration_request_applications ENABLE ROW LEVEL SECURITY;
        
        -- RLS Policies
        CREATE POLICY "Users can view applications for their requests" ON collaboration_request_applications
          FOR SELECT USING (
            request_id IN (SELECT id FROM collaboration_requests WHERE requested_by = auth.uid())
          );
          
        CREATE POLICY "Users can view their own applications" ON collaboration_request_applications
          FOR SELECT USING (applicant_id = auth.uid());
          
        CREATE POLICY "Users can create applications" ON collaboration_request_applications
          FOR INSERT WITH CHECK (applicant_id = auth.uid());
          
        CREATE POLICY "Users can update their own applications" ON collaboration_request_applications
          FOR UPDATE USING (applicant_id = auth.uid());
    END IF;
END
$$;

-- Task-Gig relationship tracking
CREATE TABLE IF NOT EXISTS task_gig_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationship details
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
  gig_id UUID REFERENCES collaboration_requests(id) ON DELETE CASCADE,
  relationship_type TEXT NOT NULL CHECK (relationship_type IN ('task_to_gig', 'gig_to_task', 'bidirectional')),
  
  -- Conversion metadata
  converted_by UUID NOT NULL REFERENCES auth.users(id),
  conversion_reason TEXT,
  original_type TEXT NOT NULL CHECK (original_type IN ('task', 'gig')),
  
  -- Sync settings
  auto_sync_status BOOLEAN DEFAULT true,
  auto_sync_assignee BOOLEAN DEFAULT true,
  auto_sync_deadline BOOLEAN DEFAULT true,
  
  -- Status tracking
  is_active BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique relationships
  UNIQUE(task_id, gig_id)
);

-- Gig application to task assignment tracking
CREATE TABLE IF NOT EXISTS gig_task_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Assignment details
  gig_id UUID NOT NULL REFERENCES collaboration_requests(id) ON DELETE CASCADE,
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  application_id UUID REFERENCES collaboration_request_applications(id) ON DELETE SET NULL,
  
  -- Assignment metadata
  assigned_user_id UUID NOT NULL REFERENCES auth.users(id),
  assigned_by UUID NOT NULL REFERENCES auth.users(id),
  assignment_type TEXT DEFAULT 'gig_application' CHECK (assignment_type IN ('gig_application', 'direct_assignment', 'auto_assignment')),
  
  -- Terms and conditions
  agreed_rate DECIMAL(10,2),
  agreed_deadline TIMESTAMP WITH TIME ZONE,
  agreed_deliverables TEXT[],
  
  -- Status tracking
  status TEXT DEFAULT 'assigned' CHECK (status IN ('assigned', 'in_progress', 'completed', 'cancelled')),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Task conversion templates for common gig types
CREATE TABLE IF NOT EXISTS task_gig_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Template details
  name TEXT NOT NULL,
  description TEXT,
  category TEXT, -- 'development', 'design', 'content', 'marketing', etc.
  
  -- Default gig settings
  default_budget_min DECIMAL(10,2),
  default_budget_max DECIMAL(10,2),
  default_timeline_days INTEGER,
  default_skill_requirements TEXT[],
  
  -- Template configuration
  required_fields TEXT[], -- Fields that must be filled when using template
  optional_fields TEXT[], -- Fields that can be customized
  
  -- Usage tracking
  usage_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add columns to existing tables if they don't exist
DO $$
BEGIN
    -- Add columns to collaboration_requests
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'source_task_id') THEN
        ALTER TABLE collaboration_requests ADD COLUMN source_task_id UUID REFERENCES tasks(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'auto_assign_on_acceptance') THEN
        ALTER TABLE collaboration_requests ADD COLUMN auto_assign_on_acceptance BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'task_integration_settings') THEN
        ALTER TABLE collaboration_requests ADD COLUMN task_integration_settings JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Add columns to tasks
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'source_gig_id') THEN
        ALTER TABLE tasks ADD COLUMN source_gig_id UUID REFERENCES collaboration_requests(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'is_gig_derived') THEN
        ALTER TABLE tasks ADD COLUMN is_gig_derived BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'gig_integration_settings') THEN
        ALTER TABLE tasks ADD COLUMN gig_integration_settings JSONB DEFAULT '{}'::jsonb;
    END IF;
END
$$;

-- Create indexes if they don't exist
DO $$
BEGIN
    -- Task-gig relationships indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_task_gig_relationships_task_id') THEN
        CREATE INDEX idx_task_gig_relationships_task_id ON task_gig_relationships(task_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_task_gig_relationships_gig_id') THEN
        CREATE INDEX idx_task_gig_relationships_gig_id ON task_gig_relationships(gig_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_task_gig_relationships_active') THEN
        CREATE INDEX idx_task_gig_relationships_active ON task_gig_relationships(is_active) WHERE is_active = true;
    END IF;
    
    -- Gig task assignments indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_gig_task_assignments_gig_id') THEN
        CREATE INDEX idx_gig_task_assignments_gig_id ON gig_task_assignments(gig_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_gig_task_assignments_task_id') THEN
        CREATE INDEX idx_gig_task_assignments_task_id ON gig_task_assignments(task_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_gig_task_assignments_user') THEN
        CREATE INDEX idx_gig_task_assignments_user ON gig_task_assignments(assigned_user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_gig_task_assignments_status') THEN
        CREATE INDEX idx_gig_task_assignments_status ON gig_task_assignments(status);
    END IF;
    
    -- Templates indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_task_gig_templates_category') THEN
        CREATE INDEX idx_task_gig_templates_category ON task_gig_templates(category);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_task_gig_templates_active') THEN
        CREATE INDEX idx_task_gig_templates_active ON task_gig_templates(is_active) WHERE is_active = true;
    END IF;
    
    -- New column indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_collaboration_requests_source_task') THEN
        CREATE INDEX idx_collaboration_requests_source_task ON collaboration_requests(source_task_id) WHERE source_task_id IS NOT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tasks_source_gig') THEN
        CREATE INDEX idx_tasks_source_gig ON tasks(source_gig_id) WHERE source_gig_id IS NOT NULL;
    END IF;
END
$$;

-- Function to convert task to gig (create or replace)
CREATE OR REPLACE FUNCTION convert_task_to_gig(
  p_task_id UUID,
  p_user_id UUID,
  p_budget_min DECIMAL DEFAULT NULL,
  p_budget_max DECIMAL DEFAULT NULL,
  p_timeline_days INTEGER DEFAULT NULL,
  p_skill_requirements TEXT[] DEFAULT NULL,
  p_conversion_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_task_record RECORD;
  v_gig_id UUID;
  v_relationship_id UUID;
BEGIN
  -- Get task details
  SELECT * INTO v_task_record
  FROM tasks
  WHERE id = p_task_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Task not found: %', p_task_id;
  END IF;
  
  -- Create collaboration request (gig)
  INSERT INTO collaboration_requests (
    title,
    description,
    project_id,
    requester_id,
    request_type,
    budget_min,
    budget_max,
    timeline_days,
    skill_requirements,
    status,
    source_task_id,
    auto_assign_on_acceptance,
    task_integration_settings
  ) VALUES (
    v_task_record.title,
    COALESCE(v_task_record.description, 'Converted from task'),
    v_task_record.project_id,
    p_user_id,
    'task_assistance',
    p_budget_min,
    p_budget_max,
    p_timeline_days,
    p_skill_requirements,
    'open',
    p_task_id,
    true,
    jsonb_build_object(
      'original_task_id', p_task_id,
      'auto_sync_enabled', true,
      'conversion_date', NOW()
    )
  ) RETURNING id INTO v_gig_id;
  
  -- Create relationship record
  INSERT INTO task_gig_relationships (
    task_id,
    gig_id,
    relationship_type,
    converted_by,
    conversion_reason,
    original_type
  ) VALUES (
    p_task_id,
    v_gig_id,
    'task_to_gig',
    p_user_id,
    p_conversion_reason,
    'task'
  ) RETURNING id INTO v_relationship_id;
  
  -- Update task to reference gig
  UPDATE tasks
  SET 
    source_gig_id = v_gig_id,
    is_gig_derived = false, -- This is the original task
    gig_integration_settings = jsonb_build_object(
      'converted_to_gig', true,
      'gig_id', v_gig_id,
      'relationship_id', v_relationship_id
    )
  WHERE id = p_task_id;
  
  RETURN v_gig_id;
END;
$$ LANGUAGE plpgsql;

-- Enable RLS on new tables
ALTER TABLE task_gig_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE gig_task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_gig_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies for new tables
DO $$
BEGIN
    -- Task-gig relationships policies
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Users can view task-gig relationships for their content' AND tablename = 'task_gig_relationships') THEN
        CREATE POLICY "Users can view task-gig relationships for their content" ON task_gig_relationships
          FOR SELECT USING (
            task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid()) OR
            gig_id IN (SELECT id FROM collaboration_requests WHERE requested_by = auth.uid())
          );
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Users can create task-gig relationships for their content' AND tablename = 'task_gig_relationships') THEN
        CREATE POLICY "Users can create task-gig relationships for their content" ON task_gig_relationships
          FOR INSERT WITH CHECK (
            converted_by = auth.uid() AND (
              task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid()) OR
              gig_id IN (SELECT id FROM collaboration_requests WHERE requested_by = auth.uid())
            )
          );
    END IF;
    
    -- Gig-task assignments policies
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Users can view relevant gig-task assignments' AND tablename = 'gig_task_assignments') THEN
        CREATE POLICY "Users can view relevant gig-task assignments" ON gig_task_assignments
          FOR SELECT USING (
            assigned_user_id = auth.uid() OR
            assigned_by = auth.uid() OR
            gig_id IN (SELECT id FROM collaboration_requests WHERE requested_by = auth.uid()) OR
            task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid())
          );
    END IF;
    
    -- Templates policies
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Anyone can view active templates' AND tablename = 'task_gig_templates') THEN
        CREATE POLICY "Anyone can view active templates" ON task_gig_templates
          FOR SELECT USING (is_active = true);
    END IF;
END
$$;

-- Comments
COMMENT ON TABLE task_gig_relationships IS 'Tracks relationships between tasks and gigs for bidirectional integration';
COMMENT ON TABLE gig_task_assignments IS 'Manages assignments when gig applications are accepted';
COMMENT ON TABLE task_gig_templates IS 'Templates for converting tasks to gigs with predefined settings';
COMMENT ON FUNCTION convert_task_to_gig IS 'Converts a task into a gig with proper relationship tracking';
