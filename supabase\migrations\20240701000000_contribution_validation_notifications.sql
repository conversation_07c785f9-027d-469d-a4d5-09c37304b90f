-- Create triggers for contribution validations
CREATE OR R<PERSON>LACE FUNCTION handle_contribution_validation_notification() RETURNS TRIGGER AS $$
BEGIN
  -- Only create notification if validation status changed
  IF (TG_OP = 'UPDATE' AND NEW.validation_status != OLD.validation_status) THEN
    -- Get the contributor to notify them
    DECLARE
      contributor_id UUID := NEW.user_id;
      validator_id UUID;
      validator_name TEXT;
      project_name TEXT;
      feedback TEXT := NULL;
    BEGIN
      -- Get the validator info from the validation history
      SELECT 
        cv.validator_id, 
        u.display_name,
        cv.feedback
      INTO 
        validator_id, 
        validator_name,
        feedback
      FROM 
        contribution_validations cv
        JOIN users u ON cv.validator_id = u.id
      WHERE 
        cv.contribution_id = NEW.id
      ORDER BY 
        cv.created_at DESC
      LIMIT 1;
      
      -- If no validator found in history, use the current user
      IF validator_id IS NULL THEN
        validator_id := auth.uid();
        
        -- Get validator name
        SELECT display_name INTO validator_name
        FROM users
        WHERE id = validator_id;
      END IF;
      
      -- Skip if the validator is the same as the contributor
      IF validator_id = contributor_id THEN
        RETURN NEW;
      END IF;
      
      -- Get project name
      SELECT name INTO project_name
      FROM projects
      WHERE id = NEW.project_id;
      
      -- Create appropriate notification based on status
      CASE NEW.validation_status
        WHEN 'approved' THEN
          PERFORM create_notification(
            contributor_id,
            'contribution_approved',
            'Contribution Approved',
            'Your contribution to ' || COALESCE(project_name, 'a project') || ' has been approved',
            '/contribution/' || NEW.id || '/validate',
            NEW.id,
            jsonb_build_object(
              'contribution_id', NEW.id,
              'project_id', NEW.project_id,
              'project_name', project_name,
              'validator_id', validator_id,
              'validator_name', validator_name,
              'feedback', feedback
            )
          );
        WHEN 'rejected' THEN
          PERFORM create_notification(
            contributor_id,
            'contribution_rejected',
            'Contribution Rejected',
            'Your contribution to ' || COALESCE(project_name, 'a project') || ' has been rejected',
            '/contribution/' || NEW.id || '/validate',
            NEW.id,
            jsonb_build_object(
              'contribution_id', NEW.id,
              'project_id', NEW.project_id,
              'project_name', project_name,
              'validator_id', validator_id,
              'validator_name', validator_name,
              'feedback', feedback
            )
          );
        WHEN 'pending_changes' THEN
          PERFORM create_notification(
            contributor_id,
            'contribution_changes_requested',
            'Changes Requested',
            'Changes have been requested for your contribution to ' || COALESCE(project_name, 'a project'),
            '/contribution/' || NEW.id || '/validate',
            NEW.id,
            jsonb_build_object(
              'contribution_id', NEW.id,
              'project_id', NEW.project_id,
              'project_name', project_name,
              'validator_id', validator_id,
              'validator_name', validator_name,
              'feedback', feedback
            )
          );
        ELSE
          -- For any other status changes
          PERFORM create_notification(
            contributor_id,
            'contribution_status_update',
            'Contribution Status Updated',
            'Your contribution to ' || COALESCE(project_name, 'a project') || ' has been updated',
            '/contribution/' || NEW.id || '/validate',
            NEW.id,
            jsonb_build_object(
              'contribution_id', NEW.id,
              'project_id', NEW.project_id,
              'project_name', project_name,
              'validator_id', validator_id,
              'validator_name', validator_name,
              'status', NEW.validation_status
            )
          );
      END CASE;
    END;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for contribution validations (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'contributions') THEN
        DROP TRIGGER IF EXISTS contribution_validation_notification_trigger ON public.contributions;
        CREATE TRIGGER contribution_validation_notification_trigger
        AFTER UPDATE ON public.contributions
        FOR EACH ROW
        EXECUTE FUNCTION handle_contribution_validation_notification();
    END IF;
END $$;
