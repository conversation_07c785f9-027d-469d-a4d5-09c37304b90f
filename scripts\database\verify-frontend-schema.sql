-- Verify Frontend Schema - Check all required tables and columns exist
-- This script verifies that all the tables and columns needed by the frontend are present

-- ============================================================================
-- 1. CHECK CRITICAL TABLES EXIST
-- ============================================================================

SELECT 'CHECKING CRITICAL TABLES' as status;

SELECT 
    table_name,
    CASE 
        WHEN table_name IN (
            'users', 'projects', 'tasks', 'teams', 'team_members', 'project_contributors',
            'active_timers', 'contribution_tracking_config', 'contributions', 'team_invitations'
        ) THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'users', 'projects', 'tasks', 'teams', 'team_members', 'project_contributors',
    'active_timers', 'contribution_tracking_config', 'contributions', 'team_invitations'
)
ORDER BY table_name;

-- ============================================================================
-- 2. CHECK USERS TABLE COLUMNS
-- ============================================================================

SELECT 'CHECKING USERS TABLE COLUMNS' as status;

SELECT 
    column_name,
    data_type,
    CASE 
        WHEN column_name IN ('id', 'email', 'display_name', 'avatar_url', 'is_premium', 'bio', 'social_links', 'stats') 
        THEN '✅ EXISTS'
        ELSE '⚠️ EXTRA'
    END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY column_name;

-- ============================================================================
-- 3. CHECK PROJECTS TABLE COLUMNS
-- ============================================================================

SELECT 'CHECKING PROJECTS TABLE COLUMNS' as status;

SELECT 
    column_name,
    data_type,
    CASE 
        WHEN column_name IN ('id', 'name', 'title', 'description', 'status', 'created_by', 'team_id', 'created_at', 'is_active', 'total_revenue', 'revenue_distribution_model') 
        THEN '✅ EXISTS'
        ELSE '⚠️ EXTRA'
    END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'projects'
AND column_name IN ('id', 'name', 'title', 'description', 'status', 'created_by', 'team_id', 'created_at', 'is_active', 'total_revenue', 'revenue_distribution_model')
ORDER BY column_name;

-- ============================================================================
-- 4. CHECK PROJECT_CONTRIBUTORS TABLE COLUMNS
-- ============================================================================

SELECT 'CHECKING PROJECT_CONTRIBUTORS TABLE COLUMNS' as status;

SELECT 
    column_name,
    data_type,
    CASE 
        WHEN column_name IN ('id', 'project_id', 'user_id', 'role', 'status', 'permission_level', 'is_admin') 
        THEN '✅ EXISTS'
        ELSE '⚠️ EXTRA'
    END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'project_contributors'
AND column_name IN ('id', 'project_id', 'user_id', 'role', 'status', 'permission_level', 'is_admin')
ORDER BY column_name;

-- ============================================================================
-- 5. CHECK TEAM_MEMBERS TABLE COLUMNS
-- ============================================================================

SELECT 'CHECKING TEAM_MEMBERS TABLE COLUMNS' as status;

SELECT 
    column_name,
    data_type,
    CASE 
        WHEN column_name IN ('id', 'team_id', 'user_id', 'role', 'status') 
        THEN '✅ EXISTS'
        ELSE '⚠️ EXTRA'
    END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'team_members'
AND column_name IN ('id', 'team_id', 'user_id', 'role', 'status')
ORDER BY column_name;

-- ============================================================================
-- 6. CHECK ACTIVE_TIMERS TABLE COLUMNS
-- ============================================================================

SELECT 'CHECKING ACTIVE_TIMERS TABLE COLUMNS' as status;

SELECT 
    column_name,
    data_type,
    CASE 
        WHEN column_name IN ('id', 'user_id', 'task_id', 'project_id', 'is_active', 'started_at', 'total_seconds') 
        THEN '✅ EXISTS'
        ELSE '⚠️ EXTRA'
    END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'active_timers'
AND column_name IN ('id', 'user_id', 'task_id', 'project_id', 'is_active', 'started_at', 'total_seconds')
ORDER BY column_name;

-- ============================================================================
-- 7. CHECK CONTRIBUTIONS TABLE COLUMNS
-- ============================================================================

SELECT 'CHECKING CONTRIBUTIONS TABLE COLUMNS' as status;

SELECT 
    column_name,
    data_type,
    CASE 
        WHEN column_name IN ('id', 'user_id', 'project_id', 'contribution_type', 'hours_worked', 'status') 
        THEN '✅ EXISTS'
        ELSE '⚠️ EXTRA'
    END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'contributions'
AND column_name IN ('id', 'user_id', 'project_id', 'contribution_type', 'hours_worked', 'status')
ORDER BY column_name;

-- ============================================================================
-- 8. CHECK TEAM_INVITATIONS TABLE COLUMNS
-- ============================================================================

SELECT 'CHECKING TEAM_INVITATIONS TABLE COLUMNS' as status;

SELECT 
    column_name,
    data_type,
    CASE 
        WHEN column_name IN ('id', 'team_id', 'invited_email', 'invited_user_id', 'role', 'status', 'invited_by', 'project_id', 'message') 
        THEN '✅ EXISTS'
        ELSE '⚠️ EXTRA'
    END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'team_invitations'
AND column_name IN ('id', 'team_id', 'invited_email', 'invited_user_id', 'role', 'status', 'invited_by', 'project_id', 'message')
ORDER BY column_name;

-- ============================================================================
-- 9. CHECK ROW LEVEL SECURITY IS ENABLED
-- ============================================================================

SELECT 'CHECKING ROW LEVEL SECURITY STATUS' as status;

SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN (
    'users', 'projects', 'tasks', 'teams', 'team_members', 'project_contributors',
    'active_timers', 'contribution_tracking_config', 'contributions', 'team_invitations'
)
ORDER BY tablename;

-- ============================================================================
-- 10. CHECK POLICIES EXIST
-- ============================================================================

SELECT 'CHECKING RLS POLICIES' as status;

SELECT 
    tablename,
    policyname,
    cmd as policy_type
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN (
    'active_timers', 'contribution_tracking_config', 'contributions', 'team_invitations'
)
ORDER BY tablename, policyname;

-- ============================================================================
-- 11. SUMMARY REPORT
-- ============================================================================

SELECT 'SCHEMA VERIFICATION SUMMARY' as status;

-- Count tables
WITH table_counts AS (
    SELECT COUNT(*) as existing_tables
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name IN (
        'users', 'projects', 'tasks', 'teams', 'team_members', 'project_contributors',
        'active_timers', 'contribution_tracking_config', 'contributions', 'team_invitations'
    )
)
SELECT 
    existing_tables || '/10 critical tables exist' as table_status,
    CASE 
        WHEN existing_tables = 10 THEN '✅ ALL TABLES PRESENT'
        ELSE '❌ MISSING TABLES'
    END as overall_status
FROM table_counts;

-- Check for any missing critical columns
SELECT 'CRITICAL MISSING COLUMNS CHECK' as status;

-- This will show any tables that are missing critical columns
SELECT 
    'users table' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_premium')
        THEN '✅ has is_premium'
        ELSE '❌ missing is_premium'
    END as column_status
UNION ALL
SELECT 
    'projects table' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'is_active')
        THEN '✅ has is_active'
        ELSE '❌ missing is_active'
    END as column_status
UNION ALL
SELECT 
    'project_contributors table' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'project_contributors' AND column_name = 'is_admin')
        THEN '✅ has is_admin'
        ELSE '❌ missing is_admin'
    END as column_status
UNION ALL
SELECT 
    'team_members table' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'status')
        THEN '✅ has status'
        ELSE '❌ missing status'
    END as column_status;

SELECT '🎉 SCHEMA VERIFICATION COMPLETE' as final_status;
