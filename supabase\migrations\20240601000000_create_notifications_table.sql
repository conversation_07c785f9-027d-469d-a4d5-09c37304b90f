-- Create notifications table for centralized notification management
DO $$
DECLARE
  notifications_exists B<PERSON><PERSON>EAN;
BEGIN
  -- Check if table exists
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'notifications'
  ) INTO notifications_exists;
  
  -- Create notifications table if it doesn't exist
  IF NOT notifications_exists THEN
    CREATE TABLE public.notifications (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      type TEXT NOT NULL, -- 'friend_request', 'project_invitation', 'agreement', etc.
      title TEXT NOT NULL,
      message TEXT,
      action_url TEXT,
      related_id UUID, -- ID of the related entity (friend_request_id, project_invitation_id, etc.)
      metadata JSONB, -- Additional data specific to notification type
      is_read BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
    );
    
    -- Add indexes
    CREATE INDEX notifications_user_id_idx ON public.notifications(user_id);
    CREATE INDEX notifications_type_idx ON public.notifications(type);
    CREATE INDEX notifications_is_read_idx ON public.notifications(is_read);
    CREATE INDEX notifications_created_at_idx ON public.notifications(created_at);
    
    -- Enable RLS
    ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
    
    -- Create RLS policies
    CREATE POLICY "Users can view their own notifications"
      ON public.notifications
      FOR SELECT
      TO authenticated
      USING (auth.uid() = user_id);
      
    CREATE POLICY "Users can update their own notifications"
      ON public.notifications
      FOR UPDATE
      TO authenticated
      USING (auth.uid() = user_id);
    
    RAISE NOTICE 'Created notifications table with RLS policies';
  END IF;
END $$;

-- Create function to create notifications
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_type TEXT,
  p_title TEXT,
  p_message TEXT,
  p_action_url TEXT,
  p_related_id UUID,
  p_metadata JSONB
) RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO public.notifications (
    user_id,
    type,
    title,
    message,
    action_url,
    related_id,
    metadata
  ) VALUES (
    p_user_id,
    p_type,
    p_title,
    p_message,
    p_action_url,
    p_related_id,
    p_metadata
  ) RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for friend requests
CREATE OR REPLACE FUNCTION handle_friend_request_notification() RETURNS TRIGGER AS $$
BEGIN
  -- For new friend requests
  IF (TG_OP = 'INSERT') THEN
    -- If recipient has a user account
    IF NEW.recipient_id IS NOT NULL THEN
      PERFORM create_notification(
        NEW.recipient_id,
        'friend_request',
        'New Friend Request',
        'You have received a friend request from ' || (SELECT display_name FROM public.users WHERE id = NEW.sender_id),
        '/notifications/friends',
        NEW.id,
        jsonb_build_object(
          'sender_id', NEW.sender_id,
          'sender_name', (SELECT display_name FROM public.users WHERE id = NEW.sender_id),
          'message', NEW.message
        )
      );
    END IF;
  -- For updated friend requests
  ELSIF (TG_OP = 'UPDATE') THEN
    -- If status changed to accepted
    IF NEW.status = 'accepted' AND OLD.status = 'pending' THEN
      -- Notify the sender
      PERFORM create_notification(
        NEW.sender_id,
        'friend_request_accepted',
        'Friend Request Accepted',
        (SELECT display_name FROM public.users WHERE id = NEW.recipient_id) || ' accepted your friend request',
        '/notifications/friends',
        NEW.id,
        jsonb_build_object(
          'recipient_id', NEW.recipient_id,
          'recipient_name', (SELECT display_name FROM public.users WHERE id = NEW.recipient_id)
        )
      );
    -- If status changed to rejected
    ELSIF NEW.status = 'rejected' AND OLD.status = 'pending' THEN
      -- Notify the sender
      PERFORM create_notification(
        NEW.sender_id,
        'friend_request_rejected',
        'Friend Request Declined',
        'Your friend request was declined',
        '/notifications/friends',
        NEW.id,
        jsonb_build_object(
          'recipient_id', NEW.recipient_id
        )
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for friend requests (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'friend_requests') THEN
        DROP TRIGGER IF EXISTS friend_request_notification_trigger ON public.friend_requests;
        CREATE TRIGGER friend_request_notification_trigger
        AFTER INSERT OR UPDATE ON public.friend_requests
        FOR EACH ROW
        EXECUTE FUNCTION handle_friend_request_notification();
    END IF;
END $$;

-- Create triggers for project invitations
CREATE OR REPLACE FUNCTION handle_project_invitation_notification() RETURNS TRIGGER AS $$
BEGIN
  -- For new project invitations
  IF (TG_OP = 'INSERT') THEN
    -- If recipient has a user account
    IF NEW.invited_user_id IS NOT NULL THEN
      PERFORM create_notification(
        NEW.invited_user_id,
        'project_invitation',
        'New Project Invitation',
        'You have been invited to join a project by ' || (SELECT display_name FROM public.users WHERE id = NEW.invited_by),
        '/notifications/projects',
        NEW.id,
        jsonb_build_object(
          'project_id', NEW.project_id,
          'project_name', (SELECT name FROM public.projects WHERE id = NEW.project_id),
          'inviter_id', NEW.invited_by,
          'inviter_name', (SELECT display_name FROM public.users WHERE id = NEW.invited_by),
          'message', NEW.message
        )
      );
    END IF;
  -- For updated project invitations
  ELSIF (TG_OP = 'UPDATE') THEN
    -- If status changed to accepted
    IF NEW.status = 'accepted' AND OLD.status = 'pending' THEN
      -- Notify the inviter
      PERFORM create_notification(
        NEW.invited_by,
        'project_invitation_accepted',
        'Project Invitation Accepted',
        (SELECT display_name FROM public.users WHERE id = NEW.invited_user_id) || ' accepted your project invitation',
        '/project/' || NEW.project_id || '/team',
        NEW.id,
        jsonb_build_object(
          'project_id', NEW.project_id,
          'project_name', (SELECT name FROM public.projects WHERE id = NEW.project_id),
          'invitee_id', NEW.invited_user_id,
          'invitee_name', (SELECT display_name FROM public.users WHERE id = NEW.invited_user_id)
        )
      );
    -- If status changed to rejected
    ELSIF NEW.status = 'rejected' AND OLD.status = 'pending' THEN
      -- Notify the inviter
      PERFORM create_notification(
        NEW.invited_by,
        'project_invitation_rejected',
        'Project Invitation Declined',
        'Your project invitation was declined',
        '/project/' || NEW.project_id || '/team',
        NEW.id,
        jsonb_build_object(
          'project_id', NEW.project_id,
          'project_name', (SELECT name FROM public.projects WHERE id = NEW.project_id),
          'invitee_id', NEW.invited_user_id
        )
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for project invitations (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'project_invitations') THEN
        DROP TRIGGER IF EXISTS project_invitation_notification_trigger ON public.project_invitations;
        CREATE TRIGGER project_invitation_notification_trigger
        AFTER INSERT OR UPDATE ON public.project_invitations
        FOR EACH ROW
        EXECUTE FUNCTION handle_project_invitation_notification();
    END IF;
END $$;
