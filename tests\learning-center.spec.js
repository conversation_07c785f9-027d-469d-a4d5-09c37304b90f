import { test, expect } from '@playwright/test';

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Learning Center', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    
    // Login with test credentials
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    
    // Wait for successful login (content changes but URL stays the same)
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
    
    // Navigate to learning center
    await page.goto('/learn');
    await page.waitForLoadState('networkidle');
  });

  test('should display learning center with main sections', async ({ page }) => {
    // Check main header
    await expect(page.locator('h1')).toContainText('Learning Center');
    
    // Check search functionality
    await expect(page.locator('input[placeholder*="Search"]')).toBeVisible();
    
    // Check action buttons
    await expect(page.locator('button:has-text("Submit Video")')).toBeVisible();
    await expect(page.locator('button:has-text("My Queues")')).toBeVisible();
    
    // Check main tabs
    await expect(page.locator('[role="tab"]:has-text("Discover")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Learning Paths")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Community")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("My Progress")')).toBeVisible();
  });

  test('should display smart suggestions', async ({ page }) => {
    // Check if smart suggestions section is visible
    await expect(page.locator('h2:has-text("Smart Suggestions")')).toBeVisible();
    
    // Check suggestion categories
    const suggestionTabs = page.locator('[role="tab"]').filter({ hasText: /For You|Skill Gaps|Project Based|Trending|Career Path/ });
    await expect(suggestionTabs.first()).toBeVisible();
    
    // Test switching between suggestion categories
    if (await page.locator('[role="tab"]:has-text("Trending")').isVisible()) {
      await page.click('[role="tab"]:has-text("Trending")');
      await page.waitForTimeout(1000); // Wait for content to load
    }
  });

  test('should handle video search and filtering', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="Search"]');
    
    // Test search functionality
    await searchInput.fill('JavaScript');
    await page.waitForTimeout(1000); // Wait for search results
    
    // Check if search results are displayed or no results message
    const hasResults = await page.locator('[data-testid="video-card"]').count() > 0;
    const hasNoResults = await page.locator('text=No content found').isVisible();
    
    expect(hasResults || hasNoResults).toBeTruthy();
    
    // Test category filter
    const categorySelect = page.locator('select').first();
    if (await categorySelect.isVisible()) {
      await categorySelect.selectOption({ index: 1 }); // Select first non-default option
      await page.waitForTimeout(1000);
    }
    
    // Test difficulty filter
    const difficultySelect = page.locator('select').nth(1);
    if (await difficultySelect.isVisible()) {
      await difficultySelect.selectOption('beginner');
      await page.waitForTimeout(1000);
    }
  });

  test('should open video submission form', async ({ page }) => {
    // Click submit video button
    await page.click('button:has-text("Submit Video")');
    
    // Check if modal opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('h3:has-text("Submit Video")')).toBeVisible();
    
    // Check form fields
    await expect(page.locator('input[label*="YouTube Video URL"]')).toBeVisible();
    await expect(page.locator('input[label*="Title"]')).toBeVisible();
    await expect(page.locator('textarea[label*="Description"]')).toBeVisible();
    
    // Test form validation
    await page.click('button:has-text("Next")');
    // Should show validation errors or stay on same step
    
    // Close modal
    await page.click('button:has-text("Cancel")');
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('should open learning queues modal', async ({ page }) => {
    // Click my queues button
    await page.click('button:has-text("My Queues")');
    
    // Check if modal opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('h2:has-text("Learning Queues")')).toBeVisible();
    
    // Check queue stats cards
    await expect(page.locator('text=My Queues')).toBeVisible();
    await expect(page.locator('text=Total Items')).toBeVisible();
    await expect(page.locator('text=Completed')).toBeVisible();
    
    // Check tabs
    await expect(page.locator('[role="tab"]:has-text("My Queues")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Shared Queues")')).toBeVisible();
    
    // Test creating new queue
    if (await page.locator('button:has-text("New Queue")').isVisible()) {
      await page.click('button:has-text("New Queue")');
      await expect(page.locator('input[label*="Name"]')).toBeVisible();
      await page.click('button:has-text("Cancel")');
    }
    
    // Close modal
    await page.click('button:has-text("Close")');
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('should navigate between tabs', async ({ page }) => {
    // Test Learning Paths tab
    await page.click('[role="tab"]:has-text("Learning Paths")');
    await page.waitForTimeout(1000);
    await expect(page.locator('h2:has-text("Official Learning Paths")')).toBeVisible();
    
    // Test Community tab
    await page.click('[role="tab"]:has-text("Community")');
    await page.waitForTimeout(1000);
    await expect(page.locator('h2:has-text("Community Recommendations")')).toBeVisible();
    
    // Test My Progress tab
    await page.click('[role="tab"]:has-text("My Progress")');
    await page.waitForTimeout(1000);
    await expect(page.locator('h2:has-text("Learning Progress")')).toBeVisible();
    
    // Check progress overview cards
    await expect(page.locator('text=Completed Videos')).toBeVisible();
    await expect(page.locator('text=In Progress')).toBeVisible();
    await expect(page.locator('text=Average Progress')).toBeVisible();
    
    // Return to Discover tab
    await page.click('[role="tab"]:has-text("Discover")');
    await page.waitForTimeout(1000);
  });

  test('should handle video interactions', async ({ page }) => {
    // Look for video cards
    const videoCards = page.locator('[data-testid="video-card"]');
    const videoCount = await videoCards.count();
    
    if (videoCount > 0) {
      const firstVideo = videoCards.first();
      
      // Test video hover effects
      await firstVideo.hover();
      
      // Test enrollment button
      const enrollButton = firstVideo.locator('button:has-text("Enroll")');
      if (await enrollButton.isVisible()) {
        await enrollButton.click();
        // Should show success message or enrollment confirmation
        await page.waitForTimeout(1000);
      }
      
      // Test share button
      const shareButton = firstVideo.locator('button[aria-label*="Share"]');
      if (await shareButton.isVisible()) {
        await shareButton.click();
        // Should open share modal
        await page.waitForTimeout(1000);
        
        // Close share modal if opened
        if (await page.locator('[role="dialog"]:has-text("Share")').isVisible()) {
          await page.click('button:has-text("Cancel")');
        }
      }
      
      // Test external link
      const externalButton = firstVideo.locator('button[aria-label*="External"]');
      if (await externalButton.isVisible()) {
        // Don't actually click external links in tests
        await expect(externalButton).toBeVisible();
      }
    } else {
      // If no videos, check for empty state
      const emptyState = page.locator('text=No content found');
      await expect(emptyState).toBeVisible();
    }
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Test network error handling by intercepting requests
    await page.route('**/course_catalog*', route => {
      route.abort('failed');
    });
    
    // Reload page to trigger error
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Should show error message or fallback content
    const hasErrorMessage = await page.locator('text=Failed to load').isVisible();
    const hasEmptyState = await page.locator('text=No content found').isVisible();
    const hasSpinner = await page.locator('[data-testid="spinner"]').isVisible();
    
    // At least one of these should be true
    expect(hasErrorMessage || hasEmptyState || !hasSpinner).toBeTruthy();
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if main elements are still visible and accessible
    await expect(page.locator('h1:has-text("Learning Center")')).toBeVisible();
    
    // Check if search is accessible
    await expect(page.locator('input[placeholder*="Search"]')).toBeVisible();
    
    // Check if tabs are accessible (might be collapsed on mobile)
    const tabs = page.locator('[role="tab"]');
    const tabCount = await tabs.count();
    expect(tabCount).toBeGreaterThan(0);
    
    // Check if action buttons are accessible
    await expect(page.locator('button:has-text("Submit Video")')).toBeVisible();
    
    // Test mobile navigation
    if (await page.locator('button[aria-label*="menu"]').isVisible()) {
      await page.click('button[aria-label*="menu"]');
      await page.waitForTimeout(500);
    }
  });

  test('should maintain state during navigation', async ({ page }) => {
    // Perform a search
    await page.fill('input[placeholder*="Search"]', 'React');
    await page.waitForTimeout(1000);
    
    // Navigate to different tab
    await page.click('[role="tab"]:has-text("Learning Paths")');
    await page.waitForTimeout(1000);
    
    // Navigate back to Discover
    await page.click('[role="tab"]:has-text("Discover")');
    await page.waitForTimeout(1000);
    
    // Check if search term is maintained (depending on implementation)
    const searchValue = await page.locator('input[placeholder*="Search"]').inputValue();
    // This might or might not be maintained depending on implementation
  });

  test('should handle authentication properly', async ({ page }) => {
    // Test that authenticated features are available
    await expect(page.locator('button:has-text("Submit Video")')).toBeVisible();
    await expect(page.locator('button:has-text("My Queues")')).toBeVisible();
    
    // Test My Progress tab (should be available for authenticated users)
    await page.click('[role="tab"]:has-text("My Progress")');
    await page.waitForTimeout(1000);
    
    // Should show progress content, not login prompt
    const hasProgressContent = await page.locator('text=Completed Videos').isVisible();
    const hasLoginPrompt = await page.locator('text=Please log in').isVisible();
    
    expect(hasProgressContent || !hasLoginPrompt).toBeTruthy();
  });
});

test.describe('Learning Center - Admin Features', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user (if different credentials needed)
    await page.goto('/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
  });

  test('should access admin video vetting dashboard', async ({ page }) => {
    // Navigate to admin dashboard
    await page.goto('/admin');
    await page.waitForLoadState('networkidle');
    
    // Check if video vetting tab is available
    const vettingTab = page.locator('[role="tab"]:has-text("Video Vetting")');
    if (await vettingTab.isVisible()) {
      await vettingTab.click();
      await page.waitForTimeout(1000);
      
      // Check vetting dashboard elements
      await expect(page.locator('h1:has-text("Video Vetting Dashboard")')).toBeVisible();
      await expect(page.locator('text=Pending Review')).toBeVisible();
      await expect(page.locator('text=Approved')).toBeVisible();
      await expect(page.locator('text=Rejected')).toBeVisible();
    }
  });

  test('should access learning path manager', async ({ page }) => {
    // Navigate to admin dashboard
    await page.goto('/admin');
    await page.waitForLoadState('networkidle');
    
    // Check if learning paths tab is available
    const pathsTab = page.locator('[role="tab"]:has-text("Learning Paths")');
    if (await pathsTab.isVisible()) {
      await pathsTab.click();
      await page.waitForTimeout(1000);
      
      // Check learning path manager elements
      await expect(page.locator('h1:has-text("Learning Path Manager")')).toBeVisible();
      await expect(page.locator('button:has-text("Create Learning Path")')).toBeVisible();
    }
  });
});
