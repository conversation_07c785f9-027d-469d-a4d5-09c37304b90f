-- Migration: Venture Management System Enhancement
-- Description: Enhanced venture/project management with advanced features
-- Created: 2024-01-16
-- Integration & Services Agent

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enhanced venture members table (extends project_contributors)
CREATE TABLE IF NOT EXISTS venture_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venture_id UUID NOT NULL, -- References projects table (ventures)
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(50) NOT NULL DEFAULT 'member', -- 'lead', 'member', 'contributor', 'advisor', 'observer'
  permissions JSONB DEFAULT '{
    "can_edit_venture": false,
    "can_manage_team": false,
    "can_create_milestones": false,
    "can_approve_milestones": false,
    "can_manage_budget": false,
    "can_view_analytics": true,
    "can_invite_members": false
  }'::jsonb,
  revenue_share DECIMAL(5,2) DEFAULT 0.00, -- Percentage of revenue (0-100)
  hourly_rate DECIMAL(8,2), -- Optional hourly rate for time-based compensation
  equity_percentage DECIMAL(5,2) DEFAULT 0.00, -- Equity stake if applicable
  compensation_model VARCHAR(50) DEFAULT 'revenue_share', -- 'revenue_share', 'hourly', 'equity', 'fixed', 'hybrid'
  fixed_amount DECIMAL(10,2), -- Fixed compensation amount
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'active', -- 'active', 'inactive', 'pending', 'removed'
  invitation_token VARCHAR(255), -- For pending invitations
  invitation_expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(venture_id, user_id)
);

-- Venture milestones table
CREATE TABLE venture_milestones (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venture_id UUID NOT NULL, -- References projects table
  title VARCHAR(255) NOT NULL,
  description TEXT,
  milestone_type VARCHAR(50) DEFAULT 'deliverable', -- 'deliverable', 'payment', 'review', 'approval'
  priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
  due_date DATE,
  estimated_hours DECIMAL(6,2),
  actual_hours DECIMAL(6,2) DEFAULT 0.00,
  budget_allocation DECIMAL(10,2), -- Budget allocated to this milestone
  actual_cost DECIMAL(10,2) DEFAULT 0.00,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'in_progress', 'review', 'completed', 'overdue', 'cancelled'
  completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
  assigned_to UUID REFERENCES auth.users(id),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  approved_by UUID REFERENCES auth.users(id),
  
  -- Revenue and payment triggers
  revenue_trigger BOOLEAN DEFAULT false, -- Does completion trigger payment?
  revenue_amount DECIMAL(10,2), -- Amount to distribute on completion
  payment_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  
  -- Dependencies and relationships
  dependencies JSONB DEFAULT '[]'::jsonb, -- Array of milestone IDs this depends on
  deliverables JSONB DEFAULT '[]'::jsonb, -- Array of deliverable descriptions/links
  acceptance_criteria JSONB DEFAULT '[]'::jsonb, -- Array of acceptance criteria
  
  -- Tracking and metadata
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  approved_at TIMESTAMP WITH TIME ZONE,
  review_notes TEXT,
  completion_evidence JSONB DEFAULT '[]'::jsonb, -- Links to deliverables, screenshots, etc.
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Venture activities/timeline table
CREATE TABLE venture_activities (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venture_id UUID NOT NULL, -- References projects table
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  activity_type VARCHAR(50) NOT NULL, -- 'milestone_created', 'milestone_completed', 'member_added', 'payment_made', 'status_changed'
  activity_category VARCHAR(50) DEFAULT 'general', -- 'milestone', 'team', 'financial', 'system', 'communication'
  title VARCHAR(255) NOT NULL,
  description TEXT,
  metadata JSONB DEFAULT '{}'::jsonb, -- Additional activity-specific data
  
  -- Related entities
  related_milestone_id UUID REFERENCES venture_milestones(id) ON DELETE SET NULL,
  related_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Visibility and importance
  visibility VARCHAR(20) DEFAULT 'team', -- 'public', 'team', 'leads', 'private'
  importance VARCHAR(20) DEFAULT 'normal', -- 'low', 'normal', 'high', 'critical'
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Venture templates table
CREATE TABLE venture_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  template_type VARCHAR(50) NOT NULL, -- 'software', 'creative', 'consulting', 'research', 'marketing', 'event'
  industry VARCHAR(100),
  complexity_level VARCHAR(20) DEFAULT 'medium', -- 'simple', 'medium', 'complex', 'enterprise'
  estimated_duration_weeks INTEGER,
  estimated_budget DECIMAL(10,2),
  team_size_min INTEGER DEFAULT 1,
  team_size_max INTEGER DEFAULT 10,
  
  -- Template configuration
  default_roles JSONB DEFAULT '[]'::jsonb, -- Array of default roles and permissions
  milestone_templates JSONB DEFAULT '[]'::jsonb, -- Array of milestone templates
  revenue_model_template JSONB DEFAULT '{}'::jsonb, -- Default revenue sharing model
  required_skills JSONB DEFAULT '[]'::jsonb, -- Array of required skills
  recommended_tools JSONB DEFAULT '[]'::jsonb, -- Array of recommended tools/technologies
  
  -- Template metadata
  usage_count INTEGER DEFAULT 0,
  success_rate DECIMAL(5,2), -- Success rate of ventures using this template
  average_completion_time INTEGER, -- Average completion time in days
  is_active BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Venture budget tracking table
CREATE TABLE venture_budget_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venture_id UUID NOT NULL, -- References projects table
  milestone_id UUID REFERENCES venture_milestones(id) ON DELETE SET NULL,
  category VARCHAR(100) NOT NULL, -- 'development', 'design', 'marketing', 'tools', 'infrastructure', 'other'
  item_name VARCHAR(255) NOT NULL,
  description TEXT,
  budget_type VARCHAR(50) DEFAULT 'expense', -- 'expense', 'revenue', 'investment'
  planned_amount DECIMAL(10,2) NOT NULL,
  actual_amount DECIMAL(10,2) DEFAULT 0.00,
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Tracking and approval
  status VARCHAR(20) DEFAULT 'planned', -- 'planned', 'approved', 'spent', 'cancelled'
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  spent_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  vendor VARCHAR(255), -- Who the expense was paid to
  receipt_url VARCHAR(500), -- Link to receipt/invoice
  notes TEXT,
  
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Venture revenue tracking table
CREATE TABLE venture_revenue_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venture_id UUID NOT NULL, -- References projects table
  milestone_id UUID REFERENCES venture_milestones(id) ON DELETE SET NULL,
  revenue_type VARCHAR(50) NOT NULL, -- 'milestone_payment', 'recurring_revenue', 'bonus', 'equity_distribution'
  amount DECIMAL(12,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Distribution details
  distribution_model JSONB NOT NULL, -- How revenue is distributed among members
  distribution_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  distributed_at TIMESTAMP WITH TIME ZONE,
  
  -- Source and metadata
  revenue_source VARCHAR(255), -- Client, platform, sales, etc.
  payment_method VARCHAR(50), -- 'bank_transfer', 'crypto', 'platform_escrow'
  transaction_id VARCHAR(255), -- External transaction reference
  notes TEXT,
  
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Venture communication threads table
CREATE TABLE venture_communications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venture_id UUID NOT NULL, -- References projects table
  thread_type VARCHAR(50) DEFAULT 'general', -- 'general', 'milestone', 'decision', 'announcement'
  subject VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  author_id UUID NOT NULL REFERENCES auth.users(id),
  
  -- Threading and replies
  parent_id UUID REFERENCES venture_communications(id) ON DELETE CASCADE,
  thread_root_id UUID REFERENCES venture_communications(id) ON DELETE CASCADE,
  
  -- Related entities
  related_milestone_id UUID REFERENCES venture_milestones(id) ON DELETE SET NULL,
  related_member_id UUID REFERENCES venture_members(id) ON DELETE SET NULL,
  
  -- Metadata
  is_announcement BOOLEAN DEFAULT false,
  is_decision BOOLEAN DEFAULT false,
  decision_deadline TIMESTAMP WITH TIME ZONE,
  attachments JSONB DEFAULT '[]'::jsonb,
  mentions JSONB DEFAULT '[]'::jsonb, -- Array of mentioned user IDs
  
  -- Status and visibility
  status VARCHAR(20) DEFAULT 'active', -- 'active', 'archived', 'deleted'
  visibility VARCHAR(20) DEFAULT 'team', -- 'public', 'team', 'leads', 'private'
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_venture_members_venture_id ON venture_members(venture_id);
CREATE INDEX idx_venture_members_user_id ON venture_members(user_id);
CREATE INDEX idx_venture_members_status ON venture_members(status);

CREATE INDEX idx_venture_milestones_venture_id ON venture_milestones(venture_id);
CREATE INDEX idx_venture_milestones_status ON venture_milestones(status);
CREATE INDEX idx_venture_milestones_due_date ON venture_milestones(due_date);
CREATE INDEX idx_venture_milestones_assigned_to ON venture_milestones(assigned_to);

CREATE INDEX idx_venture_activities_venture_id ON venture_activities(venture_id);
CREATE INDEX idx_venture_activities_type ON venture_activities(activity_type);
CREATE INDEX idx_venture_activities_created_at ON venture_activities(created_at);

CREATE INDEX idx_venture_templates_type ON venture_templates(template_type);
CREATE INDEX idx_venture_templates_active ON venture_templates(is_active);
CREATE INDEX idx_venture_templates_featured ON venture_templates(is_featured);

CREATE INDEX idx_venture_budget_venture_id ON venture_budget_items(venture_id);
CREATE INDEX idx_venture_budget_category ON venture_budget_items(category);
CREATE INDEX idx_venture_budget_status ON venture_budget_items(status);

CREATE INDEX idx_venture_revenue_venture_id ON venture_revenue_events(venture_id);
CREATE INDEX idx_venture_revenue_status ON venture_revenue_events(distribution_status);
CREATE INDEX idx_venture_revenue_created_at ON venture_revenue_events(created_at);

CREATE INDEX idx_venture_communications_venture_id ON venture_communications(venture_id);
CREATE INDEX idx_venture_communications_thread_root ON venture_communications(thread_root_id);
CREATE INDEX idx_venture_communications_created_at ON venture_communications(created_at);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_venture_members_updated_at BEFORE UPDATE ON venture_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_venture_milestones_updated_at BEFORE UPDATE ON venture_milestones FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_venture_templates_updated_at BEFORE UPDATE ON venture_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_venture_budget_items_updated_at BEFORE UPDATE ON venture_budget_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_venture_communications_updated_at BEFORE UPDATE ON venture_communications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE venture_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE venture_milestones ENABLE ROW LEVEL SECURITY;
ALTER TABLE venture_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE venture_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE venture_budget_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE venture_revenue_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE venture_communications ENABLE ROW LEVEL SECURITY;

-- RLS policies for venture members
CREATE POLICY "Venture members can view team members" ON venture_members FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_members.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
  )
);

CREATE POLICY "Venture leads can manage team members" ON venture_members FOR ALL USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_members.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.role IN ('lead') 
    AND vm.status = 'active'
    AND (vm.permissions->>'can_manage_team')::boolean = true
  )
);

-- RLS policies for milestones
CREATE POLICY "Venture members can view milestones" ON venture_milestones FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_milestones.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
  )
);

CREATE POLICY "Authorized members can manage milestones" ON venture_milestones FOR ALL USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_milestones.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
    AND (vm.permissions->>'can_create_milestones')::boolean = true
  ) OR auth.uid() = created_by
);

-- RLS policies for activities
CREATE POLICY "Venture members can view activities" ON venture_activities FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_activities.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
  )
);

CREATE POLICY "Venture members can create activities" ON venture_activities FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_activities.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
  )
);

-- RLS policies for templates
CREATE POLICY "Templates are viewable by all authenticated users" ON venture_templates FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);
CREATE POLICY "Users can manage their own templates" ON venture_templates FOR ALL USING (auth.uid() = created_by);

-- RLS policies for budget items
CREATE POLICY "Venture members can view budget items" ON venture_budget_items FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_budget_items.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
  )
);

CREATE POLICY "Authorized members can manage budget" ON venture_budget_items FOR ALL USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_budget_items.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
    AND (vm.permissions->>'can_manage_budget')::boolean = true
  ) OR auth.uid() = created_by
);

-- RLS policies for revenue events
CREATE POLICY "Venture members can view revenue events" ON venture_revenue_events FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_revenue_events.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
  )
);

-- RLS policies for communications
CREATE POLICY "Venture members can view communications" ON venture_communications FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_communications.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
  )
);

CREATE POLICY "Venture members can create communications" ON venture_communications FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM venture_members vm 
    WHERE vm.venture_id = venture_communications.venture_id 
    AND vm.user_id = auth.uid() 
    AND vm.status = 'active'
  )
);

-- Insert default venture templates (only if users exist)
DO $$
DECLARE
    sample_user_id UUID;
BEGIN
    -- Get a sample user ID
    SELECT id INTO sample_user_id FROM auth.users LIMIT 1;

    -- Only insert if we have a user
    IF sample_user_id IS NOT NULL THEN
        INSERT INTO venture_templates (name, description, template_type, industry, complexity_level, estimated_duration_weeks, estimated_budget, team_size_min, team_size_max, default_roles, milestone_templates, revenue_model_template, required_skills, created_by) VALUES
('Web Application Development', 'Full-stack web application with modern technologies', 'software', 'Technology', 'medium', 12, 50000.00, 3, 8, 
'[{"role": "lead", "title": "Project Lead"}, {"role": "member", "title": "Frontend Developer"}, {"role": "member", "title": "Backend Developer"}, {"role": "member", "title": "UI/UX Designer"}]'::jsonb,
'[{"title": "Project Setup & Planning", "type": "deliverable", "estimated_hours": 40}, {"title": "UI/UX Design", "type": "deliverable", "estimated_hours": 80}, {"title": "Frontend Development", "type": "deliverable", "estimated_hours": 160}, {"title": "Backend Development", "type": "deliverable", "estimated_hours": 160}, {"title": "Testing & QA", "type": "deliverable", "estimated_hours": 60}, {"title": "Deployment & Launch", "type": "deliverable", "estimated_hours": 40}]'::jsonb,
'{"model": "contribution_based", "distribution": "equal_split"}'::jsonb,
'["JavaScript", "React", "Node.js", "Database Design", "UI/UX Design"]'::jsonb,
sample_user_id),

('Mobile App Development', 'Cross-platform mobile application development', 'software', 'Technology', 'complex', 16, 75000.00, 4, 10,
'[{"role": "lead", "title": "Project Lead"}, {"role": "member", "title": "Mobile Developer"}, {"role": "member", "title": "Backend Developer"}, {"role": "member", "title": "UI/UX Designer"}, {"role": "member", "title": "QA Engineer"}]'::jsonb,
'[{"title": "App Architecture & Planning", "type": "deliverable", "estimated_hours": 60}, {"title": "UI/UX Design", "type": "deliverable", "estimated_hours": 100}, {"title": "Frontend Development", "type": "deliverable", "estimated_hours": 200}, {"title": "Backend API Development", "type": "deliverable", "estimated_hours": 120}, {"title": "Testing & QA", "type": "deliverable", "estimated_hours": 80}, {"title": "App Store Deployment", "type": "deliverable", "estimated_hours": 40}]'::jsonb,
'{"model": "role_based", "distribution": "weighted_by_role"}'::jsonb,
'["React Native", "Flutter", "Mobile UI/UX", "API Development", "App Store Optimization"]'::jsonb,
sample_user_id),

('Creative Content Production', 'Video, graphic design, and content creation project', 'creative', 'Media', 'medium', 8, 25000.00, 2, 6,
'[{"role": "lead", "title": "Creative Director"}, {"role": "member", "title": "Video Editor"}, {"role": "member", "title": "Graphic Designer"}, {"role": "member", "title": "Content Writer"}]'::jsonb,
'[{"title": "Creative Brief & Concept", "type": "deliverable", "estimated_hours": 20}, {"title": "Content Planning", "type": "deliverable", "estimated_hours": 30}, {"title": "Content Creation", "type": "deliverable", "estimated_hours": 120}, {"title": "Review & Revisions", "type": "deliverable", "estimated_hours": 40}, {"title": "Final Delivery", "type": "deliverable", "estimated_hours": 20}]'::jsonb,
'{"model": "milestone_based", "distribution": "equal_split"}'::jsonb,
'["Video Editing", "Graphic Design", "Content Writing", "Creative Direction"]'::jsonb,
sample_user_id);
    END IF;
END $$;

-- Functions for venture management
CREATE OR REPLACE FUNCTION calculate_venture_progress(venture_id_param UUID)
RETURNS DECIMAL(5,2) AS $$
DECLARE
  total_milestones INTEGER;
  completed_milestones INTEGER;
  progress_percentage DECIMAL(5,2);
BEGIN
  SELECT COUNT(*) INTO total_milestones
  FROM venture_milestones 
  WHERE venture_id = venture_id_param;
  
  SELECT COUNT(*) INTO completed_milestones
  FROM venture_milestones 
  WHERE venture_id = venture_id_param AND status = 'completed';
  
  IF total_milestones = 0 THEN
    RETURN 0.00;
  END IF;
  
  progress_percentage := (completed_milestones::DECIMAL / total_milestones::DECIMAL) * 100;
  RETURN progress_percentage;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION log_venture_activity(
  venture_id_param UUID,
  user_id_param UUID,
  activity_type_param VARCHAR(50),
  title_param VARCHAR(255),
  description_param TEXT DEFAULT NULL,
  metadata_param JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO venture_activities (
    venture_id, user_id, activity_type, title, description, metadata
  ) VALUES (
    venture_id_param, user_id_param, activity_type_param, title_param, description_param, metadata_param
  ) RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql;

-- Table comments
COMMENT ON TABLE venture_members IS 'Enhanced team member management with roles, permissions, and revenue sharing';
COMMENT ON TABLE venture_milestones IS 'Comprehensive milestone tracking with dependencies and revenue triggers';
COMMENT ON TABLE venture_activities IS 'Activity timeline and audit log for venture events';
COMMENT ON TABLE venture_templates IS 'Pre-configured venture templates for different project types';
COMMENT ON TABLE venture_budget_items IS 'Budget planning and expense tracking for ventures';
COMMENT ON TABLE venture_revenue_events IS 'Revenue distribution and payment tracking';
COMMENT ON TABLE venture_communications IS 'Team communication and decision tracking';
