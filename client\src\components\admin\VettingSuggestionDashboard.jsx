import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Chip, Progress, Modal, ModalContent, 
  ModalHeader, ModalBody, ModalFooter, useDisclosure, Textarea, Select, 
  SelectItem, Input, Tabs, Tab, Table, TableHeader, TableColumn, TableBody, 
  TableRow, TableCell, Spinner, Badge, Divider
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  Shield, CheckCircle, XCircle, Clock, TrendingUp, Users, Star, 
  ThumbsUp, ThumbsDown, Eye, ExternalLink, AlertTriangle, Award,
  BookOpen, Target, Filter, Search
} from 'lucide-react';

/**
 * Vetting Suggestion Dashboard Component
 * 
 * Admin interface for reviewing and approving/rejecting vetting content suggestions
 * with detailed review criteria and community voting integration.
 */
const VettingSuggestionDashboard = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [suggestions, setSuggestions] = useState([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLevel, setFilterLevel] = useState('all');
  
  const [reviewForm, setReviewForm] = useState({
    action: '',
    reviewNotes: '',
    approvedLevel: 1,
    approvedSkills: [],
    finalJustification: '',
    adminRating: 5
  });

  const { isOpen: isReviewOpen, onOpen: onReviewOpen, onClose: onReviewClose } = useDisclosure();

  // Vetting level definitions
  const vettingLevels = [
    { level: 1, name: 'Learning', color: 'warning', icon: '🟡' },
    { level: 2, name: 'Peer Verified', color: 'secondary', icon: '🟠' },
    { level: 3, name: 'Project Verified', color: 'success', icon: '🟢' },
    { level: 4, name: 'Expert Verified', color: 'primary', icon: '🔵' },
    { level: 5, name: 'Master Verified', color: 'danger', icon: '🟣' }
  ];

  useEffect(() => {
    loadSuggestions();
  }, [activeTab]);

  const loadSuggestions = async () => {
    try {
      setLoading(true);

      let query = supabase
        .from('vetting_content_suggestions')
        .select(`
          *,
          submitter:auth.users!user_id(user_metadata),
          reviewer:auth.users!reviewed_by(user_metadata),
          votes:vetting_content_votes(vote_type, comment, expertise_level)
        `)
        .order('created_at', { ascending: false });

      // Filter by status based on active tab
      if (activeTab !== 'all') {
        query = query.eq('status', activeTab);
      }

      const { data, error } = await query;

      if (error) throw error;

      setSuggestions(data || []);

    } catch (error) {
      console.error('Error loading suggestions:', error);
      toast.error('Failed to load vetting suggestions');
    } finally {
      setLoading(false);
    }
  };

  const handleReviewSuggestion = (suggestion) => {
    setSelectedSuggestion(suggestion);
    setReviewForm({
      action: '',
      reviewNotes: '',
      approvedLevel: suggestion.suggested_vetting_level,
      approvedSkills: suggestion.target_skills || [],
      finalJustification: suggestion.justification || '',
      adminRating: 5
    });
    onReviewOpen();
  };

  const submitReview = async () => {
    if (!reviewForm.action || !reviewForm.reviewNotes) {
      toast.error('Please provide an action and review notes');
      return;
    }

    try {
      setLoading(true);

      // Update suggestion status
      const updateData = {
        status: reviewForm.action,
        reviewed_by: currentUser.id,
        reviewed_at: new Date().toISOString(),
        review_notes: reviewForm.reviewNotes,
        admin_rating: reviewForm.adminRating
      };

      if (reviewForm.action === 'approved') {
        updateData.approved_vetting_level = reviewForm.approvedLevel;
        updateData.approved_skills = reviewForm.approvedSkills;
        updateData.final_justification = reviewForm.finalJustification;
      }

      const { error: updateError } = await supabase
        .from('vetting_content_suggestions')
        .update(updateData)
        .eq('id', selectedSuggestion.id);

      if (updateError) throw updateError;

      // If approved, add to course catalog with vetting information
      if (reviewForm.action === 'approved') {
        const catalogData = {
          external_id: selectedSuggestion.video_id,
          provider: 'youtube',
          title: selectedSuggestion.title || 'Vetting Content',
          description: selectedSuggestion.description || selectedSuggestion.justification,
          course_url: selectedSuggestion.video_url || `https://www.youtube.com/watch?v=${selectedSuggestion.video_id}`,
          skills: reviewForm.approvedSkills,
          difficulty_level: getLevelDifficulty(reviewForm.approvedLevel),
          is_active: true,
          is_featured: false,
          vetting_level: reviewForm.approvedLevel,
          vetting_approved: true,
          estimated_duration_hours: selectedSuggestion.estimated_completion_hours,
          metadata: {
            reviewed_by: currentUser.id,
            review_date: new Date().toISOString(),
            vetting_suggestion_id: selectedSuggestion.id,
            admin_rating: reviewForm.adminRating,
            community_votes: selectedSuggestion.community_votes || 0
          }
        };

        const { error: catalogError } = await supabase
          .from('course_catalog')
          .upsert([catalogData], { onConflict: 'external_id,provider' });

        if (catalogError) {
          console.error('Error adding to catalog:', catalogError);
          toast.error('Suggestion approved but failed to add to catalog');
        } else {
          toast.success('Suggestion approved and added to vetting catalog!');
        }
      } else {
        toast.success(`Suggestion ${reviewForm.action}!`);
      }

      onReviewClose();
      loadSuggestions();

    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    } finally {
      setLoading(false);
    }
  };

  const getLevelDifficulty = (level) => {
    const difficultyMap = {
      1: 'beginner',
      2: 'intermediate', 
      3: 'intermediate',
      4: 'advanced',
      5: 'advanced'
    };
    return difficultyMap[level] || 'intermediate';
  };

  const getVettingLevelInfo = (level) => {
    return vettingLevels.find(v => v.level === level) || vettingLevels[0];
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending_review': 'warning',
      'under_review': 'primary',
      'approved': 'success',
      'rejected': 'danger',
      'needs_revision': 'secondary'
    };
    return colors[status] || 'default';
  };

  const filteredSuggestions = suggestions.filter(suggestion => {
    const matchesSearch = !searchTerm || 
      suggestion.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      suggestion.justification?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      suggestion.target_skills?.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesLevel = filterLevel === 'all' || 
      suggestion.suggested_vetting_level === parseInt(filterLevel);
    
    return matchesSearch && matchesLevel;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center py-12">
        <Card className="max-w-md">
          <CardBody className="text-center p-8">
            <AlertTriangle className="w-16 h-16 mx-auto mb-4 text-warning" />
            <h2 className="text-xl font-bold mb-2">Access Denied</h2>
            <p className="text-default-600">
              You need to be logged in as an admin to access the vetting suggestion dashboard.
            </p>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="vetting-suggestion-dashboard space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="text-primary" />
            Vetting Suggestion Dashboard
          </h1>
          <p className="text-default-600 mt-1">
            Review and approve community suggestions for vetting content
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Search className="w-4 h-4 text-default-400" />
            <Input
              placeholder="Search suggestions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
              size="sm"
            />
          </div>
          
          <Select
            placeholder="Filter by level"
            selectedKeys={[filterLevel]}
            onSelectionChange={(keys) => setFilterLevel(Array.from(keys)[0])}
            className="w-48"
            size="sm"
          >
            <SelectItem key="all">All Levels</SelectItem>
            {vettingLevels.map((level) => (
              <SelectItem key={level.level.toString()}>
                {level.icon} Level {level.level} - {level.name}
              </SelectItem>
            ))}
          </Select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {[
          { key: 'pending_review', label: 'Pending Review', icon: Clock, color: 'warning' },
          { key: 'under_review', label: 'Under Review', icon: Eye, color: 'primary' },
          { key: 'approved', label: 'Approved', icon: CheckCircle, color: 'success' },
          { key: 'rejected', label: 'Rejected', icon: XCircle, color: 'danger' },
          { key: 'needs_revision', label: 'Needs Revision', icon: AlertTriangle, color: 'secondary' }
        ].map((stat) => {
          const count = suggestions.filter(s => s.status === stat.key).length;
          const Icon = stat.icon;
          
          return (
            <Card key={stat.key} className="hover:shadow-md transition-shadow">
              <CardBody className="p-4">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg bg-${stat.color}/10`}>
                    <Icon className={`w-5 h-5 text-${stat.color}`} />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{count}</p>
                    <p className="text-sm text-default-600">{stat.label}</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          );
        })}
      </div>

      {/* Main Content */}
      <Tabs 
        selectedKey={activeTab} 
        onSelectionChange={setActiveTab}
        className="w-full"
      >
        <Tab key="pending_review" title="Pending Review">
          <SuggestionsTable
            suggestions={filteredSuggestions.filter(s => s.status === 'pending_review')}
            loading={loading}
            onReview={handleReviewSuggestion}
            vettingLevels={vettingLevels}
          />
        </Tab>
        <Tab key="under_review" title="Under Review">
          <SuggestionsTable
            suggestions={filteredSuggestions.filter(s => s.status === 'under_review')}
            loading={loading}
            onReview={handleReviewSuggestion}
            vettingLevels={vettingLevels}
          />
        </Tab>
        <Tab key="approved" title="Approved">
          <SuggestionsTable
            suggestions={filteredSuggestions.filter(s => s.status === 'approved')}
            loading={loading}
            onReview={handleReviewSuggestion}
            vettingLevels={vettingLevels}
            showActions={false}
          />
        </Tab>
        <Tab key="rejected" title="Rejected">
          <SuggestionsTable
            suggestions={filteredSuggestions.filter(s => s.status === 'rejected')}
            loading={loading}
            onReview={handleReviewSuggestion}
            vettingLevels={vettingLevels}
            showActions={false}
          />
        </Tab>
        <Tab key="all" title="All Suggestions">
          <SuggestionsTable
            suggestions={filteredSuggestions}
            loading={loading}
            onReview={handleReviewSuggestion}
            vettingLevels={vettingLevels}
          />
        </Tab>
      </Tabs>

      {/* Review Modal */}
      <Modal isOpen={isReviewOpen} onClose={onReviewClose} size="3xl">
        <ModalContent>
          <ModalHeader>
            <h2 className="text-xl font-bold">Review Vetting Suggestion</h2>
          </ModalHeader>
          <ModalBody>
            {selectedSuggestion && (
              <div className="space-y-6">
                {/* Suggestion Details */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Suggestion Details</h3>
                  </CardHeader>
                  <CardBody className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-default-600">Video ID</p>
                        <p className="font-mono text-sm">{selectedSuggestion.video_id}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-default-600">Suggested Level</p>
                        <Chip 
                          color={getVettingLevelInfo(selectedSuggestion.suggested_vetting_level).color}
                          variant="flat"
                        >
                          {getVettingLevelInfo(selectedSuggestion.suggested_vetting_level).icon} Level {selectedSuggestion.suggested_vetting_level}
                        </Chip>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium text-default-600 mb-2">Target Skills</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedSuggestion.target_skills?.map((skill, index) => (
                          <Chip key={index} size="sm" variant="flat">{skill}</Chip>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium text-default-600 mb-2">Justification</p>
                      <p className="text-sm bg-default-100 p-3 rounded-lg">
                        {selectedSuggestion.justification}
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-default-600">Community Votes</p>
                        <p className="text-lg font-semibold text-success">
                          {selectedSuggestion.community_votes || 0}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-default-600">Estimated Hours</p>
                        <p className="text-lg font-semibold">
                          {selectedSuggestion.estimated_completion_hours || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Review Form */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Admin Review</h3>
                  </CardHeader>
                  <CardBody className="space-y-4">
                    <Select
                      label="Review Action"
                      placeholder="Select action"
                      selectedKeys={[reviewForm.action]}
                      onSelectionChange={(keys) => setReviewForm(prev => ({
                        ...prev,
                        action: Array.from(keys)[0]
                      }))}
                    >
                      <SelectItem key="approved">✅ Approve</SelectItem>
                      <SelectItem key="rejected">❌ Reject</SelectItem>
                      <SelectItem key="needs_revision">🔄 Needs Revision</SelectItem>
                    </Select>

                    {reviewForm.action === 'approved' && (
                      <>
                        <Select
                          label="Approved Vetting Level"
                          placeholder="Select final vetting level"
                          selectedKeys={[reviewForm.approvedLevel.toString()]}
                          onSelectionChange={(keys) => setReviewForm(prev => ({
                            ...prev,
                            approvedLevel: parseInt(Array.from(keys)[0])
                          }))}
                        >
                          {vettingLevels.map((level) => (
                            <SelectItem key={level.level.toString()}>
                              {level.icon} Level {level.level} - {level.name}
                            </SelectItem>
                          ))}
                        </Select>

                        <Textarea
                          label="Final Justification"
                          placeholder="Admin justification for the approved level..."
                          value={reviewForm.finalJustification}
                          onChange={(e) => setReviewForm(prev => ({
                            ...prev,
                            finalJustification: e.target.value
                          }))}
                          minRows={2}
                        />
                      </>
                    )}

                    <Textarea
                      label="Review Notes"
                      placeholder="Detailed review notes..."
                      value={reviewForm.reviewNotes}
                      onChange={(e) => setReviewForm(prev => ({
                        ...prev,
                        reviewNotes: e.target.value
                      }))}
                      minRows={3}
                    />

                    <Select
                      label="Admin Rating"
                      placeholder="Rate the suggestion quality"
                      selectedKeys={[reviewForm.adminRating.toString()]}
                      onSelectionChange={(keys) => setReviewForm(prev => ({
                        ...prev,
                        adminRating: parseInt(Array.from(keys)[0])
                      }))}
                    >
                      <SelectItem key="5">⭐⭐⭐⭐⭐ Excellent</SelectItem>
                      <SelectItem key="4">⭐⭐⭐⭐ Good</SelectItem>
                      <SelectItem key="3">⭐⭐⭐ Average</SelectItem>
                      <SelectItem key="2">⭐⭐ Poor</SelectItem>
                      <SelectItem key="1">⭐ Very Poor</SelectItem>
                    </Select>
                  </CardBody>
                </Card>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onReviewClose}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={submitReview}
              isLoading={loading}
            >
              Submit Review
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

// Suggestions Table Component
const SuggestionsTable = ({ suggestions, loading, onReview, vettingLevels, showActions = true }) => {
  const getVettingLevelInfo = (level) => {
    return vettingLevels.find(v => v.level === level) || vettingLevels[0];
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending_review': 'warning',
      'under_review': 'primary',
      'approved': 'success',
      'rejected': 'danger',
      'needs_revision': 'secondary'
    };
    return colors[status] || 'default';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  if (suggestions.length === 0) {
    return (
      <div className="text-center py-12">
        <BookOpen className="w-16 h-16 mx-auto mb-4 text-default-400" />
        <h3 className="text-xl font-semibold mb-2">No suggestions found</h3>
        <p className="text-default-600">
          No vetting suggestions match the current filters.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {suggestions.map((suggestion) => (
        <Card key={suggestion.id} className="hover:shadow-md transition-shadow">
          <CardBody className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-3">
                  <h3 className="font-semibold text-lg">
                    {suggestion.title || `Video ${suggestion.video_id}`}
                  </h3>
                  <Chip 
                    color={getVettingLevelInfo(suggestion.suggested_vetting_level).color}
                    variant="flat"
                    size="sm"
                  >
                    {getVettingLevelInfo(suggestion.suggested_vetting_level).icon} Level {suggestion.suggested_vetting_level}
                  </Chip>
                  <Chip 
                    color={getStatusColor(suggestion.status)}
                    variant="flat"
                    size="sm"
                  >
                    {suggestion.status.replace('_', ' ')}
                  </Chip>
                </div>
                
                <p className="text-default-600 mb-3 line-clamp-2">
                  {suggestion.justification}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-default-500">
                  <span>By: {suggestion.submitter?.user_metadata?.full_name || 'Unknown'}</span>
                  <span>•</span>
                  <span>{new Date(suggestion.created_at).toLocaleDateString()}</span>
                  <span>•</span>
                  <span className="flex items-center gap-1">
                    <ThumbsUp className="w-3 h-3" />
                    {suggestion.community_votes || 0}
                  </span>
                  {suggestion.estimated_completion_hours && (
                    <>
                      <span>•</span>
                      <span>{suggestion.estimated_completion_hours}h</span>
                    </>
                  )}
                </div>
                
                {suggestion.target_skills && suggestion.target_skills.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {suggestion.target_skills.map((skill, index) => (
                      <Chip key={index} size="sm" variant="flat" color="primary">
                        {skill}
                      </Chip>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-2 ml-4">
                <Button
                  size="sm"
                  variant="flat"
                  startContent={<ExternalLink className="w-4 h-4" />}
                  onClick={() => window.open(
                    suggestion.video_url || `https://www.youtube.com/watch?v=${suggestion.video_id}`, 
                    '_blank'
                  )}
                >
                  View Video
                </Button>
                {showActions && (
                  <Button
                    size="sm"
                    color="primary"
                    startContent={<Eye className="w-4 h-4" />}
                    onClick={() => onReview(suggestion)}
                  >
                    Review
                  </Button>
                )}
              </div>
            </div>
          </CardBody>
        </Card>
      ))}
    </div>
  );
};

export default VettingSuggestionDashboard;
