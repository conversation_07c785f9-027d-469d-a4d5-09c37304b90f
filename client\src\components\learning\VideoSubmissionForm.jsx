import React, { useState, useContext } from 'react';
import { 
  <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON>dal<PERSON>eader, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>ooter,
  Button, Input, Textarea, Select, SelectItem, Chip, Card, CardBody,
  Checkbox, CheckboxGroup, Divider
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Video Submission Form Component
 * 
 * Allows users to submit educational YouTube videos for review and inclusion
 * in the learning center. Includes metadata collection and quality guidelines.
 */
const VideoSubmissionForm = ({ isOpen, onClose, onSubmissionSuccess }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  
  const [formData, setFormData] = useState({
    videoUrl: '',
    title: '',
    description: '',
    channelName: '',
    durationMinutes: '',
    skills: [],
    categories: [],
    difficultyLevel: 'beginner',
    learningObjectives: [''],
    prerequisites: [''],
    submissionReason: '',
    targetAudience: '',
    qualityNotes: '',
    trainingTrack: '',
    isPublic: true,
    allowVoting: true,
    // Vetting system integration
    suggestForVetting: false,
    vettingLevel: 1,
    vettingSkills: [],
    vettingJustification: '',
    estimatedCompletionTime: ''
  });

  const [errors, setErrors] = useState({});
  const [videoPreview, setVideoPreview] = useState(null);
  const [fetchingVideoData, setFetchingVideoData] = useState(false);

  // Available skills and categories
  const availableSkills = [
    'JavaScript', 'Python', 'React', 'Node.js', 'HTML', 'CSS', 'TypeScript',
    'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin',
    'SQL', 'MongoDB', 'PostgreSQL', 'MySQL', 'Redis',
    'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'DevOps',
    'Machine Learning', 'Data Science', 'AI', 'Deep Learning',
    'UI Design', 'UX Design', 'Figma', 'Adobe XD', 'Photoshop',
    'Project Management', 'Agile', 'Scrum', 'Leadership'
  ];

  const availableCategories = [
    'Programming', 'Web Development', 'Mobile Development', 'Data Science',
    'Machine Learning', 'DevOps', 'Cloud Computing', 'Database',
    'Design', 'UI/UX', 'Project Management', 'Soft Skills', 'Career Development'
  ];

  const trainingTracks = [
    'Frontend Development', 'Backend Development', 'Full Stack Development',
    'Data Science & Analytics', 'Machine Learning & AI', 'DevOps & Cloud',
    'Mobile Development', 'UI/UX Design', 'Project Management',
    'Cybersecurity', 'Blockchain', 'Game Development'
  ];

  const difficultyLevels = [
    { key: 'beginner', label: 'Beginner' },
    { key: 'intermediate', label: 'Intermediate' },
    { key: 'advanced', label: 'Advanced' }
  ];

  // Vetting level definitions (matching the vetting system)
  const vettingLevels = [
    {
      level: 1,
      name: 'Learning',
      color: '🟡',
      description: 'Foundational education content for beginners',
      requirements: 'Basic educational content, clear explanations, suitable for newcomers'
    },
    {
      level: 2,
      name: 'Peer Verified',
      color: '🟠',
      description: 'Community-validated intermediate content',
      requirements: 'Practical examples, hands-on projects, peer-reviewable content'
    },
    {
      level: 3,
      name: 'Project Verified',
      color: '🟢',
      description: 'Professional-level content with real-world applications',
      requirements: 'Industry best practices, complex projects, client-ready skills'
    },
    {
      level: 4,
      name: 'Expert Verified',
      color: '🔵',
      description: 'Advanced content for experienced professionals',
      requirements: 'Cutting-edge techniques, expert insights, advanced problem-solving'
    },
    {
      level: 5,
      name: 'Master Verified',
      color: '🟣',
      description: 'Master-level content for industry leaders',
      requirements: 'Thought leadership, innovation, mastery-level expertise'
    }
  ];

  // Extract YouTube video ID from URL
  const extractVideoId = (url) => {
    const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  // Fetch video data from YouTube
  const fetchVideoData = async (videoId) => {
    try {
      setFetchingVideoData(true);

      // Use YouTube oEmbed API for basic info (no API key required)
      const oEmbedResponse = await fetch(
        `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`
      );

      if (!oEmbedResponse.ok) {
        throw new Error('Video not found or unavailable');
      }

      const oEmbedData = await oEmbedResponse.json();

      // Create video preview object
      const preview = {
        id: videoId,
        title: oEmbedData.title,
        channelName: oEmbedData.author_name,
        thumbnail: oEmbedData.thumbnail_url,
        embedHtml: oEmbedData.html
      };

      setVideoPreview(preview);

      // Auto-fill form data
      setFormData(prev => ({
        ...prev,
        title: oEmbedData.title,
        channelName: oEmbedData.author_name
      }));

      toast.success('Video data loaded successfully!');

    } catch (error) {
      console.error('Error fetching video data:', error);
      toast.error('Failed to fetch video data. Please check the URL and try again.');
      setVideoPreview(null);
    } finally {
      setFetchingVideoData(false);
    }
  };

  // Handle video URL change
  const handleVideoUrlChange = async (url) => {
    setFormData(prev => ({ ...prev, videoUrl: url }));

    if (url.trim()) {
      const videoId = extractVideoId(url);
      if (videoId) {
        await fetchVideoData(videoId);
      } else {
        setVideoPreview(null);
        toast.error('Invalid YouTube URL format');
      }
    } else {
      setVideoPreview(null);
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};

    if (!formData.videoUrl) {
      newErrors.videoUrl = 'Video URL is required';
    } else if (!extractVideoId(formData.videoUrl)) {
      newErrors.videoUrl = 'Please enter a valid YouTube URL';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Video title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Video description is required';
    }

    if (formData.skills.length === 0) {
      newErrors.skills = 'Please select at least one skill';
    }

    if (formData.categories.length === 0) {
      newErrors.categories = 'Please select at least one category';
    }

    if (!formData.submissionReason.trim()) {
      newErrors.submissionReason = 'Please explain why this video should be included';
    }

    if (!formData.targetAudience.trim()) {
      newErrors.targetAudience = 'Please describe the target audience';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error('Please fix the errors before submitting');
      return;
    }

    setLoading(true);
    try {
      const videoId = extractVideoId(formData.videoUrl);

      // Check if video already exists
      const { data: existingVideo, error: checkError } = await supabase
        .from('video_submissions')
        .select('id, status')
        .eq('video_id', videoId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existingVideo) {
        toast.error('This video has already been submitted');
        setLoading(false);
        return;
      }

      const submissionData = {
        submitted_by: currentUser.id,
        submitter_name: currentUser.user_metadata?.full_name || currentUser.email,
        submitter_email: currentUser.email,
        video_url: formData.videoUrl,
        video_id: videoId,
        title: formData.title.trim(),
        description: formData.description.trim(),
        channel_name: formData.channelName.trim(),
        duration_minutes: formData.durationMinutes ? parseInt(formData.durationMinutes) : null,
        skills: formData.skills,
        categories: formData.categories,
        difficulty_level: formData.difficultyLevel,
        learning_objectives: formData.learningObjectives.filter(obj => obj.trim()),
        prerequisites: formData.prerequisites.filter(req => req.trim()),
        submission_reason: formData.submissionReason.trim(),
        target_audience: formData.targetAudience.trim(),
        quality_notes: formData.qualityNotes.trim(),
        training_track: formData.trainingTrack,
        is_public: formData.isPublic,
        allow_voting: formData.allowVoting,
        thumbnail_url: videoPreview?.thumbnail,
        // Vetting system fields
        suggest_for_vetting: formData.suggestForVetting,
        suggested_vetting_level: formData.suggestForVetting ? formData.vettingLevel : null,
        vetting_skills: formData.suggestForVetting ? formData.vettingSkills : [],
        vetting_justification: formData.suggestForVetting ? formData.vettingJustification.trim() : null,
        estimated_completion_time: formData.estimatedCompletionTime ? parseInt(formData.estimatedCompletionTime) : null
      };

      const { data, error } = await supabase
        .from('video_submissions')
        .insert([submissionData])
        .select()
        .single();

      if (error) throw error;

      // Also create a video recommendation entry for community voting
      if (formData.isPublic && formData.allowVoting) {
        const { error: recError } = await supabase
          .from('video_recommendations')
          .insert({
            title: formData.title.trim(),
            description: formData.description.trim(),
            video_url: formData.videoUrl,
            thumbnail_url: videoPreview?.thumbnail,
            provider: 'youtube',
            duration_minutes: formData.durationMinutes ? parseInt(formData.durationMinutes) : null,
            category: formData.categories[0] || null,
            skills: formData.skills,
            difficulty_level: formData.difficultyLevel,
            recommended_by: currentUser.id,
            recommended_by_name: currentUser.user_metadata?.full_name || currentUser.email,
            upvotes: 0,
            downvotes: 0,
            is_public: true,
            is_vetted: false
          });

        if (recError) {
          console.error('Error creating recommendation:', recError);
          // Don't fail the submission if recommendation creation fails
        }
      }

      toast.success('Video submitted successfully! It will be reviewed by our team and is now available for community voting.');

      if (onSubmissionSuccess) {
        onSubmissionSuccess(data);
      }

      // Reset form
      setFormData({
        videoUrl: '',
        title: '',
        description: '',
        channelName: '',
        durationMinutes: '',
        skills: [],
        categories: [],
        difficultyLevel: 'beginner',
        learningObjectives: [''],
        prerequisites: [''],
        submissionReason: '',
        targetAudience: '',
        qualityNotes: '',
        trainingTrack: '',
        isPublic: true,
        allowVoting: true
      });
      setVideoPreview(null);
      setStep(1);
      onClose();

    } catch (error) {
      console.error('Error submitting video:', error);
      toast.error('Failed to submit video. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle array field updates
  const updateArrayField = (field, index, value) => {
    const newArray = [...formData[field]];
    newArray[index] = value;
    setFormData({ ...formData, [field]: newArray });
  };

  const addArrayField = (field) => {
    setFormData({ ...formData, [field]: [...formData[field], ''] });
  };

  const removeArrayField = (field, index) => {
    const newArray = formData[field].filter((_, i) => i !== index);
    setFormData({ ...formData, [field]: newArray });
  };

  // Render step content
  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Video Information</h3>
              <p className="text-sm text-default-600 mb-4">
                Please provide basic information about the educational video you'd like to submit.
              </p>
            </div>

            <Input
              label="YouTube Video URL"
              placeholder="https://www.youtube.com/watch?v=..."
              value={formData.videoUrl}
              onChange={(e) => handleVideoUrlChange(e.target.value)}
              isInvalid={!!errors.videoUrl}
              errorMessage={errors.videoUrl}
              isRequired
              endContent={fetchingVideoData && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              )}
            />

            {/* Video Preview */}
            {videoPreview && (
              <Card className="bg-default-50">
                <CardBody className="p-4">
                  <div className="flex gap-4">
                    <img
                      src={videoPreview.thumbnail}
                      alt={videoPreview.title}
                      className="w-32 h-24 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h4 className="font-semibold line-clamp-2 mb-1">
                        {videoPreview.title}
                      </h4>
                      <p className="text-sm text-default-600">
                        by {videoPreview.channelName}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Chip size="sm" color="success" variant="flat">
                          ✓ Video Found
                        </Chip>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}

            <Input
              label="Video Title"
              placeholder="Enter the video title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              isInvalid={!!errors.title}
              errorMessage={errors.title}
              isRequired
            />

            <Textarea
              label="Video Description"
              placeholder="Describe what this video teaches and covers"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              isInvalid={!!errors.description}
              errorMessage={errors.description}
              minRows={3}
              isRequired
            />

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Channel Name"
                placeholder="YouTube channel name"
                value={formData.channelName}
                onChange={(e) => setFormData({ ...formData, channelName: e.target.value })}
              />

              <Input
                label="Duration (minutes)"
                placeholder="Video length in minutes"
                type="number"
                value={formData.durationMinutes}
                onChange={(e) => setFormData({ ...formData, durationMinutes: e.target.value })}
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Educational Metadata</h3>
              <p className="text-sm text-default-600 mb-4">
                Help us categorize this video and understand what skills it teaches.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Skills Covered *</label>
              <div className="flex flex-wrap gap-2 mb-2">
                {formData.skills.map((skill) => (
                  <Chip
                    key={skill}
                    onClose={() => setFormData({ 
                      ...formData, 
                      skills: formData.skills.filter(s => s !== skill) 
                    })}
                    variant="flat"
                    color="primary"
                  >
                    {skill}
                  </Chip>
                ))}
              </div>
              <Select
                placeholder="Select skills this video teaches"
                selectionMode="multiple"
                onSelectionChange={(keys) => setFormData({ 
                  ...formData, 
                  skills: Array.from(keys) 
                })}
                isInvalid={!!errors.skills}
                errorMessage={errors.skills}
              >
                {availableSkills.map((skill) => (
                  <SelectItem key={skill} value={skill}>
                    {skill}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Categories *</label>
              <div className="flex flex-wrap gap-2 mb-2">
                {formData.categories.map((category) => (
                  <Chip
                    key={category}
                    onClose={() => setFormData({ 
                      ...formData, 
                      categories: formData.categories.filter(c => c !== category) 
                    })}
                    variant="flat"
                    color="secondary"
                  >
                    {category}
                  </Chip>
                ))}
              </div>
              <Select
                placeholder="Select relevant categories"
                selectionMode="multiple"
                onSelectionChange={(keys) => setFormData({ 
                  ...formData, 
                  categories: Array.from(keys) 
                })}
                isInvalid={!!errors.categories}
                errorMessage={errors.categories}
              >
                {availableCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <Select
              label="Training Track (Optional)"
              placeholder="Select a training track this video belongs to"
              selectedKeys={formData.trainingTrack ? [formData.trainingTrack] : []}
              onSelectionChange={(keys) => setFormData({
                ...formData,
                trainingTrack: Array.from(keys)[0] || ''
              })}
            >
              {trainingTracks.map((track) => (
                <SelectItem key={track} value={track}>
                  {track}
                </SelectItem>
              ))}
            </Select>

            <Select
              label="Difficulty Level"
              placeholder="Select difficulty level"
              selectedKeys={[formData.difficultyLevel]}
              onSelectionChange={(keys) => setFormData({ 
                ...formData, 
                difficultyLevel: Array.from(keys)[0] 
              })}
            >
              {difficultyLevels.map((level) => (
                <SelectItem key={level.key} value={level.key}>
                  {level.label}
                </SelectItem>
              ))}
            </Select>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Submission Details</h3>
              <p className="text-sm text-default-600 mb-4">
                Tell us why this video should be included in our learning center.
              </p>
            </div>

            <Textarea
              label="Why should this video be included? *"
              placeholder="Explain why this video would be valuable for our learning community"
              value={formData.submissionReason}
              onChange={(e) => setFormData({ ...formData, submissionReason: e.target.value })}
              isInvalid={!!errors.submissionReason}
              errorMessage={errors.submissionReason}
              minRows={3}
              isRequired
            />

            <Textarea
              label="Target Audience *"
              placeholder="Who would benefit most from watching this video?"
              value={formData.targetAudience}
              onChange={(e) => setFormData({ ...formData, targetAudience: e.target.value })}
              isInvalid={!!errors.targetAudience}
              errorMessage={errors.targetAudience}
              minRows={2}
              isRequired
            />

            <Textarea
              label="Quality Notes (Optional)"
              placeholder="Any additional notes about the video quality, teaching style, or content"
              value={formData.qualityNotes}
              onChange={(e) => setFormData({ ...formData, qualityNotes: e.target.value })}
              minRows={2}
            />

            <Divider />

            <div>
              <h4 className="font-semibold mb-3">Community Settings</h4>
              <div className="space-y-3">
                <Checkbox
                  isSelected={formData.isPublic}
                  onValueChange={(checked) => setFormData({ ...formData, isPublic: checked })}
                >
                  <div>
                    <div className="font-medium">Make submission public</div>
                    <div className="text-sm text-default-600">
                      Allow the community to see and discuss this submission
                    </div>
                  </div>
                </Checkbox>

                <Checkbox
                  isSelected={formData.allowVoting}
                  onValueChange={(checked) => setFormData({ ...formData, allowVoting: checked })}
                  isDisabled={!formData.isPublic}
                >
                  <div>
                    <div className="font-medium">Enable community voting</div>
                    <div className="text-sm text-default-600">
                      Let the community vote on this video for inclusion in the learning center
                    </div>
                  </div>
                </Checkbox>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="3xl"
      scrollBehavior="inside"
      isDismissable={!loading}
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Submit Educational Video</h2>
          <p className="text-default-600 font-normal">
            Share a great educational video with the Royaltea learning community
          </p>
          
          {/* Progress indicator */}
          <div className="flex items-center gap-2 mt-4">
            {[1, 2, 3].map((stepNum) => (
              <div
                key={stepNum}
                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  stepNum === step
                    ? 'bg-primary text-white'
                    : stepNum < step
                    ? 'bg-success text-white'
                    : 'bg-default-200 text-default-600'
                }`}
              >
                {stepNum < step ? '✓' : stepNum}
              </div>
            ))}
          </div>
        </ModalHeader>
        
        <ModalBody>
          <motion.div
            key={step}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {renderStepContent()}
          </motion.div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            color="danger" 
            variant="flat" 
            onPress={onClose}
            isDisabled={loading}
          >
            Cancel
          </Button>
          
          {step > 1 && (
            <Button
              color="default"
              variant="flat"
              onPress={() => setStep(step - 1)}
              isDisabled={loading}
            >
              Previous
            </Button>
          )}
          
          {step < 3 ? (
            <Button
              color="primary"
              onPress={() => setStep(step + 1)}
              isDisabled={loading}
            >
              Next
            </Button>
          ) : (
            <Button
              color="primary"
              onPress={handleSubmit}
              isLoading={loading}
            >
              Submit Video
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default VideoSubmissionForm;
