import { test, expect } from '@playwright/test';

// Test credentials
const PROJECT_MANAGER = {
  email: '<EMAIL>',
  password: 'ProjectManager123!'
};

const DEVELOPER = {
  email: '<EMAIL>',
  password: 'Developer123!'
};

test.describe('Task Creation → Completion → Royalty Earning Flow', () => {
  test('Complete End-to-End Task and Royalty Flow', async ({ browser }) => {
    // Create two browser contexts for different users
    const managerContext = await browser.newContext();
    const developerContext = await browser.newContext();
    
    const managerPage = await managerContext.newPage();
    const developerPage = await developerContext.newPage();

    try {
      // PART 1: PROJECT MANAGER - Create Project and Tasks
      console.log('🔧 Step 1: Project Manager creates project and tasks');
      
      // Login as project manager
      await managerPage.goto('/login');
      await managerPage.fill('input[type="email"]', PROJECT_MANAGER.email);
      await managerPage.fill('input[type="password"]', PROJECT_MANAGER.password);
      await managerPage.click('button[type="submit"]');
      await managerPage.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });

      // Navigate to Track page
      await managerPage.goto('/track');
      await managerPage.waitForLoadState('networkidle');
      
      // Verify track page loads
      await expect(managerPage.locator('h1:has-text("Track")')).toBeVisible();

      // Create a new project (if needed)
      const createProjectButton = managerPage.locator('button:has-text("Create Project")');
      if (await createProjectButton.isVisible()) {
        await createProjectButton.click();
        await managerPage.waitForTimeout(1000);
        
        // Fill project creation form
        await managerPage.fill('input[label*="Project Name"]', 'E2E Test Project - Task to Royalty');
        await managerPage.fill('textarea[label*="Description"]', 'End-to-end test project for task completion and royalty earning flow');
        await managerPage.selectOption('select[label*="Project Type"]', 'web_development');
        
        // Set revenue sharing configuration
        const revenueCheckbox = managerPage.locator('input[type="checkbox"]:near(text="Enable revenue sharing")');
        if (await revenueCheckbox.isVisible()) {
          await revenueCheckbox.check();
        }
        
        await managerPage.click('button:has-text("Create Project")');
        await managerPage.waitForTimeout(3000);
        
        // Verify project creation success
        const successMessage = managerPage.locator('text=Project created successfully');
        if (await successMessage.isVisible()) {
          console.log('✅ Project created successfully');
        }
      }

      // Create tasks in the project
      const createTaskButton = managerPage.locator('button:has-text("Create Task")').or(managerPage.locator('button:has-text("Add Task")'));
      if (await createTaskButton.isVisible()) {
        await createTaskButton.click();
        await managerPage.waitForTimeout(1000);
        
        // Fill task creation form
        await managerPage.fill('input[label*="Task Title"]', 'Frontend Component Development');
        await managerPage.fill('textarea[label*="Description"]', 'Develop React components for the user dashboard with responsive design');
        await managerPage.selectOption('select[label*="Priority"]', 'high');
        await managerPage.selectOption('select[label*="Difficulty"]', 'intermediate');
        
        // Set task value/points
        const pointsInput = managerPage.locator('input[label*="Points"]').or(managerPage.locator('input[label*="Value"]'));
        if (await pointsInput.isVisible()) {
          await pointsInput.fill('100');
        }
        
        // Assign to developer
        const assigneeSelect = managerPage.locator('select[label*="Assignee"]');
        if (await assigneeSelect.isVisible()) {
          await assigneeSelect.selectOption(DEVELOPER.email);
        }
        
        // Set estimated hours
        const hoursInput = managerPage.locator('input[label*="Estimated Hours"]');
        if (await hoursInput.isVisible()) {
          await hoursInput.fill('8');
        }
        
        await managerPage.click('button:has-text("Create Task")');
        await managerPage.waitForTimeout(2000);
        
        // Verify task creation
        const taskCreatedMessage = managerPage.locator('text=Task created').or(managerPage.locator('text=created successfully'));
        if (await taskCreatedMessage.isVisible()) {
          console.log('✅ Task created successfully');
        }
      }

      // Create a second task for milestone completion
      if (await createTaskButton.isVisible()) {
        await createTaskButton.click();
        await managerPage.waitForTimeout(1000);
        
        await managerPage.fill('input[label*="Task Title"]', 'API Integration');
        await managerPage.fill('textarea[label*="Description"]', 'Integrate backend APIs with frontend components');
        await managerPage.selectOption('select[label*="Priority"]', 'medium');
        
        const pointsInput = managerPage.locator('input[label*="Points"]').or(managerPage.locator('input[label*="Value"]'));
        if (await pointsInput.isVisible()) {
          await pointsInput.fill('75');
        }
        
        await managerPage.click('button:has-text("Create Task")');
        await managerPage.waitForTimeout(2000);
      }

      // PART 2: DEVELOPER - Accept and Complete Tasks
      console.log('👨‍💻 Step 2: Developer accepts and completes tasks');
      
      // Login as developer
      await developerPage.goto('/login');
      await developerPage.fill('input[type="email"]', DEVELOPER.email);
      await developerPage.fill('input[type="password"]', DEVELOPER.password);
      await developerPage.click('button[type="submit"]');
      await developerPage.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });

      // Navigate to Track page
      await developerPage.goto('/track');
      await developerPage.waitForLoadState('networkidle');

      // Find and start working on assigned tasks
      const taskCards = developerPage.locator('[data-testid="task-card"]');
      const taskCount = await taskCards.count();
      
      if (taskCount > 0) {
        // Start timer on first task
        const firstTask = taskCards.first();
        const playButton = firstTask.locator('button[aria-label*="Play"]').or(firstTask.locator('button:has-text("▶")'));
        
        if (await playButton.isVisible()) {
          await playButton.click();
          await developerPage.waitForTimeout(1000);
          
          console.log('✅ Task timer started');
          
          // Simulate work time (wait a few seconds)
          await developerPage.waitForTimeout(3000);
          
          // Stop timer
          const pauseButton = firstTask.locator('button[aria-label*="Pause"]').or(firstTask.locator('button:has-text("⏸")'));
          if (await pauseButton.isVisible()) {
            await pauseButton.click();
            await developerPage.waitForTimeout(1000);
            console.log('✅ Task timer stopped');
          }
        }

        // Move task to "In Progress" if not already
        const todoColumn = developerPage.locator('[data-testid="todo-column"]');
        const inProgressColumn = developerPage.locator('[data-testid="in-progress-column"]');
        
        if (await todoColumn.isVisible() && await inProgressColumn.isVisible()) {
          // Drag task from Todo to In Progress
          const taskToDrag = todoColumn.locator('[data-testid="task-card"]').first();
          if (await taskToDrag.isVisible()) {
            await taskToDrag.dragTo(inProgressColumn);
            await developerPage.waitForTimeout(1000);
            console.log('✅ Task moved to In Progress');
          }
        }

        // Complete the task by moving to Done column
        const doneColumn = developerPage.locator('[data-testid="done-column"]');
        if (await inProgressColumn.isVisible() && await doneColumn.isVisible()) {
          const taskToComplete = inProgressColumn.locator('[data-testid="task-card"]').first();
          if (await taskToComplete.isVisible()) {
            await taskToComplete.dragTo(doneColumn);
            await developerPage.waitForTimeout(2000);
            console.log('✅ Task completed and moved to Done');
            
            // Verify completion notification
            const completionMessage = developerPage.locator('text=Task completed').or(developerPage.locator('text=completed successfully'));
            if (await completionMessage.isVisible()) {
              console.log('✅ Task completion confirmed');
            }
          }
        }

        // Complete second task if available
        const secondTask = taskCards.nth(1);
        if (await secondTask.isVisible()) {
          // Quick completion flow for second task
          const secondPlayButton = secondTask.locator('button[aria-label*="Play"]');
          if (await secondPlayButton.isVisible()) {
            await secondPlayButton.click();
            await developerPage.waitForTimeout(2000);
            
            const secondPauseButton = secondTask.locator('button[aria-label*="Pause"]');
            if (await secondPauseButton.isVisible()) {
              await secondPauseButton.click();
            }
          }
          
          // Move to done
          if (await doneColumn.isVisible()) {
            await secondTask.dragTo(doneColumn);
            await developerPage.waitForTimeout(1000);
            console.log('✅ Second task completed');
          }
        }
      }

      // PART 3: REVENUE GENERATION SIMULATION
      console.log('💰 Step 3: Simulating revenue generation and distribution');
      
      // Switch back to project manager to simulate revenue entry
      await managerPage.goto('/earn');
      await managerPage.waitForLoadState('networkidle');

      // Simulate revenue detection (in real flow, this would be automatic via bank webhooks)
      const addRevenueButton = managerPage.locator('button:has-text("Add Revenue")').or(managerPage.locator('button:has-text("Manual Entry")'));
      if (await addRevenueButton.isVisible()) {
        await addRevenueButton.click();
        await managerPage.waitForTimeout(1000);
        
        // Fill revenue entry form
        await managerPage.fill('input[label*="Amount"]', '5000');
        await managerPage.selectOption('select[label*="Source"]', 'client_payment');
        await managerPage.fill('textarea[label*="Description"]', 'Client payment for completed project milestone');
        
        await managerPage.click('button:has-text("Add Revenue")');
        await managerPage.waitForTimeout(2000);
        
        const revenueAddedMessage = managerPage.locator('text=Revenue added').or(managerPage.locator('text=added successfully'));
        if (await revenueAddedMessage.isVisible()) {
          console.log('✅ Revenue entry created');
        }
      }

      // Configure and trigger revenue distribution
      const distributeButton = managerPage.locator('button:has-text("Start Distribution")').or(managerPage.locator('button:has-text("Distribute Revenue")'));
      if (await distributeButton.isVisible()) {
        await distributeButton.click();
        await managerPage.waitForTimeout(1000);
        
        // Review distribution details
        const distributionModal = managerPage.locator('[role="dialog"]:has-text("Distribution")');
        if (await distributionModal.isVisible()) {
          // Verify contributor calculations
          await expect(managerPage.locator('text=Total Amount')).toBeVisible();
          await expect(managerPage.locator('text=Contributors')).toBeVisible();
          
          // Check if developer is listed as contributor
          const developerContribution = managerPage.locator(`text=${DEVELOPER.email}`);
          if (await developerContribution.isVisible()) {
            console.log('✅ Developer found in contribution list');
          }
          
          // For testing, we'll cancel instead of actually distributing
          await managerPage.click('button:has-text("Cancel")');
        }
      }

      // PART 4: DEVELOPER - Check Earnings
      console.log('💵 Step 4: Developer checks earnings');
      
      // Developer navigates to earnings page
      await developerPage.goto('/earn');
      await developerPage.waitForLoadState('networkidle');

      // Verify earnings dashboard
      await expect(developerPage.locator('h1:has-text("Earn")')).toBeVisible();

      // Check for earnings cards
      const earningsCards = developerPage.locator('[data-testid="earnings-card"]');
      const earningsCount = await earningsCards.count();
      
      if (earningsCount > 0) {
        console.log(`✅ Found ${earningsCount} earnings entries`);
        
        // Check earnings details
        const totalEarnings = developerPage.locator('text=Total Earnings');
        if (await totalEarnings.isVisible()) {
          console.log('✅ Total earnings displayed');
        }
        
        const pendingPayments = developerPage.locator('text=Pending Payments');
        if (await pendingPayments.isVisible()) {
          console.log('✅ Pending payments displayed');
        }
      }

      // Check contribution tracking
      const contributionTab = developerPage.locator('[role="tab"]:has-text("Contributions")');
      if (await contributionTab.isVisible()) {
        await contributionTab.click();
        await developerPage.waitForTimeout(1000);
        
        // Verify contribution history
        const contributionRows = developerPage.locator('[data-testid="contribution-row"]');
        const contributionCount = await contributionRows.count();
        
        if (contributionCount > 0) {
          console.log(`✅ Found ${contributionCount} contribution records`);
          
          // Verify task completion is tracked
          const taskContribution = developerPage.locator('text=Frontend Component Development');
          if (await taskContribution.isVisible()) {
            console.log('✅ Task contribution tracked correctly');
          }
        }
      }

      // PART 5: VERIFY COMPLETE FLOW
      console.log('✅ Step 5: Verifying complete flow integration');
      
      // Back to manager page to verify project completion status
      await managerPage.goto('/track');
      await managerPage.waitForLoadState('networkidle');
      
      // Check project progress
      const projectProgress = managerPage.locator('[data-testid="project-progress"]');
      if (await projectProgress.isVisible()) {
        const progressBar = projectProgress.locator('[role="progressbar"]');
        if (await progressBar.isVisible()) {
          const progressValue = await progressBar.getAttribute('aria-valuenow');
          console.log(`✅ Project progress: ${progressValue}%`);
        }
      }
      
      // Verify completed tasks in done column
      const doneColumn = managerPage.locator('[data-testid="done-column"]');
      if (await doneColumn.isVisible()) {
        const completedTasks = doneColumn.locator('[data-testid="task-card"]');
        const completedCount = await completedTasks.count();
        console.log(`✅ Found ${completedCount} completed tasks`);
      }

      console.log('🎉 Complete Task → Completion → Royalty Earning flow test PASSED');

    } finally {
      // Cleanup
      await managerContext.close();
      await developerContext.close();
    }
  });

  test('Task Milestone and Bonus System', async ({ page }) => {
    // Login as project manager
    await page.goto('/login');
    await page.fill('input[type="email"]', PROJECT_MANAGER.email);
    await page.fill('input[type="password"]', PROJECT_MANAGER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });

    await page.goto('/track');
    await page.waitForLoadState('networkidle');

    // Test milestone creation
    const milestoneButton = page.locator('button:has-text("Create Milestone")');
    if (await milestoneButton.isVisible()) {
      await milestoneButton.click();
      await page.waitForTimeout(1000);
      
      // Fill milestone form
      await page.fill('input[label*="Milestone Name"]', 'MVP Completion');
      await page.fill('textarea[label*="Description"]', 'Complete minimum viable product features');
      await page.fill('input[label*="Bonus Amount"]', '1000');
      
      // Set milestone criteria
      const criteriaTextarea = page.locator('textarea[label*="Completion Criteria"]');
      if (await criteriaTextarea.isVisible()) {
        await criteriaTextarea.fill('All core features implemented and tested');
      }
      
      await page.click('button:has-text("Create Milestone")');
      await page.waitForTimeout(2000);
      
      const milestoneCreated = page.locator('text=Milestone created');
      if (await milestoneCreated.isVisible()) {
        console.log('✅ Milestone created successfully');
      }
    }

    // Test bonus distribution setup
    const bonusButton = page.locator('button:has-text("Bonus Settings")');
    if (await bonusButton.isVisible()) {
      await bonusButton.click();
      await page.waitForTimeout(1000);
      
      const bonusModal = page.locator('[role="dialog"]:has-text("Bonus")');
      if (await bonusModal.isVisible()) {
        // Configure performance bonuses
        const performanceBonus = page.locator('input[label*="Performance Bonus"]');
        if (await performanceBonus.isVisible()) {
          await performanceBonus.fill('10'); // 10% performance bonus
        }
        
        // Configure early completion bonus
        const earlyBonus = page.locator('input[label*="Early Completion"]');
        if (await earlyBonus.isVisible()) {
          await earlyBonus.fill('5'); // 5% early completion bonus
        }
        
        await page.click('button:has-text("Save Settings")');
        await page.waitForTimeout(1000);
        
        await page.click('button:has-text("Close")');
      }
    }

    console.log('✅ Task milestone and bonus system test passed');
  });

  test('Automated Royalty Calculation', async ({ page }) => {
    // Login as project manager
    await page.goto('/login');
    await page.fill('input[type="email"]', PROJECT_MANAGER.email);
    await page.fill('input[type="password"]', PROJECT_MANAGER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });

    await page.goto('/earn');
    await page.waitForLoadState('networkidle');

    // Test automated calculation settings
    const automationButton = page.locator('button:has-text("Automation Settings")');
    if (await automationButton.isVisible()) {
      await automationButton.click();
      await page.waitForTimeout(1000);
      
      const automationModal = page.locator('[role="dialog"]:has-text("Automation")');
      if (await automationModal.isVisible()) {
        // Enable automatic distribution
        const autoDistribute = page.locator('input[type="checkbox"]:near(text="Automatic distribution")');
        if (await autoDistribute.isVisible()) {
          await autoDistribute.check();
        }
        
        // Set distribution threshold
        const thresholdInput = page.locator('input[label*="Threshold"]');
        if (await thresholdInput.isVisible()) {
          await thresholdInput.fill('1000');
        }
        
        // Configure calculation method
        const calculationSelect = page.locator('select[label*="Calculation Method"]');
        if (await calculationSelect.isVisible()) {
          await calculationSelect.selectOption('contribution_based');
        }
        
        await page.click('button:has-text("Save Settings")');
        await page.waitForTimeout(1000);
        
        await page.click('button:has-text("Close")');
      }
    }

    // Test calculation preview
    const previewButton = page.locator('button:has-text("Preview Calculation")');
    if (await previewButton.isVisible()) {
      await previewButton.click();
      await page.waitForTimeout(1000);
      
      const previewModal = page.locator('[role="dialog"]:has-text("Calculation Preview")');
      if (await previewModal.isVisible()) {
        // Verify calculation details
        await expect(page.locator('text=Contribution Breakdown')).toBeVisible();
        await expect(page.locator('text=Distribution Amounts')).toBeVisible();
        
        await page.click('button:has-text("Close")');
      }
    }

    console.log('✅ Automated royalty calculation test passed');
  });
});
