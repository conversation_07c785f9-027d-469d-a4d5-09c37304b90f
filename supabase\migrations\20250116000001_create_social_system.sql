-- Social System Database Schema
-- Complete implementation of social networking features
-- Based on docs/design-system/systems/social-system.md

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enhanced user connections with relationship metadata
CREATE TABLE IF NOT EXISTS public.user_allies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    ally_id UUID REFERENCES auth.users(id) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'blocked')),
    connection_strength INTEGER DEFAULT 1 CHECK (connection_strength BETWEEN 1 AND 10),
    connection_reason VARCHAR(100), -- 'collaboration', 'skill_learning', 'networking', 'industry'
    requested_at TIMESTAMP DEFAULT NOW(),
    accepted_at TIMESTAMP,
    last_interaction_at TIMESTAMP DEFAULT NOW(),
    created_by <PERSON><PERSON><PERSON> REFERENCES auth.users(id) NOT NULL,
    request_message TEXT,
    mutual_connections_count INTEGER DEFAULT 0,
    collaboration_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, ally_id),
    CHECK (user_id != ally_id)
);

-- Direct messaging system
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_user_id UUID REFERENCES auth.users(id) NOT NULL,
    to_user_id UUID REFERENCES auth.users(id) NOT NULL,
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'file', 'project_invite', 'collaboration_request')),
    thread_id UUID, -- For grouping related messages
    reply_to_id UUID REFERENCES messages(id), -- For threaded conversations
    read_at TIMESTAMP,
    delivered_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    attachment_url TEXT,
    attachment_type VARCHAR(50)
);

-- Professional skill endorsements
CREATE TABLE IF NOT EXISTS public.skill_endorsements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endorser_id UUID REFERENCES auth.users(id) NOT NULL,
    endorsed_id UUID REFERENCES auth.users(id) NOT NULL,
    skill_name VARCHAR(255) NOT NULL,
    skill_category VARCHAR(100), -- 'technical', 'design', 'business', 'communication'
    proficiency_level INTEGER CHECK (proficiency_level BETWEEN 1 AND 5),
    endorsement_message TEXT,
    collaboration_context TEXT, -- Which project/context this endorsement is based on
    is_verified BOOLEAN DEFAULT FALSE, -- Verified through actual collaboration
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(endorser_id, endorsed_id, skill_name)
);

-- Collaboration requests and project partnerships
CREATE TABLE IF NOT EXISTS public.collaboration_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID REFERENCES auth.users(id) NOT NULL,
    target_user_id UUID REFERENCES auth.users(id),
    target_audience VARCHAR(50) DEFAULT 'specific' CHECK (target_audience IN ('specific', 'network', 'public')),
    project_title VARCHAR(255) NOT NULL,
    project_description TEXT NOT NULL,
    required_skills TEXT[], -- Array of required skills
    budget_range_min INTEGER,
    budget_range_max INTEGER,
    timeline_weeks INTEGER,
    project_type VARCHAR(50) CHECK (project_type IN ('fixed', 'ongoing', 'hourly')),
    experience_level VARCHAR(50) CHECK (experience_level IN ('beginner', 'intermediate', 'advanced', 'expert')),

    -- Vetting requirements
    minimum_vetting_level INTEGER DEFAULT 0 CHECK (minimum_vetting_level >= 0 AND minimum_vetting_level <= 5),
    required_certifications TEXT[], -- Array of required certifications
    preferred_vetting_level INTEGER CHECK (preferred_vetting_level >= 0 AND preferred_vetting_level <= 5),
    availability_requirement VARCHAR(50) CHECK (availability_requirement IN ('full_time', 'part_time', 'flexible')),
    additional_requirements TEXT,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_review', 'accepted', 'declined', 'completed', 'cancelled')),
    response_deadline TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Network analytics and insights
CREATE TABLE IF NOT EXISTS public.network_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    total_allies INTEGER DEFAULT 0,
    network_score DECIMAL(3,1) DEFAULT 0.0 CHECK (network_score BETWEEN 0.0 AND 10.0),
    network_level VARCHAR(50) DEFAULT 'beginner',
    skill_diversity_score INTEGER DEFAULT 0,
    collaboration_success_rate DECIMAL(5,2) DEFAULT 0.0,
    endorsements_received INTEGER DEFAULT 0,
    endorsements_given INTEGER DEFAULT 0,
    monthly_new_connections INTEGER DEFAULT 0,
    calculated_at TIMESTAMP DEFAULT NOW(),
    analytics_month DATE GENERATED ALWAYS AS (DATE_TRUNC('month', calculated_at)::DATE) STORED,
    UNIQUE(user_id, analytics_month)
);

-- Activity feed for social updates
CREATE TABLE IF NOT EXISTS public.social_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    activity_type VARCHAR(50) NOT NULL CHECK (activity_type IN ('connection_made', 'endorsement_received', 'endorsement_given', 'project_completed', 'skill_verified', 'collaboration_started')),
    related_user_id UUID REFERENCES auth.users(id),
    related_entity_id UUID, -- Could reference projects, skills, etc.
    activity_data JSONB, -- Flexible data storage for activity details
    visibility VARCHAR(20) DEFAULT 'allies' CHECK (visibility IN ('public', 'allies', 'private')),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_user_allies_user_id ON public.user_allies(user_id);
CREATE INDEX IF NOT EXISTS idx_user_allies_ally_id ON public.user_allies(ally_id);
CREATE INDEX IF NOT EXISTS idx_user_allies_status ON public.user_allies(status);
CREATE INDEX IF NOT EXISTS idx_user_allies_created_by ON public.user_allies(created_by);
CREATE INDEX IF NOT EXISTS idx_messages_thread ON public.messages(from_user_id, to_user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_messages_thread_id ON public.messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_messages_read_status ON public.messages(to_user_id, read_at);
CREATE INDEX IF NOT EXISTS idx_skill_endorsements_endorsed ON public.skill_endorsements(endorsed_id);
CREATE INDEX IF NOT EXISTS idx_skill_endorsements_endorser ON public.skill_endorsements(endorser_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_target ON public.collaboration_requests(target_user_id, status);
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_requester ON public.collaboration_requests(requester_id, status);
CREATE INDEX IF NOT EXISTS idx_social_activities_user_feed ON public.social_activities(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_network_analytics_user ON public.network_analytics(user_id, calculated_at DESC);
CREATE INDEX IF NOT EXISTS idx_network_analytics_month ON public.network_analytics(user_id, analytics_month);

-- Enable Row Level Security (RLS)
ALTER TABLE public.user_allies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.skill_endorsements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.network_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_activities ENABLE ROW LEVEL SECURITY;

-- Drop existing policies first
DROP POLICY IF EXISTS "Users can view their own ally connections" ON public.user_allies;
DROP POLICY IF EXISTS "Users can create ally requests" ON public.user_allies;
DROP POLICY IF EXISTS "Users can update their ally connections" ON public.user_allies;
DROP POLICY IF EXISTS "Users can view their own messages" ON public.messages;
DROP POLICY IF EXISTS "Users can send messages to allies" ON public.messages;
DROP POLICY IF EXISTS "Users can update their own messages" ON public.messages;
DROP POLICY IF EXISTS "Users can view endorsements" ON public.skill_endorsements;
DROP POLICY IF EXISTS "Users can create endorsements for allies" ON public.skill_endorsements;
DROP POLICY IF EXISTS "Users can view relevant collaboration requests" ON public.collaboration_requests;
DROP POLICY IF EXISTS "Users can create collaboration requests" ON public.collaboration_requests;
DROP POLICY IF EXISTS "Users can update their collaboration requests" ON public.collaboration_requests;
DROP POLICY IF EXISTS "Users can view their own analytics" ON public.network_analytics;
DROP POLICY IF EXISTS "System can insert analytics" ON public.network_analytics;
DROP POLICY IF EXISTS "System can update analytics" ON public.network_analytics;
DROP POLICY IF EXISTS "Users can view relevant activities" ON public.social_activities;
DROP POLICY IF EXISTS "System can create activities" ON public.social_activities;
DROP POLICY IF EXISTS "Users can view their own applications" ON public.collaboration_request_applications;
DROP POLICY IF EXISTS "Request owners can view applications to their requests" ON public.collaboration_request_applications;
DROP POLICY IF EXISTS "Users can create applications" ON public.collaboration_request_applications;
DROP POLICY IF EXISTS "Users can update their own applications" ON public.collaboration_request_applications;
DROP POLICY IF EXISTS "Request owners can update application status" ON public.collaboration_request_applications;

-- RLS Policies for user_allies
CREATE POLICY "Users can view their own ally connections" ON public.user_allies
    FOR SELECT USING (auth.uid() = user_id OR auth.uid() = ally_id);

CREATE POLICY "Users can create ally requests" ON public.user_allies
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their ally connections" ON public.user_allies
    FOR UPDATE USING (auth.uid() = user_id OR auth.uid() = ally_id);

-- RLS Policies for messages
CREATE POLICY "Users can view their own messages" ON public.messages
    FOR SELECT USING (auth.uid() = from_user_id OR auth.uid() = to_user_id);

CREATE POLICY "Users can send messages to allies" ON public.messages
    FOR INSERT WITH CHECK (auth.uid() = from_user_id);

CREATE POLICY "Users can update their own messages" ON public.messages
    FOR UPDATE USING (auth.uid() = from_user_id OR auth.uid() = to_user_id);

-- RLS Policies for skill_endorsements
CREATE POLICY "Users can view endorsements" ON public.skill_endorsements
    FOR SELECT USING (true); -- Public visibility for professional credibility

CREATE POLICY "Users can create endorsements for allies" ON public.skill_endorsements
    FOR INSERT WITH CHECK (auth.uid() = endorser_id);

-- RLS Policies for collaboration_requests
CREATE POLICY "Users can view relevant collaboration requests" ON public.collaboration_requests
    FOR SELECT USING (
        auth.uid() = requester_id OR
        auth.uid() = target_user_id OR
        target_audience IN ('network', 'public')
    );

CREATE POLICY "Users can create collaboration requests" ON public.collaboration_requests
    FOR INSERT WITH CHECK (auth.uid() = requester_id);

CREATE POLICY "Users can update their collaboration requests" ON public.collaboration_requests
    FOR UPDATE USING (auth.uid() = requester_id OR auth.uid() = target_user_id);

-- RLS Policies for network_analytics
CREATE POLICY "Users can view their own analytics" ON public.network_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert analytics" ON public.network_analytics
    FOR INSERT WITH CHECK (true); -- Allow system-generated analytics

CREATE POLICY "System can update analytics" ON public.network_analytics
    FOR UPDATE USING (true); -- Allow system updates

-- RLS Policies for social_activities
CREATE POLICY "Users can view relevant activities" ON public.social_activities
    FOR SELECT USING (
        auth.uid() = user_id OR
        (visibility = 'public') OR
        (visibility = 'allies' AND EXISTS (
            SELECT 1 FROM public.user_allies
            WHERE (user_id = auth.uid() AND ally_id = social_activities.user_id AND status = 'accepted')
               OR (ally_id = auth.uid() AND user_id = social_activities.user_id AND status = 'accepted')
        ))
    );

CREATE POLICY "System can create activities" ON public.social_activities
    FOR INSERT WITH CHECK (true); -- Allow system-generated activities

-- Trigger functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_user_allies_updated_at BEFORE UPDATE ON public.user_allies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_collaboration_requests_updated_at BEFORE UPDATE ON public.collaboration_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to create social activity when ally connection is made
CREATE OR REPLACE FUNCTION create_ally_connection_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create activity when status changes to 'accepted'
    IF NEW.status = 'accepted' AND (OLD IS NULL OR OLD.status IS NULL OR OLD.status != 'accepted') THEN
        -- Create activity for the user who sent the request
        INSERT INTO public.social_activities (
            user_id,
            activity_type,
            related_user_id,
            related_entity_id,
            activity_data,
            visibility
        ) VALUES (
            NEW.created_by,
            'connection_made',
            CASE WHEN NEW.created_by = NEW.user_id THEN NEW.ally_id ELSE NEW.user_id END,
            NEW.id,
            jsonb_build_object(
                'ally_name', COALESCE((SELECT display_name FROM public.users WHERE id = CASE WHEN NEW.created_by = NEW.user_id THEN NEW.ally_id ELSE NEW.user_id END), 'Unknown'),
                'connection_reason', COALESCE(NEW.connection_reason, 'networking')
            ),
            'allies'
        );

        -- Create activity for the user who accepted the request
        INSERT INTO public.social_activities (
            user_id,
            activity_type,
            related_user_id,
            related_entity_id,
            activity_data,
            visibility
        ) VALUES (
            CASE WHEN NEW.created_by = NEW.user_id THEN NEW.ally_id ELSE NEW.user_id END,
            'connection_made',
            NEW.created_by,
            NEW.id,
            jsonb_build_object(
                'ally_name', COALESCE((SELECT display_name FROM public.users WHERE id = NEW.created_by), 'Unknown'),
                'connection_reason', COALESCE(NEW.connection_reason, 'networking')
            ),
            'allies'
        );
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER ally_connection_activity_trigger
    AFTER INSERT OR UPDATE ON public.user_allies
    FOR EACH ROW EXECUTE FUNCTION create_ally_connection_activity();

-- Function to create activity when endorsement is given
CREATE OR REPLACE FUNCTION create_endorsement_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Create activity for endorsement received
    INSERT INTO public.social_activities (
        user_id,
        activity_type,
        related_user_id,
        related_entity_id,
        activity_data,
        visibility
    ) VALUES (
        NEW.endorsed_id,
        'endorsement_received',
        NEW.endorser_id,
        NEW.id,
        jsonb_build_object(
            'skill_name', NEW.skill_name,
            'endorser_name', COALESCE((SELECT display_name FROM public.users WHERE id = NEW.endorser_id), 'Unknown'),
            'proficiency_level', COALESCE(NEW.proficiency_level, 1)
        ),
        'allies'
    );

    -- Create activity for endorsement given
    INSERT INTO public.social_activities (
        user_id,
        activity_type,
        related_user_id,
        related_entity_id,
        activity_data,
        visibility
    ) VALUES (
        NEW.endorser_id,
        'endorsement_given',
        NEW.endorsed_id,
        NEW.id,
        jsonb_build_object(
            'skill_name', NEW.skill_name,
            'endorsed_name', COALESCE((SELECT display_name FROM public.users WHERE id = NEW.endorsed_id), 'Unknown'),
            'proficiency_level', COALESCE(NEW.proficiency_level, 1)
        ),
        'allies'
    );

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER endorsement_activity_trigger
    AFTER INSERT ON public.skill_endorsements
    FOR EACH ROW EXECUTE FUNCTION create_endorsement_activity();

-- Function to update network analytics
CREATE OR REPLACE FUNCTION update_network_analytics(target_user_id UUID)
RETURNS void AS $$
DECLARE
    ally_count INTEGER;
    endorsements_received_count INTEGER;
    endorsements_given_count INTEGER;
    calculated_score DECIMAL(3,1);
    network_level_text VARCHAR(50);
BEGIN
    -- Count total allies
    SELECT COUNT(*) INTO ally_count
    FROM public.user_allies
    WHERE (user_id = target_user_id OR ally_id = target_user_id) AND status = 'accepted';

    -- Count endorsements received
    SELECT COUNT(*) INTO endorsements_received_count
    FROM public.skill_endorsements
    WHERE endorsed_id = target_user_id;

    -- Count endorsements given
    SELECT COUNT(*) INTO endorsements_given_count
    FROM public.skill_endorsements
    WHERE endorser_id = target_user_id;

    -- Calculate network score (simple algorithm)
    calculated_score := LEAST(10.0, (ally_count * 0.5) + (endorsements_received_count * 0.3) + (endorsements_given_count * 0.2));

    -- Determine network level
    IF calculated_score >= 8.0 THEN
        network_level_text := 'expert';
    ELSIF calculated_score >= 6.0 THEN
        network_level_text := 'advanced';
    ELSIF calculated_score >= 4.0 THEN
        network_level_text := 'intermediate';
    ELSIF calculated_score >= 2.0 THEN
        network_level_text := 'developing';
    ELSE
        network_level_text := 'beginner';
    END IF;

    -- Insert or update analytics
    INSERT INTO public.network_analytics (
        user_id,
        total_allies,
        network_score,
        network_level,
        endorsements_received,
        endorsements_given,
        calculated_at
    ) VALUES (
        target_user_id,
        ally_count,
        calculated_score,
        network_level_text,
        endorsements_received_count,
        endorsements_given_count,
        NOW()
    )
    ON CONFLICT (user_id, analytics_month)
    DO UPDATE SET
        total_allies = EXCLUDED.total_allies,
        network_score = EXCLUDED.network_score,
        network_level = EXCLUDED.network_level,
        endorsements_received = EXCLUDED.endorsements_received,
        endorsements_given = EXCLUDED.endorsements_given,
        calculated_at = EXCLUDED.calculated_at;
END;
$$ language 'plpgsql';

-- Function to trigger analytics update on ally changes
CREATE OR REPLACE FUNCTION trigger_analytics_update()
RETURNS TRIGGER AS $$
BEGIN
    -- Update analytics for both users involved
    IF TG_TABLE_NAME = 'user_allies' THEN
        PERFORM update_network_analytics(NEW.user_id);
        PERFORM update_network_analytics(NEW.ally_id);
    ELSIF TG_TABLE_NAME = 'skill_endorsements' THEN
        PERFORM update_network_analytics(NEW.endorser_id);
        PERFORM update_network_analytics(NEW.endorsed_id);
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for analytics updates
CREATE TRIGGER update_analytics_on_ally_change
    AFTER INSERT OR UPDATE ON public.user_allies
    FOR EACH ROW EXECUTE FUNCTION trigger_analytics_update();

CREATE TRIGGER update_analytics_on_endorsement
    AFTER INSERT ON public.skill_endorsements
    FOR EACH ROW EXECUTE FUNCTION trigger_analytics_update();

-- Collaboration request applications table
CREATE TABLE IF NOT EXISTS public.collaboration_request_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id UUID NOT NULL REFERENCES public.collaboration_requests(id) ON DELETE CASCADE,
    applicant_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Application content
    application_message TEXT NOT NULL,
    proposed_timeline INTEGER, -- Weeks
    proposed_budget_min INTEGER,
    proposed_budget_max INTEGER,
    portfolio_url TEXT,
    relevant_experience TEXT,

    -- Application status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),

    -- Timestamps
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES auth.users(id),

    -- Ensure unique application per user per request
    CONSTRAINT unique_application_per_request UNIQUE (request_id, applicant_id)
);

-- Add missing columns to existing table
ALTER TABLE public.collaboration_request_applications ADD COLUMN IF NOT EXISTS request_id UUID REFERENCES public.collaboration_requests(id) ON DELETE CASCADE;

-- RLS Policies for collaboration_request_applications
CREATE POLICY "Users can view their own applications" ON public.collaboration_request_applications
    FOR SELECT USING (auth.uid() = applicant_id);

CREATE POLICY "Request owners can view applications to their requests" ON public.collaboration_request_applications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.collaboration_requests
            WHERE id = request_id AND requester_id = auth.uid()
        )
    );

CREATE POLICY "Users can create applications" ON public.collaboration_request_applications
    FOR INSERT WITH CHECK (auth.uid() = applicant_id);

CREATE POLICY "Users can update their own applications" ON public.collaboration_request_applications
    FOR UPDATE USING (auth.uid() = applicant_id);

CREATE POLICY "Request owners can update application status" ON public.collaboration_request_applications
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.collaboration_requests
            WHERE id = request_id AND requester_id = auth.uid()
        )
    );

-- Apply updated_at trigger to collaboration_request_applications
CREATE TRIGGER update_collaboration_request_applications_updated_at
    BEFORE UPDATE ON public.collaboration_request_applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE public.user_allies IS 'Professional ally connections with relationship metadata';
COMMENT ON TABLE public.messages IS 'Direct messaging system between allies';
COMMENT ON TABLE public.skill_endorsements IS 'Professional skill endorsements and validations';
COMMENT ON TABLE public.collaboration_requests IS 'Project partnership requests and opportunities';
COMMENT ON TABLE public.collaboration_request_applications IS 'Applications submitted for collaboration requests';
COMMENT ON TABLE public.network_analytics IS 'Network growth and engagement analytics';
COMMENT ON TABLE public.social_activities IS 'Activity feed for social interactions and achievements';
