-- Seed initial skill taxonomy data
-- This migration adds the initial set of skills for the Game Development domain

-- Function to insert skills with proper hierarchy
CREATE OR REPLACE FUNCTION insert_skill_hierarchy(
    p_category TEXT,
    p_area TEXT,
    p_name TEXT,
    p_micro_skill TEXT DEFAULT NULL,
    p_mastery_component TEXT DEFAULT NULL,
    p_description TEXT DEFAULT NULL,
    p_icon TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    parent_id UUID;
    current_id UUID;
BEGIN
    -- Insert or get the parent skill (if applicable)
    IF p_micro_skill IS NOT NULL AND p_mastery_component IS NOT NULL THEN
        -- We're inserting a mastery component, so get the micro-skill parent
        SELECT id INTO parent_id FROM public.skills
        WHERE category = p_category AND area = p_area AND name = p_name
        AND micro_skill = p_micro_skill AND mastery_component IS NULL;
    ELSIF p_micro_skill IS NOT NULL THEN
        -- We're inserting a micro-skill, so get the specific skill parent
        SELECT id INTO parent_id FROM public.skills
        WHERE category = p_category AND area = p_area AND name = p_name
        AND micro_skill IS NULL AND mastery_component IS NULL;
    ELSIF p_name IS NOT NULL THEN
        -- We're inserting a specific skill, so get the skill area parent
        SELECT id INTO parent_id FROM public.skills
        WHERE category = p_category AND area = p_area AND name = 'General'
        AND micro_skill IS NULL AND mastery_component IS NULL;
    ELSIF p_area IS NOT NULL THEN
        -- We're inserting a skill area, so get the category parent
        SELECT id INTO parent_id FROM public.skills
        WHERE category = p_category AND area = 'General' AND name IS NULL
        AND micro_skill IS NULL AND mastery_component IS NULL;
    END IF;

    -- Insert the skill
    INSERT INTO public.skills (
        category,
        area,
        name,
        micro_skill,
        mastery_component,
        description,
        icon,
        parent_id
    ) VALUES (
        p_category,
        p_area,
        COALESCE(p_name, p_area, p_category), -- Use area or category as name if name is NULL
        p_micro_skill,
        p_mastery_component,
        p_description,
        p_icon,
        parent_id
    )
    ON CONFLICT (category, area, name, micro_skill, mastery_component)
    DO UPDATE SET
        description = EXCLUDED.description,
        icon = EXCLUDED.icon,
        parent_id = EXCLUDED.parent_id,
        updated_at = now()
    RETURNING id INTO current_id;

    RETURN current_id;
END;
$$ LANGUAGE plpgsql;

-- Insert top-level skill categories
DO $$
BEGIN
    -- Programming & Development
    PERFORM insert_skill_hierarchy('Programming & Development', 'General', NULL, NULL, NULL, 'Skills related to programming and development in game creation');

    -- Game Engines category
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'General', NULL, NULL, 'Skills related to various game engines');

    -- Unreal Engine skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', NULL, NULL, 'Skills related to Unreal Engine development', 'bi-joystick');

    -- Unreal Engine micro-skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Blueprint Visual Scripting', NULL, 'Visual scripting system in Unreal Engine');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'C++ Programming in Unreal', NULL, 'C++ programming specific to Unreal Engine');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Unreal Material System', NULL, 'Material creation and management in Unreal Engine');

    -- Blueprint Visual Scripting mastery components
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Blueprint Visual Scripting', 'Blueprint Component System', 'Creating and managing components in Blueprint');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Blueprint Visual Scripting', 'Animation Blueprint System', 'Creating and managing animation blueprints');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Blueprint Visual Scripting', 'UI Blueprint Integration', 'Integrating UI elements with Blueprint');

    -- Unity Engine skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unity Engine', NULL, NULL, 'Skills related to Unity Engine development', 'bi-unity');

    -- Unity Engine micro-skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unity Engine', 'C# Programming in Unity', NULL, 'C# programming specific to Unity Engine');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unity Engine', 'Unity Rendering', NULL, 'Rendering systems in Unity Engine');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unity Engine', 'Unity Physics', NULL, 'Physics systems in Unity Engine');

    -- Programming Languages category
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'General', NULL, NULL, 'Skills related to programming languages used in game development');

    -- C++ skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'C++', NULL, NULL, 'Skills related to C++ programming', 'bi-code-slash');

    -- C++ micro-skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'C++', 'C++ Fundamentals', NULL, 'Fundamental concepts of C++ programming');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'C++', 'Modern C++ (11/14/17/20)', NULL, 'Modern C++ features and standards');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'C++', 'STL Library Usage', NULL, 'Standard Template Library usage in C++');

    -- Art & Visual Design category
    PERFORM insert_skill_hierarchy('Art & Visual Design', 'General', NULL, NULL, NULL, 'Skills related to art and visual design in game creation');

    -- 2D Art category
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'General', NULL, NULL, 'Skills related to 2D art creation');

    -- Concept Art skills
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'Concept Art', NULL, NULL, 'Skills related to concept art creation', 'bi-brush');

    -- Concept Art micro-skills
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'Concept Art', 'Character Concept Art', NULL, 'Creating concept art for characters');
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'Concept Art', 'Environment Concept Art', NULL, 'Creating concept art for environments');
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'Concept Art', 'Prop Concept Art', NULL, 'Creating concept art for props');

    -- Add more skills as needed...
END $$;
