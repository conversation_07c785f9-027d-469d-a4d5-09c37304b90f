-- Add invitation tokens to support secure invitation links
-- Migration: Add invitation_token and token_generated_at columns to invitation tables

-- Add invitation token columns to project_contributors table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_contributors') THEN
        ALTER TABLE public.project_contributors
        ADD COLUMN IF NOT EXISTS invitation_token TEXT,
        ADD COLUMN IF NOT EXISTS token_generated_at TIMESTAMPTZ;
    END IF;
END $$;

-- Add invitation token columns to friend_requests table (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'friend_requests') THEN
        ALTER TABLE public.friend_requests
        ADD COLUMN IF NOT EXISTS invitation_token TEXT,
        ADD COLUMN IF NOT EXISTS token_generated_at TIMESTAMPTZ;
    END IF;
END $$;

-- Add invitation token columns to team_invitations table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_invitations') THEN
        ALTER TABLE public.team_invitations 
        ADD COLUMN IF NOT EXISTS invitation_token TEXT,
        ADD COLUMN IF NOT EXISTS token_generated_at TIMESTAMPTZ;
    END IF;
END $$;

-- Add invitation token columns to alliance_invitations table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'alliance_invitations') THEN
        ALTER TABLE public.alliance_invitations 
        ADD COLUMN IF NOT EXISTS invitation_token TEXT,
        ADD COLUMN IF NOT EXISTS token_generated_at TIMESTAMPTZ;
    END IF;
END $$;

-- Add invitation token columns to contributor_invitations table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'contributor_invitations') THEN
        ALTER TABLE public.contributor_invitations 
        ADD COLUMN IF NOT EXISTS invitation_token TEXT,
        ADD COLUMN IF NOT EXISTS token_generated_at TIMESTAMPTZ;
    END IF;
END $$;

-- Create indexes for faster token lookups
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_contributors') THEN
        CREATE INDEX IF NOT EXISTS idx_project_contributors_invitation_token
        ON public.project_contributors(invitation_token)
        WHERE invitation_token IS NOT NULL;
    END IF;
END $$;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'friend_requests') THEN
        CREATE INDEX IF NOT EXISTS idx_friend_requests_invitation_token
        ON public.friend_requests(invitation_token)
        WHERE invitation_token IS NOT NULL;
    END IF;
END $$;

-- Create indexes for other tables if they exist
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_invitations') THEN
        CREATE INDEX IF NOT EXISTS idx_team_invitations_invitation_token 
        ON public.team_invitations(invitation_token) 
        WHERE invitation_token IS NOT NULL;
    END IF;
END $$;

DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'alliance_invitations') THEN
        CREATE INDEX IF NOT EXISTS idx_alliance_invitations_invitation_token 
        ON public.alliance_invitations(invitation_token) 
        WHERE invitation_token IS NOT NULL;
    END IF;
END $$;

DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'contributor_invitations') THEN
        CREATE INDEX IF NOT EXISTS idx_contributor_invitations_invitation_token 
        ON public.contributor_invitations(invitation_token) 
        WHERE invitation_token IS NOT NULL;
    END IF;
END $$;

-- Add comments for documentation
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_contributors') THEN
        COMMENT ON COLUMN public.project_contributors.invitation_token IS 'Secure token for invitation acceptance links';
        COMMENT ON COLUMN public.project_contributors.token_generated_at IS 'Timestamp when the invitation token was generated';
    END IF;
END $$;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'friend_requests') THEN
        COMMENT ON COLUMN public.friend_requests.invitation_token IS 'Secure token for invitation acceptance links';
        COMMENT ON COLUMN public.friend_requests.token_generated_at IS 'Timestamp when the invitation token was generated';
    END IF;
END $$;
