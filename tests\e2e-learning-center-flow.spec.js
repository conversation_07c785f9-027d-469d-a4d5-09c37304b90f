import { test, expect } from '@playwright/test';
import { Eyes, Target, Configuration, BatchInfo } from '@applitools/eyes-playwright';

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const ADMIN_USER = {
  email: '<EMAIL>',
  password: 'AdminPassword123!'
};

// Applitools configuration
const APPLITOOLS_API_KEY = 'N98LseXWBh4rhN7ku0fHh2RkXNiyosMpms0o0o0f2zp8110';

// Global Eyes instance and configuration
let eyes;
let configuration;

test.describe('Learning Center - Complete User Flow', () => {
  test.beforeAll(async () => {
    // Initialize Applitools Eyes configuration
    configuration = new Configuration();
    configuration.setApiKey(APPLITOOLS_API_KEY);
    configuration.setAppName('Royaltea Learning Center');
    configuration.setTestName('Learning Center E2E Flow');

    // Set batch info for grouping tests
    const batchInfo = new BatchInfo('Learning Center E2E Tests');
    batchInfo.setId('learning-center-batch-' + Date.now());
    configuration.setBatch(batchInfo);

    // Configure for responsive testing
    configuration.addBrowser(1200, 800, 'chrome');
    configuration.addBrowser(768, 1024, 'chrome'); // Tablet
    configuration.addBrowser(375, 667, 'chrome');  // Mobile
  });

  test.beforeEach(async ({ page }) => {
    // Initialize Eyes for this test
    eyes = new Eyes();
    eyes.setConfiguration(configuration);

    // Login as regular user
    await page.goto('/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
  });

  test.afterEach(async () => {
    // Close Eyes after each test
    if (eyes) {
      try {
        await eyes.close();
      } catch (error) {
        console.error('Error closing Eyes:', error);
      }
    }
  });

  test('Complete Learning Journey: Discover → Submit → Learn → Progress', async ({ page }) => {
    // Open Eyes and start visual testing
    await eyes.open(page, 'Royaltea Learning Center', 'Complete Learning Journey Flow');

    // Step 1: Navigate to Learning Center
    await page.goto('/learn');
    await page.waitForLoadState('networkidle');

    // Verify learning center loads
    await expect(page.locator('h1:has-text("Learning Center")')).toBeVisible();
    await expect(page.locator('h2:has-text("Smart Suggestions")')).toBeVisible();

    // Visual checkpoint: Learning Center main page
    await eyes.check('Learning Center Main Page', Target.window().fully());

    // Step 2: Browse and interact with suggestions
    const suggestionCards = page.locator('[data-testid="suggestion-card"]');
    const cardCount = await suggestionCards.count();

    if (cardCount > 0) {
      // Test suggestion category switching
      await page.click('[role="tab"]:has-text("Skill Gaps")');
      await page.waitForTimeout(1000);

      // Visual checkpoint: Skill Gaps suggestions
      await eyes.check('Smart Suggestions - Skill Gaps', Target.region(page.locator('.smart-suggestions')));

      await page.click('[role="tab"]:has-text("Trending")');
      await page.waitForTimeout(1000);

      // Visual checkpoint: Trending suggestions
      await eyes.check('Smart Suggestions - Trending', Target.region(page.locator('.smart-suggestions')));
      
      // Enroll in a suggested video
      const enrollButton = suggestionCards.first().locator('button:has-text("Enroll")');
      if (await enrollButton.isVisible()) {
        await enrollButton.click();
        await expect(page.locator('text=Enrolled')).toBeVisible({ timeout: 5000 });
      }
    }

    // Step 3: Submit a video for community review
    await page.click('button:has-text("Submit Video")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();

    // Visual checkpoint: Video submission modal
    await eyes.check('Video Submission Modal - Step 1', Target.region(page.locator('[role="dialog"]')));

    // Fill video submission form
    await page.fill('input[label*="YouTube Video URL"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    await page.waitForTimeout(2000); // Wait for video data to load
    
    await page.fill('input[label*="Title"]', 'E2E Test Video Submission');
    await page.fill('textarea[label*="Description"]', 'This is an end-to-end test video submission for the learning center.');
    await page.fill('input[label*="Channel Name"]', 'Test Channel');
    await page.fill('input[label*="Duration"]', '25');
    
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);

    // Visual checkpoint: Video submission step 2 - Skills and categories
    await eyes.check('Video Submission Modal - Step 2', Target.region(page.locator('[role="dialog"]')));

    // Select skills and categories
    const skillChips = page.locator('[data-testid="skill-chip"]');
    const skillCount = await skillChips.count();
    if (skillCount > 0) {
      await skillChips.first().click();
      if (skillCount > 1) await skillChips.nth(1).click();
    }
    
    const categoryChips = page.locator('[data-testid="category-chip"]');
    const categoryCount = await categoryChips.count();
    if (categoryCount > 0) {
      await categoryChips.first().click();
    }
    
    await page.selectOption('select[label*="Difficulty"]', 'beginner');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);
    
    // Fill additional details
    await page.fill('textarea[label*="Submission Reason"]', 'This video provides excellent educational content for beginners learning web development.');
    await page.fill('input[label*="Target Audience"]', 'Beginner web developers');
    
    // Enable community features
    const publicCheckbox = page.locator('input[type="checkbox"]:near(text="Make submission public")');
    if (await publicCheckbox.isVisible()) {
      await publicCheckbox.check();
    }
    
    const votingCheckbox = page.locator('input[type="checkbox"]:near(text="Enable community voting")');
    if (await votingCheckbox.isVisible()) {
      await votingCheckbox.check();
    }
    
    // Submit the video
    await page.click('button:has-text("Submit")');
    await expect(page.locator('text=submitted successfully')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();

    // Step 4: Create and manage learning queues
    await page.click('button:has-text("My Queues")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('h2:has-text("Learning Queues")')).toBeVisible();

    // Visual checkpoint: Learning Queues modal
    await eyes.check('Learning Queues Modal', Target.region(page.locator('[role="dialog"]')));
    
    // Create a new learning queue
    const newQueueButton = page.locator('button:has-text("New Queue")');
    if (await newQueueButton.isVisible()) {
      await newQueueButton.click();
      await page.waitForTimeout(1000);
      
      // Fill queue creation form
      await page.fill('input[label*="Name"]', 'E2E Test Learning Queue');
      await page.fill('textarea[label*="Description"]', 'A test queue created during end-to-end testing');
      await page.selectOption('select[label*="Difficulty"]', 'beginner');
      await page.fill('input[label*="Duration"]', '15');
      
      // Select skills for the queue
      const queueSkillChips = page.locator('[data-testid="skill-chip"]');
      const queueSkillCount = await queueSkillChips.count();
      if (queueSkillCount > 0) {
        await queueSkillChips.first().click();
      }
      
      await page.click('button:has-text("Create")');
      await page.waitForTimeout(2000);
      
      // Verify queue was created
      const successMessage = page.locator('text=created successfully');
      if (await successMessage.isVisible()) {
        console.log('Learning queue created successfully');
      }
    } else {
      // Handle empty state - create first queue
      const createFirstButton = page.locator('button:has-text("Create your first queue")');
      if (await createFirstButton.isVisible()) {
        await createFirstButton.click();
        await page.fill('input[label*="Name"]', 'My First Learning Queue');
        await page.click('button:has-text("Create")');
        await page.waitForTimeout(2000);
      }
    }

    // Step 5: Browse shared queues
    await page.click('[role="tab"]:has-text("Shared Queues")');
    await page.waitForTimeout(1000);
    
    // Test search functionality
    const searchInput = page.locator('input[placeholder*="Search shared queues"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('JavaScript');
      await page.waitForTimeout(1000);
    }
    
    // Test adding shared queue to personal collection
    const sharedQueueCards = page.locator('[data-testid="shared-queue-card"]');
    const sharedCount = await sharedQueueCards.count();
    
    if (sharedCount > 0) {
      const addButton = page.locator('button:has-text("Add to My Queues")').first();
      if (await addButton.isVisible()) {
        await addButton.click();
        await page.waitForTimeout(2000);
        
        const addedMessage = page.locator('text=added to your collection');
        if (await addedMessage.isVisible()) {
          console.log('Shared queue added successfully');
        }
      }
    }
    
    // Close queues modal
    await page.click('button:has-text("Close")');
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();

    // Step 6: Check My Progress tab
    await page.click('[role="tab"]:has-text("My Progress")');
    await page.waitForTimeout(1000);
    
    // Verify progress dashboard elements
    await expect(page.locator('h2:has-text("Learning Progress")')).toBeVisible();
    await expect(page.locator('text=Completed Videos')).toBeVisible();
    await expect(page.locator('text=In Progress')).toBeVisible();
    await expect(page.locator('text=Average Progress')).toBeVisible();

    // Step 7: Test community voting (if available)
    await page.click('[role="tab"]:has-text("Community")');
    await page.waitForTimeout(1000);
    
    const recommendationCards = page.locator('[data-testid="recommendation-card"]');
    const recCount = await recommendationCards.count();
    
    if (recCount > 0) {
      // Test upvoting a recommendation
      const upvoteButton = page.locator('button:has-text("👍")').first();
      if (await upvoteButton.isVisible()) {
        await upvoteButton.click();
        await page.waitForTimeout(1000);
        console.log('Community voting tested');
      }
    }

    console.log('✅ Complete learning center user flow test passed');
  });

  test('Learning Path Management Flow', async ({ page }) => {
    await page.goto('/learn');
    await page.waitForLoadState('networkidle');
    
    // Navigate to Learning Paths tab
    await page.click('[role="tab"]:has-text("Learning Paths")');
    await page.waitForTimeout(1000);
    
    // Verify learning paths section
    await expect(page.locator('h2:has-text("Official Learning Paths")')).toBeVisible();
    
    // Browse available learning paths
    const pathCards = page.locator('[data-testid="learning-path-card"]');
    const pathCount = await pathCards.count();
    
    if (pathCount > 0) {
      // Enroll in a learning path
      const enrollButton = pathCards.first().locator('button:has-text("Start Path")');
      if (await enrollButton.isVisible()) {
        await enrollButton.click();
        await page.waitForTimeout(2000);
        
        // Verify enrollment success
        const enrolledMessage = page.locator('text=enrolled').or(page.locator('text=started'));
        if (await enrolledMessage.isVisible()) {
          console.log('Learning path enrollment successful');
        }
      }
      
      // View path details
      const viewButton = pathCards.first().locator('button:has-text("View Details")');
      if (await viewButton.isVisible()) {
        await viewButton.click();
        await page.waitForTimeout(1000);
        
        // Check if path details modal opens
        const pathModal = page.locator('[role="dialog"]:has-text("Learning Path")');
        if (await pathModal.isVisible()) {
          await page.click('button:has-text("Close")');
        }
      }
    }
    
    console.log('✅ Learning path management flow test passed');
  });

  test('Video Search and Filtering Flow', async ({ page }) => {
    await page.goto('/learn');
    await page.waitForLoadState('networkidle');
    
    // Test search functionality
    const searchInput = page.locator('input[placeholder*="Search"]');
    await searchInput.fill('React');
    await page.waitForTimeout(2000);
    
    // Verify search results or no results message
    const hasResults = await page.locator('[data-testid="video-card"]').count() > 0;
    const hasNoResults = await page.locator('text=No content found').isVisible();
    
    expect(hasResults || hasNoResults).toBeTruthy();
    
    // Test category filtering
    const categorySelect = page.locator('select').first();
    if (await categorySelect.isVisible()) {
      await categorySelect.selectOption({ index: 1 });
      await page.waitForTimeout(1000);
    }
    
    // Test difficulty filtering
    const difficultySelect = page.locator('select').nth(1);
    if (await difficultySelect.isVisible()) {
      await difficultySelect.selectOption('intermediate');
      await page.waitForTimeout(1000);
    }
    
    // Clear search and verify reset
    await searchInput.clear();
    await page.waitForTimeout(1000);
    
    console.log('✅ Video search and filtering flow test passed');
  });
});

test.describe('Learning Center - Admin Vetting Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('input[type="email"]', ADMIN_USER.email);
    await page.fill('input[type="password"]', ADMIN_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
  });

  test('Admin Video Vetting Workflow', async ({ page }) => {
    // Navigate to admin dashboard
    await page.goto('/admin');
    await page.waitForLoadState('networkidle');
    
    // Access video vetting dashboard
    const vettingTab = page.locator('[role="tab"]:has-text("Video Vetting")');
    if (await vettingTab.isVisible()) {
      await vettingTab.click();
      await page.waitForTimeout(1000);
      
      // Verify vetting dashboard
      await expect(page.locator('h1:has-text("Video Vetting Dashboard")')).toBeVisible();
      await expect(page.locator('text=Pending Review')).toBeVisible();
      
      // Review a video submission
      const reviewButtons = page.locator('button:has-text("Review")');
      const reviewCount = await reviewButtons.count();
      
      if (reviewCount > 0) {
        await reviewButtons.first().click();
        await page.waitForTimeout(1000);
        
        // Fill review form
        const reviewModal = page.locator('[role="dialog"]:has-text("Review Video Submission")');
        if (await reviewModal.isVisible()) {
          await page.selectOption('select[label*="Action"]', 'approved');
          await page.fill('textarea[label*="Review Notes"]', 'Video approved after admin review during E2E testing.');
          
          // Check add to catalog option
          const addToCatalog = page.locator('input[type="checkbox"]:near(text="Add to course catalog")');
          if (await addToCatalog.isVisible()) {
            await addToCatalog.check();
          }
          
          // Submit review (commented out to avoid actual approval)
          // await page.click('button:has-text("Submit Review")');
          // await page.waitForTimeout(2000);
          
          await page.click('button:has-text("Cancel")');
        }
      }
      
      // Test different status filters
      const statusTabs = ['High Priority', 'Auto-Approve Ready', 'Approved', 'Rejected'];
      for (const status of statusTabs) {
        const statusTab = page.locator(`[role="tab"]:has-text("${status}")`);
        if (await statusTab.isVisible()) {
          await statusTab.click();
          await page.waitForTimeout(1000);
        }
      }
    }
    
    console.log('✅ Admin video vetting workflow test passed');
  });

  test('Learning Path Creation Flow', async ({ page }) => {
    await page.goto('/admin');
    await page.waitForLoadState('networkidle');
    
    // Access learning path manager
    const pathsTab = page.locator('[role="tab"]:has-text("Learning Paths")');
    if (await pathsTab.isVisible()) {
      await pathsTab.click();
      await page.waitForTimeout(1000);
      
      // Verify learning path manager
      await expect(page.locator('h1:has-text("Learning Path Manager")')).toBeVisible();
      
      // Create new learning path
      const createButton = page.locator('button:has-text("Create Learning Path")');
      if (await createButton.isVisible()) {
        await createButton.click();
        await page.waitForTimeout(1000);
        
        // Fill learning path form
        await page.fill('input[label*="Title"]', 'E2E Test Learning Path');
        await page.fill('textarea[label*="Description"]', 'A test learning path created during E2E testing');
        await page.selectOption('select[label*="Difficulty"]', 'beginner');
        await page.fill('input[label*="Duration"]', '40');
        
        // Select skills and categories
        const skillChips = page.locator('[data-testid="skill-chip"]');
        if (await skillChips.count() > 0) {
          await skillChips.first().click();
        }
        
        // Submit form (commented out to avoid actual creation)
        // await page.click('button:has-text("Create Path")');
        // await page.waitForTimeout(2000);
        
        await page.click('button:has-text("Cancel")');
      }
    }
    
    console.log('✅ Learning path creation flow test passed');
  });
});
