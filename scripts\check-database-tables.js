// Check Database Tables - Verify what tables exist
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
  console.log('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'Set' : 'Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTables() {
  console.log('🔍 Checking database tables...\n');

  try {
    // Check what tables exist in the public schema
    const { data: tables, error } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          ORDER BY table_name;
        `
      });

    if (error) {
      console.error('❌ Error checking tables:', error);
      return;
    }

    console.log('📋 Tables in public schema:');
    if (tables && tables.length > 0) {
      tables.forEach((row, index) => {
        console.log(`${(index + 1).toString().padStart(2, '0')}. ${row.table_name}`);
      });
    } else {
      console.log('   No tables found');
    }

    // Check specific core tables
    const coreTables = ['projects', 'tasks', 'collaboration_requests', 'teams', 'learning_content'];
    console.log('\n🎯 Core table status:');
    
    for (const tableName of coreTables) {
      const { data: tableExists, error: checkError } = await supabase
        .rpc('exec_sql', {
          sql: `
            SELECT EXISTS (
              SELECT FROM information_schema.tables 
              WHERE table_schema = 'public' 
              AND table_name = '${tableName}'
            ) as exists;
          `
        });

      if (checkError) {
        console.log(`   ❌ ${tableName}: Error checking - ${checkError.message}`);
      } else if (tableExists && tableExists.length > 0) {
        const exists = tableExists[0].exists;
        console.log(`   ${exists ? '✅' : '❌'} ${tableName}: ${exists ? 'EXISTS' : 'MISSING'}`);
        
        // If table exists, check row count
        if (exists) {
          const { data: countData, error: countError } = await supabase
            .rpc('exec_sql', {
              sql: `SELECT COUNT(*) as count FROM ${tableName};`
            });
          
          if (!countError && countData && countData.length > 0) {
            console.log(`      📊 Rows: ${countData[0].count}`);
          }
        }
      }
    }

    // Check learning content specifically
    console.log('\n📚 Learning content check:');
    const { data: learningContent, error: learningError } = await supabase
      .from('learning_content')
      .select('id, title')
      .limit(3);

    if (learningError) {
      console.log(`   ❌ Error: ${learningError.message}`);
    } else if (learningContent) {
      console.log(`   ✅ Found ${learningContent.length} learning content items:`);
      learningContent.forEach((item, index) => {
        console.log(`      ${index + 1}. ${item.title}`);
      });
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function main() {
  console.log('🚀 Database Table Check\n');
  await checkTables();
  console.log('\n✅ Check complete');
}

main().catch(console.error);
