-- Onboarding Integration Schema
-- Enhances user preferences and adds onboarding state tracking

-- Create user_preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Extend user_preferences table for onboarding
ALTER TABLE public.user_preferences
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS onboarding_started_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS onboarding_completed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS onboarding_goal TEXT, -- 'project', 'work', 'learn', 'skip'
ADD COLUMN IF NOT EXISTS onboarding_project_type TEXT,
ADD COLUMN IF NOT EXISTS onboarding_team_choice TEXT, -- 'solo', 'team'
ADD COLUMN IF NOT EXISTS onboarding_experience_level TEXT,
ADD COLUMN IF NOT EXISTS onboarding_time_to_complete INTEGER, -- milliseconds
ADD COLUMN IF NOT EXISTS onboarding_actions_completed JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS onboarding_current_step INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS onboarding_state JSONB DEFAULT '{}'::jsonb;

-- Create onboarding_sessions table for detailed tracking
CREATE TABLE IF NOT EXISTS public.onboarding_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_started_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    session_completed_at TIMESTAMP WITH TIME ZONE,
    user_goal TEXT,
    project_type TEXT,
    team_choice TEXT,
    experience_level TEXT,
    steps_completed JSONB DEFAULT '[]'::jsonb,
    time_per_step JSONB DEFAULT '{}'::jsonb,
    total_time_ms INTEGER,
    completion_path JSONB DEFAULT '{}'::jsonb,
    created_alliance_id UUID REFERENCES public.teams(id),
    created_venture_id UUID REFERENCES public.projects(id),
    exit_point TEXT, -- Where user left if incomplete
    device_info JSONB DEFAULT '{}'::jsonb,
    is_completed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_onboarding_sessions_user_id ON public.onboarding_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_sessions_completed ON public.onboarding_sessions(is_completed);
CREATE INDEX IF NOT EXISTS idx_user_preferences_onboarding ON public.user_preferences(onboarding_completed);

-- Add RLS policies
ALTER TABLE public.onboarding_sessions ENABLE ROW LEVEL SECURITY;

-- Users can only access their own onboarding sessions
CREATE POLICY "Users can view own onboarding sessions" ON public.onboarding_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own onboarding sessions" ON public.onboarding_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own onboarding sessions" ON public.onboarding_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- Create function to initialize user onboarding
CREATE OR REPLACE FUNCTION public.initialize_user_onboarding(user_id UUID)
RETURNS UUID AS $$
DECLARE
    session_id UUID;
BEGIN
    -- Create onboarding session
    INSERT INTO public.onboarding_sessions (user_id, session_started_at)
    VALUES (user_id, now())
    RETURNING id INTO session_id;
    
    -- Update user preferences to mark onboarding as started
    INSERT INTO public.user_preferences (user_id, onboarding_started_at, onboarding_current_step)
    VALUES (user_id, now(), 1)
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        onboarding_started_at = COALESCE(public.user_preferences.onboarding_started_at, now()),
        onboarding_current_step = 1,
        updated_at = now();
    
    RETURN session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update onboarding progress
CREATE OR REPLACE FUNCTION public.update_onboarding_progress(
    user_id UUID,
    session_id UUID,
    step_number INTEGER,
    step_data JSONB DEFAULT '{}'::jsonb
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Update onboarding session
    UPDATE public.onboarding_sessions 
    SET 
        steps_completed = steps_completed || jsonb_build_array(step_number),
        time_per_step = time_per_step || jsonb_build_object(step_number::text, extract(epoch from now()) * 1000),
        completion_path = completion_path || step_data,
        updated_at = now()
    WHERE id = session_id AND user_id = update_onboarding_progress.user_id;
    
    -- Update user preferences
    UPDATE public.user_preferences 
    SET 
        onboarding_current_step = step_number,
        onboarding_state = onboarding_state || step_data,
        updated_at = now()
    WHERE user_id = update_onboarding_progress.user_id;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to complete onboarding
CREATE OR REPLACE FUNCTION public.complete_onboarding(
    user_id UUID,
    session_id UUID,
    completion_data JSONB DEFAULT '{}'::jsonb
)
RETURNS BOOLEAN AS $$
DECLARE
    start_time TIMESTAMP WITH TIME ZONE;
    total_time INTEGER;
BEGIN
    -- Get session start time
    SELECT session_started_at INTO start_time
    FROM public.onboarding_sessions
    WHERE id = session_id AND user_id = complete_onboarding.user_id;

    -- Calculate total time
    total_time := extract(epoch from (now() - start_time)) * 1000;

    -- Update onboarding session as completed
    UPDATE public.onboarding_sessions
    SET
        session_completed_at = now(),
        total_time_ms = total_time,
        completion_path = completion_path || completion_data,
        is_completed = true,
        updated_at = now()
    WHERE id = session_id AND user_id = complete_onboarding.user_id;

    -- Update user preferences
    UPDATE public.user_preferences
    SET
        onboarding_completed = true,
        onboarding_completed_at = now(),
        onboarding_time_to_complete = total_time,
        onboarding_goal = (completion_data->>'userGoal'),
        onboarding_project_type = (completion_data->>'projectType'),
        onboarding_team_choice = (completion_data->>'teamChoice'),
        onboarding_experience_level = (completion_data->>'experienceLevel'),
        updated_at = now()
    WHERE user_id = complete_onboarding.user_id;

    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE public.onboarding_sessions IS 'Tracks detailed onboarding session data for analytics and recovery';
COMMENT ON FUNCTION public.initialize_user_onboarding IS 'Initializes onboarding session and user preferences';
COMMENT ON FUNCTION public.update_onboarding_progress IS 'Updates onboarding progress for a specific step';
COMMENT ON FUNCTION public.complete_onboarding IS 'Marks onboarding as completed and calculates metrics';
