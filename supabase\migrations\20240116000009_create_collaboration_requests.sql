-- Create collaboration_requests table if it doesn't exist
-- This is a standalone migration to ensure the table exists

CREATE TABLE IF NOT EXISTS public.collaboration_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID REFERENCES auth.users(id) NOT NULL,
    target_user_id UUID REFERENCES auth.users(id),
    target_audience VARCHAR(50) DEFAULT 'specific' CHECK (target_audience IN ('specific', 'network', 'public')),
    project_title VARCHAR(255) NOT NULL,
    project_description TEXT NOT NULL,
    required_skills TEXT[], -- Array of required skills
    budget_range_min INTEGER,
    budget_range_max INTEGER,
    timeline_weeks INTEGER,
    project_type VARCHAR(50) CHECK (project_type IN ('fixed', 'ongoing', 'hourly')),
    experience_level VARCHAR(50) CHECK (experience_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    deadline DATE,
    location TEXT,
    is_remote BOOLEAN DEFAULT true
);

-- Create collaboration_request_applications table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.collaboration_request_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    collaboration_request_id UUID REFERENCES public.collaboration_requests(id) ON DELETE CASCADE,
    applicant_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    application_message TEXT,
    proposed_budget DECIMAL(10,2),
    proposed_timeline_weeks INTEGER,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    UNIQUE(collaboration_request_id, applicant_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_requester_id ON public.collaboration_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_status ON public.collaboration_requests(status);
CREATE INDEX IF NOT EXISTS idx_collaboration_request_applications_request_id ON public.collaboration_request_applications(collaboration_request_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_request_applications_applicant_id ON public.collaboration_request_applications(applicant_id);

-- Enable RLS
ALTER TABLE public.collaboration_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_request_applications ENABLE ROW LEVEL SECURITY;

-- RLS Policies
DROP POLICY IF EXISTS "Users can manage their own collaboration requests" ON public.collaboration_requests;
CREATE POLICY "Users can manage their own collaboration requests" ON public.collaboration_requests FOR ALL USING (requester_id = auth.uid());

DROP POLICY IF EXISTS "Users can view public collaboration requests" ON public.collaboration_requests;
CREATE POLICY "Users can view public collaboration requests" ON public.collaboration_requests FOR SELECT USING (target_audience = 'public');

DROP POLICY IF EXISTS "Users can manage their own applications" ON public.collaboration_request_applications;
CREATE POLICY "Users can manage their own applications" ON public.collaboration_request_applications FOR ALL USING (applicant_id = auth.uid());

DROP POLICY IF EXISTS "Request owners can view applications" ON public.collaboration_request_applications;
CREATE POLICY "Request owners can view applications" ON public.collaboration_request_applications FOR SELECT USING (
    collaboration_request_id IN (
        SELECT id FROM public.collaboration_requests WHERE requester_id = auth.uid()
    )
);

COMMENT ON TABLE public.collaboration_requests IS 'Gig requests and collaboration opportunities';
COMMENT ON TABLE public.collaboration_request_applications IS 'Applications for collaboration requests';
