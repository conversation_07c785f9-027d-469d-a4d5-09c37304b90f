-- Alliance & Venture System Migration
-- Backend Specialist: Complete database schema for alliance and venture management

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- ALLIANCE SYSTEM ENHANCEMENTS
-- ============================================================================

-- Add missing alliance-specific columns to teams table
DO $$
BEGIN
    -- Add industry column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'industry') THEN
        ALTER TABLE public.teams ADD COLUMN industry VARCHAR(100);
    END IF;
    
    -- Add business_model column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'business_model') THEN
        ALTER TABLE public.teams ADD COLUMN business_model JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Add max_members column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'max_members') THEN
        ALTER TABLE public.teams ADD COLUMN max_members INTEGER DEFAULT 10;
    END IF;
    
    -- Add is_public column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'is_public') THEN
        ALTER TABLE public.teams ADD COLUMN is_public BOOLEAN DEFAULT true;
    END IF;
    
    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'status') THEN
        ALTER TABLE public.teams ADD COLUMN status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived'));
    END IF;
END $$;

-- Enhance team_members table for alliance roles
DO $$
BEGIN
    -- Add role column if it doesn't exist (replace simple role with enhanced system)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'role') THEN
        ALTER TABLE public.team_members ADD COLUMN role TEXT DEFAULT 'member' CHECK (role IN ('founder', 'owner', 'admin', 'member', 'contributor'));
    END IF;
    
    -- Add permissions column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'permissions') THEN
        ALTER TABLE public.team_members ADD COLUMN permissions JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'status') THEN
        ALTER TABLE public.team_members ADD COLUMN status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending'));
    END IF;
    
    -- Add joined_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'joined_at') THEN
        ALTER TABLE public.team_members ADD COLUMN joined_at TIMESTAMP WITH TIME ZONE DEFAULT now();
    END IF;
    
    -- Add is_admin column if it doesn't exist (for backward compatibility)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'is_admin') THEN
        ALTER TABLE public.team_members ADD COLUMN is_admin BOOLEAN DEFAULT false;
    END IF;
END $$;

-- Create alliance invitations table
CREATE TABLE IF NOT EXISTS public.alliance_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alliance_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    role TEXT DEFAULT 'member' CHECK (role IN ('founder', 'owner', 'admin', 'member', 'contributor')),
    invited_by UUID NOT NULL REFERENCES auth.users(id),
    message TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (now() + INTERVAL '7 days'),
    accepted_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE
);

-- Create alliance preferences table
CREATE TABLE IF NOT EXISTS public.alliance_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alliance_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    invite_members_enabled BOOLEAN DEFAULT true,
    shared_workspace_enabled BOOLEAN DEFAULT true,
    auto_venture_creation BOOLEAN DEFAULT false,
    notification_settings JSONB DEFAULT '{}'::jsonb,
    privacy_settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- VENTURE SYSTEM ENHANCEMENTS
-- ============================================================================

-- Add venture-specific columns to projects table
DO $$
BEGIN
    -- Add venture_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'venture_type') THEN
        ALTER TABLE public.projects ADD COLUMN venture_type TEXT DEFAULT 'software' CHECK (venture_type IN ('software', 'game', 'film', 'music', 'art', 'business', 'research', 'other'));
    END IF;
    
    -- Add revenue_model column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'revenue_model') THEN
        ALTER TABLE public.projects ADD COLUMN revenue_model JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Add milestone_config column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'milestone_config') THEN
        ALTER TABLE public.projects ADD COLUMN milestone_config JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Add alliance_id column if it doesn't exist (rename from team_id)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'alliance_id') THEN
        -- Copy team_id to alliance_id if team_id exists
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'team_id') THEN
            ALTER TABLE public.projects ADD COLUMN alliance_id UUID REFERENCES public.teams(id);
            UPDATE public.projects SET alliance_id = team_id WHERE team_id IS NOT NULL;
        ELSE
            ALTER TABLE public.projects ADD COLUMN alliance_id UUID REFERENCES public.teams(id);
        END IF;
    END IF;
    
    -- Add status column if it doesn't exist (enhance existing status tracking)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'status') THEN
        ALTER TABLE public.projects ADD COLUMN status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled', 'archived'));
    END IF;
END $$;

-- ============================================================================
-- MISSION & BOUNTY SYSTEM ENHANCEMENTS
-- ============================================================================

-- Add mission/bounty specific columns to tasks table
DO $$
BEGIN
    -- Add task_category column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'task_category') THEN
        ALTER TABLE public.tasks ADD COLUMN task_category TEXT DEFAULT 'mission' CHECK (task_category IN ('mission', 'bounty', 'quest', 'task'));
    END IF;

    -- Add bounty_amount column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'bounty_amount') THEN
        ALTER TABLE public.tasks ADD COLUMN bounty_amount DECIMAL(10,2) DEFAULT 0;
    END IF;

    -- Add bounty_currency column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'bounty_currency') THEN
        ALTER TABLE public.tasks ADD COLUMN bounty_currency TEXT DEFAULT 'USD';
    END IF;

    -- Add is_public column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'is_public') THEN
        ALTER TABLE public.tasks ADD COLUMN is_public BOOLEAN DEFAULT false;
    END IF;

    -- Add required_skills column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'required_skills') THEN
        ALTER TABLE public.tasks ADD COLUMN required_skills JSONB DEFAULT '[]'::jsonb;
    END IF;

    -- Add deadline column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'deadline') THEN
        ALTER TABLE public.tasks ADD COLUMN deadline TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add priority column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'priority') THEN
        ALTER TABLE public.tasks ADD COLUMN priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
    END IF;

    -- Add created_by column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'created_by') THEN
        ALTER TABLE public.tasks ADD COLUMN created_by UUID REFERENCES auth.users(id);
    END IF;

    -- Add assigned_to column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'assigned_to') THEN
        ALTER TABLE public.tasks ADD COLUMN assigned_to UUID REFERENCES auth.users(id);
    END IF;
END $$;

-- Create bounty applications table
CREATE TABLE IF NOT EXISTS public.bounty_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
    applicant_id UUID NOT NULL REFERENCES auth.users(id),
    application_message TEXT,
    proposed_timeline TEXT,
    proposed_approach TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES auth.users(id)
);

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Alliance system indexes
CREATE INDEX IF NOT EXISTS idx_teams_alliance_type ON public.teams(alliance_type);
CREATE INDEX IF NOT EXISTS idx_teams_industry ON public.teams(industry);
CREATE INDEX IF NOT EXISTS idx_teams_status ON public.teams(status);
CREATE INDEX IF NOT EXISTS idx_teams_is_public ON public.teams(is_public);

CREATE INDEX IF NOT EXISTS idx_team_members_role ON public.team_members(role);
CREATE INDEX IF NOT EXISTS idx_team_members_status ON public.team_members(status);
CREATE INDEX IF NOT EXISTS idx_team_members_user_team ON public.team_members(user_id, team_id);

CREATE INDEX IF NOT EXISTS idx_alliance_invitations_email ON public.alliance_invitations(email);
CREATE INDEX IF NOT EXISTS idx_alliance_invitations_status ON public.alliance_invitations(status);
CREATE INDEX IF NOT EXISTS idx_alliance_invitations_alliance ON public.alliance_invitations(alliance_id);

-- Venture system indexes
CREATE INDEX IF NOT EXISTS idx_projects_alliance_id ON public.projects(alliance_id);
CREATE INDEX IF NOT EXISTS idx_projects_venture_type ON public.projects(venture_type);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON public.projects(created_by);

-- Mission/bounty system indexes
CREATE INDEX IF NOT EXISTS idx_tasks_category ON public.tasks(task_category);
CREATE INDEX IF NOT EXISTS idx_tasks_is_public ON public.tasks(is_public);
CREATE INDEX IF NOT EXISTS idx_tasks_priority ON public.tasks(priority);
CREATE INDEX IF NOT EXISTS idx_tasks_deadline ON public.tasks(deadline);
CREATE INDEX IF NOT EXISTS idx_tasks_created_by ON public.tasks(created_by);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON public.tasks(assigned_to);

CREATE INDEX IF NOT EXISTS idx_bounty_applications_task ON public.bounty_applications(task_id);
CREATE INDEX IF NOT EXISTS idx_bounty_applications_applicant ON public.bounty_applications(applicant_id);
CREATE INDEX IF NOT EXISTS idx_bounty_applications_status ON public.bounty_applications(status);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on new tables
ALTER TABLE public.alliance_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alliance_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bounty_applications ENABLE ROW LEVEL SECURITY;

-- Alliance invitations policies
CREATE POLICY "Users can view invitations sent to them" ON public.alliance_invitations
    FOR SELECT USING (
        email = (SELECT email FROM auth.users WHERE id = auth.uid()) OR
        invited_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = alliance_id AND tm.user_id = auth.uid() AND tm.role IN ('founder', 'owner', 'admin')
        )
    );

CREATE POLICY "Alliance admins can create invitations" ON public.alliance_invitations
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = alliance_id AND tm.user_id = auth.uid() AND tm.role IN ('founder', 'owner', 'admin')
        )
    );

CREATE POLICY "Users can update their own invitations" ON public.alliance_invitations
    FOR UPDATE USING (
        email = (SELECT email FROM auth.users WHERE id = auth.uid()) OR
        invited_by = auth.uid()
    );

-- Alliance preferences policies
CREATE POLICY "Alliance members can view preferences" ON public.alliance_preferences
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = alliance_id AND tm.user_id = auth.uid()
        )
    );

CREATE POLICY "Alliance admins can manage preferences" ON public.alliance_preferences
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = alliance_id AND tm.user_id = auth.uid() AND tm.role IN ('founder', 'owner', 'admin')
        )
    );

-- Bounty applications policies
CREATE POLICY "Users can view applications for their tasks" ON public.bounty_applications
    FOR SELECT USING (
        applicant_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.tasks t
            WHERE t.id = task_id AND t.created_by = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM public.tasks t
            JOIN public.projects p ON t.project_id = p.id
            JOIN public.team_members tm ON p.alliance_id = tm.team_id
            WHERE t.id = task_id AND tm.user_id = auth.uid() AND tm.role IN ('founder', 'owner', 'admin')
        )
    );

CREATE POLICY "Users can create applications for public bounties" ON public.bounty_applications
    FOR INSERT WITH CHECK (
        applicant_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.tasks t
            WHERE t.id = task_id AND t.is_public = true AND t.task_category = 'bounty'
        )
    );

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.alliance_invitations TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.alliance_preferences TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.bounty_applications TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.alliance_invitations IS 'Invitations to join alliances with role-based access';
COMMENT ON TABLE public.alliance_preferences IS 'Alliance-specific configuration and preferences';
COMMENT ON TABLE public.bounty_applications IS 'Applications for public bounty tasks';

COMMENT ON COLUMN public.teams.alliance_type IS 'Type of alliance: emerging, established, solo';
COMMENT ON COLUMN public.teams.business_model IS 'JSON configuration for revenue sharing and business rules';
COMMENT ON COLUMN public.projects.venture_type IS 'Type of venture: software, game, film, music, art, business, research, other';
COMMENT ON COLUMN public.projects.revenue_model IS 'JSON configuration for venture-specific revenue distribution';
COMMENT ON COLUMN public.tasks.task_category IS 'Category: mission (internal), bounty (public), quest (gamified), task (simple)';
COMMENT ON COLUMN public.tasks.bounty_amount IS 'Reward amount for bounty completion';

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify alliance system tables
SELECT 'Alliance system tables created' as status,
       COUNT(*) as table_count
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('teams', 'team_members', 'alliance_invitations', 'alliance_preferences');

-- Verify venture system enhancements
SELECT 'Venture system columns added' as status,
       COUNT(*) as column_count
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'projects'
AND column_name IN ('venture_type', 'revenue_model', 'alliance_id', 'status');

-- Verify mission/bounty system enhancements
SELECT 'Mission/bounty system ready' as status,
       COUNT(*) as column_count
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'tasks'
AND column_name IN ('task_category', 'bounty_amount', 'is_public', 'priority');
