-- Create Profile System Tables
-- This migration adds support for the Myspace-inspired profile system

-- First, check if we need to update the users table
DO $$
BEGIN
    -- Add new columns to the profiles table for profile customization
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS headline TEXT;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS location TEXT;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS website TEXT;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS cover_image_url TEXT;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS status_message TEXT;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS availability_status TEXT;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS profile_views INTEGER DEFAULT 0;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS theme_settings JSONB DEFAULT '{
        "theme": "default",
        "colors": {
            "background": "#f8fafc",
            "primary": "#3b82f6",
            "secondary": "#f0f2f5",
            "text": "#1c1e21",
            "accent": "#6c5ce7",
            "links": "#3b82f6",
            "borders": "#e2e8f0"
        },
        "fonts": {
            "heading": "Inter",
            "body": "Inter"
        },
        "layout": "standard"
    }'::jsonb;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS custom_css TEXT;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS profile_song_url TEXT;
    ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS privacy_settings JSONB DEFAULT '{
        "profile_visibility": "public",
        "section_visibility": {
            "personal_info": true,
            "contact_details": false,
            "skills": true,
            "projects": true,
            "contribution_details": true,
            "contribution_percentages": false,
            "royalty_info": false,
            "profile_song": true,
            "top_collaborators": true,
            "comments": true,
            "profile_visitors": false
        },
        "verification_display": "level_only"
    }'::jsonb;
    
    RAISE NOTICE 'Updated users table with profile system columns';
END $$;

-- Create profile_comments table
CREATE TABLE IF NOT EXISTS public.profile_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create index for faster comment lookups
CREATE INDEX IF NOT EXISTS idx_profile_comments_profile ON public.profile_comments(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_comments_author ON public.profile_comments(author_id);

-- Create profile_views table to track who viewed a profile
CREATE TABLE IF NOT EXISTS public.profile_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    viewer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ip_address TEXT,
    user_agent TEXT
);

-- Create index for faster profile view lookups
CREATE INDEX IF NOT EXISTS idx_profile_views_profile ON public.profile_views(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_views_viewer ON public.profile_views(viewer_id);

-- Create top_collaborators table
CREATE TABLE IF NOT EXISTS public.top_collaborators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    collaborator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, collaborator_id)
);

-- Create index for faster collaborator lookups
CREATE INDEX IF NOT EXISTS idx_top_collaborators_user ON public.top_collaborators(user_id);

-- Create profile_themes table for preset themes
CREATE TABLE IF NOT EXISTS public.profile_themes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    thumbnail_url TEXT,
    colors JSONB NOT NULL,
    fonts JSONB NOT NULL,
    css_template TEXT,
    is_premium BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(name)
);

-- Add RLS policies for profile_comments
ALTER TABLE public.profile_comments ENABLE ROW LEVEL SECURITY;

-- Anyone can view approved comments
CREATE POLICY "Anyone can view approved comments" 
ON public.profile_comments
FOR SELECT
TO authenticated
USING (is_approved = true OR profile_id = auth.uid() OR author_id = auth.uid());

-- Users can add comments to profiles
CREATE POLICY "Users can add comments" 
ON public.profile_comments
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Users can update their own comments
CREATE POLICY "Users can update their own comments" 
ON public.profile_comments
FOR UPDATE
TO authenticated
USING (author_id = auth.uid());

-- Users can delete their own comments or comments on their profile
CREATE POLICY "Users can delete comments" 
ON public.profile_comments
FOR DELETE
TO authenticated
USING (author_id = auth.uid() OR profile_id = auth.uid());

-- Add RLS policies for profile_views
ALTER TABLE public.profile_views ENABLE ROW LEVEL SECURITY;

-- Users can view profile views on their own profile
CREATE POLICY "Users can view their profile views" 
ON public.profile_views
FOR SELECT
TO authenticated
USING (profile_id = auth.uid());

-- Anyone can insert profile views
CREATE POLICY "Anyone can insert profile views" 
ON public.profile_views
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Add RLS policies for top_collaborators
ALTER TABLE public.top_collaborators ENABLE ROW LEVEL SECURITY;

-- Anyone can view top collaborators
CREATE POLICY "Anyone can view top collaborators" 
ON public.top_collaborators
FOR SELECT
TO authenticated
USING (true);

-- Users can manage their own top collaborators
CREATE POLICY "Users can manage their top collaborators" 
ON public.top_collaborators
FOR ALL
TO authenticated
USING (user_id = auth.uid());

-- Add RLS policies for profile_themes
ALTER TABLE public.profile_themes ENABLE ROW LEVEL SECURITY;

-- Anyone can view profile themes
CREATE POLICY "Anyone can view profile themes" 
ON public.profile_themes
FOR SELECT
TO authenticated
USING (true);

-- Only admins can manage profile themes
CREATE POLICY "Only admins can manage profile themes" 
ON public.profile_themes
FOR ALL
TO authenticated
USING (
    auth.uid() IS NOT NULL
);

-- Insert default profile themes
INSERT INTO public.profile_themes (name, description, thumbnail_url, colors, fonts, css_template, is_premium)
VALUES
(
    'Classic Myspace',
    'The nostalgic blue and white theme reminiscent of early social networks',
    '/themes/classic-myspace.jpg',
    '{
        "background": "#ffffff",
        "primary": "#006FDA",
        "secondary": "#f0f2f5",
        "text": "#000000",
        "accent": "#FF6600",
        "links": "#006FDA",
        "borders": "#D4D4D4"
    }',
    '{
        "heading": "Arial",
        "body": "Arial"
    }',
    '/* Classic Myspace Theme CSS */',
    false
),
(
    'Dark Mode',
    'A sleek dark theme that''s easy on the eyes',
    '/themes/dark-mode.jpg',
    '{
        "background": "#121212",
        "primary": "#BB86FC",
        "secondary": "#1F1F1F",
        "text": "#FFFFFF",
        "accent": "#03DAC6",
        "links": "#BB86FC",
        "borders": "#333333"
    }',
    '{
        "heading": "Inter",
        "body": "Inter"
    }',
    '/* Dark Mode Theme CSS */',
    false
),
(
    'Synthwave',
    'A vibrant retro theme inspired by 80s aesthetics',
    '/themes/synthwave.jpg',
    '{
        "background": "#241734",
        "primary": "#F72585",
        "secondary": "#2D1D42",
        "text": "#FFFFFF",
        "accent": "#4CC9F0",
        "links": "#F72585",
        "borders": "#4361EE"
    }',
    '{
        "heading": "Orbitron",
        "body": "Roboto"
    }',
    '/* Synthwave Theme CSS */',
    true
),
(
    'Minimalist',
    'A clean, simple theme with minimal distractions',
    '/themes/minimalist.jpg',
    '{
        "background": "#FFFFFF",
        "primary": "#000000",
        "secondary": "#F5F5F5",
        "text": "#333333",
        "accent": "#666666",
        "links": "#000000",
        "borders": "#EEEEEE"
    }',
    '{
        "heading": "Montserrat",
        "body": "Open Sans"
    }',
    '/* Minimalist Theme CSS */',
    false
)
ON CONFLICT (name) DO NOTHING;
