-- Test Content System Fix
-- Quick verification that the content system is working properly

-- Test 1: Check if all required tables exist
SELECT 
    'Table Existence Check' as test_name,
    CASE 
        WHEN COUNT(*) = 7 THEN 'PASS'
        ELSE 'FAIL - Missing tables'
    END as result,
    COUNT(*) as tables_found
FROM information_schema.tables 
WHERE table_name IN (
    'learning_content', 
    'content_categories', 
    'content_series', 
    'content_interactions', 
    'content_progress', 
    'external_content_sources', 
    'content_import_jobs'
);

-- Test 2: Check if content categories were inserted
SELECT 
    'Content Categories Check' as test_name,
    CASE 
        WHEN COUNT(*) >= 8 THEN 'PASS'
        ELSE 'FAIL - Missing categories'
    END as result,
    COUNT(*) as categories_found
FROM public.content_categories;

-- Test 3: Check if external sources were inserted
SELECT 
    'External Sources Check' as test_name,
    CASE 
        WHEN COUNT(*) >= 5 THEN 'PASS'
        ELSE 'FAIL - Missing sources'
    END as result,
    COUNT(*) as sources_found
FROM public.external_content_sources;

-- Test 4: Check foreign key constraints
SELECT 
    'Foreign Key Constraints Check' as test_name,
    CASE 
        WHEN COUNT(*) >= 3 THEN 'PASS'
        ELSE 'FAIL - Missing constraints'
    END as result,
    COUNT(*) as constraints_found
FROM information_schema.table_constraints 
WHERE constraint_name IN (
    'learning_content_category_id_fkey',
    'learning_content_series_id_fkey', 
    'learning_content_parent_content_id_fkey'
);

-- Test 5: Try to insert a test content item
DO $$ 
DECLARE
    test_category_id UUID;
    test_content_id UUID;
BEGIN
    -- Get a category ID
    SELECT id INTO test_category_id 
    FROM public.content_categories 
    WHERE slug = 'web-development' 
    LIMIT 1;
    
    IF test_category_id IS NOT NULL THEN
        -- Try to insert test content
        INSERT INTO public.learning_content (
            title,
            slug,
            description,
            content_type,
            status,
            content_body,
            difficulty_level,
            estimated_read_time_minutes,
            category_id,
            author_name,
            published_at
        ) VALUES (
            'Test Content Creation',
            'test-content-creation-' || extract(epoch from now())::text,
            'This is a test content item to verify the system is working.',
            'article',
            'published',
            '# Test Content\n\nThis is a test to verify that content creation is working properly.',
            'beginner',
            2,
            test_category_id,
            'System Test',
            NOW()
        ) RETURNING id INTO test_content_id;
        
        -- Clean up test content
        DELETE FROM public.learning_content WHERE id = test_content_id;
        
        RAISE NOTICE 'Content Creation Test: PASS - Content can be created and deleted successfully';
    ELSE
        RAISE NOTICE 'Content Creation Test: FAIL - No category found for testing';
    END IF;
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Content Creation Test: FAIL - Error: %', SQLERRM;
END $$;

-- Test 6: Check if content interactions table is working
DO $$ 
BEGIN
    -- Try to query content interactions (should not fail even if empty)
    PERFORM COUNT(*) FROM public.content_interactions;
    RAISE NOTICE 'Content Interactions Test: PASS - Table is accessible';
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Content Interactions Test: FAIL - Error: %', SQLERRM;
END $$;

-- Test 7: Check if content progress table is working
DO $$ 
BEGIN
    -- Try to query content progress (should not fail even if empty)
    PERFORM COUNT(*) FROM public.content_progress;
    RAISE NOTICE 'Content Progress Test: PASS - Table is accessible';
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Content Progress Test: FAIL - Error: %', SQLERRM;
END $$;

-- Test 8: Check if import jobs table is working
DO $$ 
BEGIN
    -- Try to query import jobs (should not fail even if empty)
    PERFORM COUNT(*) FROM public.content_import_jobs;
    RAISE NOTICE 'Import Jobs Test: PASS - Table is accessible';
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Import Jobs Test: FAIL - Error: %', SQLERRM;
END $$;

-- Final summary
SELECT 
    'CONTENT SYSTEM STATUS' as summary,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name IN ('learning_content', 'content_categories', 'content_series', 'content_interactions', 'content_progress', 'external_content_sources', 'content_import_jobs')
        ) = 7 
        AND (SELECT COUNT(*) FROM public.content_categories) >= 8
        AND (SELECT COUNT(*) FROM public.external_content_sources) >= 5
        THEN '✅ READY FOR PRODUCTION'
        ELSE '⚠️ NEEDS ATTENTION'
    END as status;
