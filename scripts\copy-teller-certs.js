#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔐 Copying Teller certificates for deployment...');

// Source paths
const sourceCertPath = path.join(__dirname, '..', 'teller', 'certificate.pem');
const sourceKeyPath = path.join(__dirname, '..', 'teller', 'private_key.pem');

// Destination paths
const destDir = path.join(__dirname, '..', 'netlify', 'functions', 'teller');
const destCertPath = path.join(destDir, 'certificate.pem');
const destKeyPath = path.join(destDir, 'private_key.pem');

try {
  // Create destination directory if it doesn't exist
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
    console.log('📁 Created teller directory in functions');
  }

  // Check if source files exist
  if (!fs.existsSync(sourceCertPath)) {
    throw new Error(`Certificate not found at: ${sourceCertPath}`);
  }
  
  if (!fs.existsSync(sourceKeyPath)) {
    throw new Error(`Private key not found at: ${sourceKeyPath}`);
  }

  // Copy certificate files
  fs.copyFileSync(sourceCertPath, destCertPath);
  fs.copyFileSync(sourceKeyPath, destKeyPath);

  console.log('✅ Certificate copied successfully');
  console.log('✅ Private key copied successfully');
  
  // Verify the copies
  const certStats = fs.statSync(destCertPath);
  const keyStats = fs.statSync(destKeyPath);
  
  console.log(`📊 Certificate size: ${certStats.size} bytes`);
  console.log(`📊 Private key size: ${keyStats.size} bytes`);
  
  console.log('🎉 Teller certificates ready for deployment!');

} catch (error) {
  console.error('❌ Error copying Teller certificates:', error.message);
  process.exit(1);
}
