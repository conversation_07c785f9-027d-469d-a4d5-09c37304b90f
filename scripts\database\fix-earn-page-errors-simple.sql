-- Simple Fix for Earn Page Database Errors
-- This script creates only the essential missing tables

-- ============================================================================
-- CREATE MISSING PAYMENT_TRANSACTIONS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Transaction parties
    from_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Transaction details
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    payment_method TEXT DEFAULT 'ach_standard',
    
    -- Transaction status
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    transaction_id TEXT,
    
    -- Transaction metadata
    description TEXT,
    reference_id TEXT,
    reference_type TEXT,
    
    -- Timing information
    initiated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Fee information
    platform_fee DECIMAL(10,2) DEFAULT 0,
    total_fees DECIMAL(10,2) DEFAULT 0,
    
    -- Error handling
    failure_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- CREATE MISSING ESCROW_ACCOUNTS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.escrow_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Account parties
    depositor_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    beneficiary_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Account details
    balance DECIMAL(15,2) DEFAULT 0,
    currency TEXT DEFAULT 'USD',
    
    -- Account status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'frozen', 'closed')),
    
    -- Account metadata
    project_id UUID,
    description TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- CREATE MISSING COLLABORATION_REQUESTS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.collaboration_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Request details
    title TEXT NOT NULL,
    description TEXT,
    project_type TEXT DEFAULT 'software',
    
    -- Requirements
    required_skills TEXT[],
    experience_level TEXT DEFAULT 'intermediate' CHECK (experience_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    estimated_hours INTEGER,
    
    -- Compensation
    budget_min DECIMAL(10,2),
    budget_max DECIMAL(10,2),
    payment_type TEXT DEFAULT 'fixed' CHECK (payment_type IN ('fixed', 'hourly', 'milestone', 'equity')),
    
    -- Request metadata
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    project_id UUID,
    
    -- Status and timing
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'completed', 'cancelled')),
    deadline DATE,
    
    -- Location and remote work
    location TEXT,
    is_remote BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- CREATE MISSING REVENUE_ENTRIES TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.revenue_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Revenue details
    project_id UUID NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    
    -- Revenue metadata
    source TEXT,
    description TEXT,
    
    -- Status and verification
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'verified', 'disputed', 'cancelled')),
    verified_by UUID,
    verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Timing
    revenue_date DATE DEFAULT CURRENT_DATE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- ADD MISSING COLUMNS TO EXISTING TABLES
-- ============================================================================

-- Add missing columns to projects table if they don't exist
DO $$ 
BEGIN
    -- Add total_revenue column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'total_revenue') THEN
        ALTER TABLE public.projects ADD COLUMN total_revenue DECIMAL(15,2) DEFAULT 0;
    END IF;
    
    -- Add revenue_distribution_model column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'revenue_distribution_model') THEN
        ALTER TABLE public.projects ADD COLUMN revenue_distribution_model TEXT DEFAULT 'equal_split';
    END IF;
END $$;

-- ============================================================================
-- ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.escrow_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.revenue_entries ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- CREATE RLS POLICIES
-- ============================================================================

-- Payment transactions policies
DROP POLICY IF EXISTS "Users can view their payment transactions" ON public.payment_transactions;
CREATE POLICY "Users can view their payment transactions" ON public.payment_transactions
    FOR SELECT USING (auth.uid() = from_user_id OR auth.uid() = to_user_id);

DROP POLICY IF EXISTS "Users can create payment transactions" ON public.payment_transactions;
CREATE POLICY "Users can create payment transactions" ON public.payment_transactions
    FOR INSERT WITH CHECK (auth.uid() = from_user_id);

-- Escrow accounts policies
DROP POLICY IF EXISTS "Users can view their escrow accounts" ON public.escrow_accounts;
CREATE POLICY "Users can view their escrow accounts" ON public.escrow_accounts
    FOR SELECT USING (auth.uid() = depositor_user_id OR auth.uid() = beneficiary_user_id);

-- Collaboration requests policies
DROP POLICY IF EXISTS "Anyone can view open collaboration requests" ON public.collaboration_requests;
CREATE POLICY "Anyone can view open collaboration requests" ON public.collaboration_requests
    FOR SELECT USING (status = 'open' OR auth.uid() = created_by);

DROP POLICY IF EXISTS "Users can create collaboration requests" ON public.collaboration_requests;
CREATE POLICY "Users can create collaboration requests" ON public.collaboration_requests
    FOR INSERT WITH CHECK (auth.uid() = created_by);

DROP POLICY IF EXISTS "Users can update their collaboration requests" ON public.collaboration_requests;
CREATE POLICY "Users can update their collaboration requests" ON public.collaboration_requests
    FOR UPDATE USING (auth.uid() = created_by);

-- Revenue entries policies
DROP POLICY IF EXISTS "Users can view revenue entries" ON public.revenue_entries;
CREATE POLICY "Users can view revenue entries" ON public.revenue_entries
    FOR SELECT USING (true); -- Allow all users to view for now

DROP POLICY IF EXISTS "Users can manage revenue entries" ON public.revenue_entries;
CREATE POLICY "Users can manage revenue entries" ON public.revenue_entries
    FOR ALL USING (true); -- Allow all users to manage for now

-- ============================================================================
-- CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_payment_transactions_from_user ON public.payment_transactions(from_user_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_to_user ON public.payment_transactions(to_user_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON public.payment_transactions(status);

CREATE INDEX IF NOT EXISTS idx_collaboration_requests_created_by ON public.collaboration_requests(created_by);
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_status ON public.collaboration_requests(status);

CREATE INDEX IF NOT EXISTS idx_revenue_entries_project_id ON public.revenue_entries(project_id);
CREATE INDEX IF NOT EXISTS idx_revenue_entries_status ON public.revenue_entries(status);

-- Add table comments for documentation
COMMENT ON TABLE public.payment_transactions IS 'Records all payment transactions between users';
COMMENT ON TABLE public.escrow_accounts IS 'Manages escrow accounts for secure project funding';
COMMENT ON TABLE public.collaboration_requests IS 'Stores gigwork and collaboration opportunity requests';
COMMENT ON TABLE public.revenue_entries IS 'Tracks revenue entries for projects and royalty calculations';
