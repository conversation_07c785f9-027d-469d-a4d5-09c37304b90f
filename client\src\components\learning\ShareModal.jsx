import React, { useState, useContext } from 'react';
import { 
  <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON>dal<PERSON>eader, <PERSON>dalBody, <PERSON>dal<PERSON>ooter,
  Button, Input, Textarea, Select, SelectItem, Chip, Card, CardBody,
  Tabs, Tab, Divider, Avatar, Checkbox
} from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  Share2, Copy, Mail, MessageCircle, Users, ExternalLink, 
  Facebook, Twitter, Linkedin, Send, Link, QrCode 
} from 'lucide-react';

/**
 * Share Modal Component
 * 
 * Comprehensive sharing functionality for learning content including
 * videos, learning paths, and progress sharing with multiple platforms
 * and internal user sharing.
 */
const ShareModal = ({ 
  isOpen, 
  onClose, 
  content, 
  contentType = 'video', // 'video', 'learning_path', 'progress'
  userProgress = null 
}) => {
  const { currentUser } = useContext(UserContext);
  const [activeTab, setActiveTab] = useState('link');
  const [loading, setLoading] = useState(false);
  const [shareForm, setShareForm] = useState({
    message: '',
    recipients: [],
    includeProgress: false,
    isPublic: true,
    allowComments: true
  });
  const [searchUsers, setSearchUsers] = useState('');
  const [foundUsers, setFoundUsers] = useState([]);

  // Generate share URL
  const getShareUrl = () => {
    const baseUrl = window.location.origin;
    switch (contentType) {
      case 'video':
        return `${baseUrl}/learn?video=${content.external_id || content.id}`;
      case 'learning_path':
        return `${baseUrl}/learn/path/${content.id}`;
      case 'progress':
        return `${baseUrl}/learn/progress/${currentUser.id}`;
      default:
        return `${baseUrl}/learn`;
    }
  };

  // Generate share text
  const getShareText = () => {
    switch (contentType) {
      case 'video':
        return `Check out this educational video: "${content.title}" on Royaltea Learning Center`;
      case 'learning_path':
        return `Explore this learning path: "${content.title}" on Royaltea - ${content.description}`;
      case 'progress':
        return `Check out my learning progress on Royaltea! I've been making great strides in my skill development.`;
      default:
        return 'Check out Royaltea Learning Center for amazing educational content!';
    }
  };

  // Copy to clipboard
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  // Share via Web Share API
  const shareNative = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: getShareText(),
          text: shareForm.message || getShareText(),
          url: getShareUrl()
        });
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Error sharing:', error);
          toast.error('Failed to share');
        }
      }
    } else {
      copyToClipboard(`${getShareText()} - ${getShareUrl()}`);
    }
  };

  // Share to social platforms
  const shareToSocial = (platform) => {
    const url = getShareUrl();
    const text = encodeURIComponent(getShareText());
    const encodedUrl = encodeURIComponent(url);

    let shareUrl = '';
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${text}&url=${encodedUrl}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=${encodeURIComponent('Check this out on Royaltea')}&body=${text}%0A%0A${encodedUrl}`;
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  // Search for users to share with
  const searchForUsers = async (query) => {
    if (!query.trim()) {
      setFoundUsers([]);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, avatar_url')
        .or(`full_name.ilike.%${query}%,email.ilike.%${query}%`)
        .neq('id', currentUser.id)
        .limit(10);

      if (error) throw error;
      setFoundUsers(data || []);
    } catch (error) {
      console.error('Error searching users:', error);
    }
  };

  // Add user to recipients
  const addRecipient = (user) => {
    if (!shareForm.recipients.find(r => r.id === user.id)) {
      setShareForm({
        ...shareForm,
        recipients: [...shareForm.recipients, user]
      });
    }
    setSearchUsers('');
    setFoundUsers([]);
  };

  // Remove recipient
  const removeRecipient = (userId) => {
    setShareForm({
      ...shareForm,
      recipients: shareForm.recipients.filter(r => r.id !== userId)
    });
  };

  // Send internal share
  const sendInternalShare = async () => {
    if (shareForm.recipients.length === 0) {
      toast.error('Please select at least one recipient');
      return;
    }

    try {
      setLoading(true);

      const shareData = {
        shared_by: currentUser.id,
        content_type: contentType,
        content_id: content.id,
        content_title: content.title,
        content_url: getShareUrl(),
        message: shareForm.message.trim(),
        include_progress: shareForm.includeProgress,
        is_public: shareForm.isPublic,
        allow_comments: shareForm.allowComments,
        recipients: shareForm.recipients.map(r => r.id)
      };

      // Create share record
      const { data: share, error: shareError } = await supabase
        .from('content_shares')
        .insert([shareData])
        .select()
        .single();

      if (shareError) throw shareError;

      // Create notifications for recipients
      const notifications = shareForm.recipients.map(recipient => ({
        user_id: recipient.id,
        type: 'content_share',
        title: `${currentUser.user_metadata?.full_name || currentUser.email} shared content with you`,
        message: `"${content.title}" - ${shareForm.message || 'Check this out!'}`,
        data: {
          share_id: share.id,
          content_type: contentType,
          content_id: content.id,
          shared_by: currentUser.id,
          content_url: getShareUrl()
        }
      }));

      const { error: notificationError } = await supabase
        .from('notifications')
        .insert(notifications);

      if (notificationError) throw notificationError;

      toast.success(`Content shared with ${shareForm.recipients.length} user(s)!`);
      onClose();

    } catch (error) {
      console.error('Error sharing content:', error);
      toast.error('Failed to share content');
    } finally {
      setLoading(false);
    }
  };

  if (!content) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="3xl" scrollBehavior="inside">
      <ModalContent>
        <ModalHeader>
          <div className="flex items-center gap-3">
            <Share2 className="w-5 h-5" />
            <div>
              <h3 className="text-lg font-semibold">Share Content</h3>
              <p className="text-sm text-default-600 font-normal">
                {content.title}
              </p>
            </div>
          </div>
        </ModalHeader>
        <ModalBody>
          <Tabs selectedKey={activeTab} onSelectionChange={setActiveTab}>
            <Tab key="link" title={
              <div className="flex items-center gap-2">
                <Link className="w-4 h-4" />
                <span>Share Link</span>
              </div>
            }>
              <div className="space-y-4">
                {/* Quick Share Options */}
                <div>
                  <h4 className="font-semibold mb-3">Quick Share</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="flat"
                      onPress={shareNative}
                      startContent={<Share2 className="w-4 h-4" />}
                    >
                      Share
                    </Button>
                    <Button
                      variant="flat"
                      onPress={() => copyToClipboard(getShareUrl())}
                      startContent={<Copy className="w-4 h-4" />}
                    >
                      Copy Link
                    </Button>
                  </div>
                </div>

                <Divider />

                {/* Social Media Sharing */}
                <div>
                  <h4 className="font-semibold mb-3">Share on Social Media</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="flat"
                      color="primary"
                      onPress={() => shareToSocial('twitter')}
                      startContent={<Twitter className="w-4 h-4" />}
                    >
                      Twitter
                    </Button>
                    <Button
                      variant="flat"
                      color="primary"
                      onPress={() => shareToSocial('facebook')}
                      startContent={<Facebook className="w-4 h-4" />}
                    >
                      Facebook
                    </Button>
                    <Button
                      variant="flat"
                      color="primary"
                      onPress={() => shareToSocial('linkedin')}
                      startContent={<Linkedin className="w-4 h-4" />}
                    >
                      LinkedIn
                    </Button>
                    <Button
                      variant="flat"
                      onPress={() => shareToSocial('email')}
                      startContent={<Mail className="w-4 h-4" />}
                    >
                      Email
                    </Button>
                  </div>
                </div>

                <Divider />

                {/* Direct Link */}
                <div>
                  <h4 className="font-semibold mb-3">Direct Link</h4>
                  <div className="flex gap-2">
                    <Input
                      value={getShareUrl()}
                      readOnly
                      className="flex-1"
                    />
                    <Button
                      isIconOnly
                      variant="flat"
                      onPress={() => copyToClipboard(getShareUrl())}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </Tab>

            <Tab key="users" title={
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                <span>Share with Users</span>
              </div>
            }>
              <div className="space-y-4">
                {/* Search Users */}
                <div>
                  <Input
                    label="Search Users"
                    placeholder="Search by name or email..."
                    value={searchUsers}
                    onChange={(e) => {
                      setSearchUsers(e.target.value);
                      searchForUsers(e.target.value);
                    }}
                    startContent={<Users className="w-4 h-4 text-default-400" />}
                  />
                  
                  {/* Search Results */}
                  {foundUsers.length > 0 && (
                    <div className="mt-2 max-h-40 overflow-y-auto border rounded-lg">
                      {foundUsers.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center gap-3 p-3 hover:bg-default-50 cursor-pointer"
                          onClick={() => addRecipient(user)}
                        >
                          <Avatar
                            size="sm"
                            src={user.avatar_url}
                            name={user.full_name || user.email}
                          />
                          <div>
                            <div className="font-medium">{user.full_name || 'Unknown'}</div>
                            <div className="text-sm text-default-600">{user.email}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Selected Recipients */}
                {shareForm.recipients.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2">Recipients ({shareForm.recipients.length})</h4>
                    <div className="flex flex-wrap gap-2">
                      {shareForm.recipients.map((recipient) => (
                        <Chip
                          key={recipient.id}
                          onClose={() => removeRecipient(recipient.id)}
                          avatar={
                            <Avatar
                              size="sm"
                              src={recipient.avatar_url}
                              name={recipient.full_name || recipient.email}
                            />
                          }
                        >
                          {recipient.full_name || recipient.email}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}

                {/* Message */}
                <Textarea
                  label="Message (Optional)"
                  placeholder="Add a personal message..."
                  value={shareForm.message}
                  onChange={(e) => setShareForm({ ...shareForm, message: e.target.value })}
                  minRows={3}
                />

                {/* Share Options */}
                <div className="space-y-2">
                  {userProgress && (
                    <Checkbox
                      isSelected={shareForm.includeProgress}
                      onValueChange={(checked) => setShareForm({ ...shareForm, includeProgress: checked })}
                    >
                      Include my progress
                    </Checkbox>
                  )}
                  <Checkbox
                    isSelected={shareForm.allowComments}
                    onValueChange={(checked) => setShareForm({ ...shareForm, allowComments: checked })}
                  >
                    Allow comments on shared content
                  </Checkbox>
                </div>
              </div>
            </Tab>
          </Tabs>
        </ModalBody>
        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Cancel
          </Button>
          {activeTab === 'users' && (
            <Button
              color="primary"
              onPress={sendInternalShare}
              isLoading={loading}
              isDisabled={shareForm.recipients.length === 0}
              startContent={<Send className="w-4 h-4" />}
            >
              Share with Users
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ShareModal;
