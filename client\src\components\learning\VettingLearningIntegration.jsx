import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Chip, Progress, Modal, ModalContent, 
  ModalHeader, ModalBody, ModalFooter, useDisclosure, Select, SelectItem,
  Input, Textarea, Tabs, Tab, Badge, Divider
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  Shield, Star, BookOpen, Target, TrendingUp, Award, 
  CheckCircle, Clock, Users, Zap, ArrowRight
} from 'lucide-react';

/**
 * Vetting Learning Integration Component
 * 
 * Connects learning content with the 6-level vetting system:
 * Level 0: Unverified - Basic learning content
 * Level 1: Learning - Foundational education content
 * Level 2: Peer Verified - Community-validated content
 * Level 3: Project Verified - Professional-level content
 * Level 4: Expert Verified - Advanced content
 * Level 5: Master Verified - Master-level content
 */
const VettingLearningIntegration = ({ 
  selectedVideo = null, 
  userSkillLevel = 0, 
  targetSkill = null,
  onSuggestForVetting = null 
}) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [userSkills, setUserSkills] = useState([]);
  const [vettingProgress, setVettingProgress] = useState({});
  const [suggestedContent, setSuggestedContent] = useState([]);
  
  const { isOpen: isSuggestOpen, onOpen: onSuggestOpen, onClose: onSuggestClose } = useDisclosure();
  
  const [suggestionForm, setSuggestionForm] = useState({
    videoId: '',
    vettingLevel: 1,
    targetSkills: [],
    justification: '',
    estimatedHours: '',
    prerequisites: [],
    learningObjectives: []
  });

  // Vetting level definitions with learning integration
  const vettingLevels = [
    {
      level: 0,
      name: 'Unverified',
      color: 'default',
      icon: '⚪',
      description: 'Basic learning content for exploration',
      learningFocus: 'General tutorials and introductory content',
      requirements: 'No specific requirements',
      contentTypes: ['Basic tutorials', 'Overview videos', 'Getting started guides']
    },
    {
      level: 1,
      name: 'Learning',
      color: 'warning',
      icon: '🟡',
      description: 'Foundational education content for beginners',
      learningFocus: 'Structured learning paths and fundamentals',
      requirements: 'Clear educational value, beginner-friendly explanations',
      contentTypes: ['Structured courses', 'Fundamentals', 'Step-by-step tutorials']
    },
    {
      level: 2,
      name: 'Peer Verified',
      color: 'secondary',
      icon: '🟠',
      description: 'Community-validated intermediate content',
      learningFocus: 'Practical skills and hands-on projects',
      requirements: 'Peer review, practical examples, project-based learning',
      contentTypes: ['Project tutorials', 'Code reviews', 'Best practices']
    },
    {
      level: 3,
      name: 'Project Verified',
      color: 'success',
      icon: '🟢',
      description: 'Professional-level content with real-world applications',
      learningFocus: 'Industry standards and professional practices',
      requirements: 'Real-world applications, industry best practices',
      contentTypes: ['Professional workflows', 'Industry case studies', 'Advanced techniques']
    },
    {
      level: 4,
      name: 'Expert Verified',
      color: 'primary',
      icon: '🔵',
      description: 'Advanced content for experienced professionals',
      learningFocus: 'Expert insights and advanced problem-solving',
      requirements: 'Expert validation, cutting-edge techniques',
      contentTypes: ['Expert masterclasses', 'Advanced architecture', 'Innovation patterns']
    },
    {
      level: 5,
      name: 'Master Verified',
      color: 'danger',
      icon: '🟣',
      description: 'Master-level content for industry leaders',
      learningFocus: 'Thought leadership and innovation',
      requirements: 'Master-level expertise, thought leadership',
      contentTypes: ['Thought leadership', 'Innovation frameworks', 'Industry transformation']
    }
  ];

  useEffect(() => {
    if (currentUser) {
      loadUserVettingData();
    }
  }, [currentUser]);

  const loadUserVettingData = async () => {
    try {
      setLoading(true);

      // Load user skill levels
      const { data: skillLevels, error: skillError } = await supabase
        .from('user_skill_levels')
        .select('*')
        .eq('user_id', currentUser.id);

      if (skillError) throw skillError;

      setUserSkills(skillLevels || []);

      // Load vetting progress
      const { data: vettingData, error: vettingError } = await supabase
        .from('vetting_applications')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      if (vettingError) throw vettingError;

      // Process vetting progress
      const progress = {};
      vettingData?.forEach(app => {
        if (!progress[app.technology] || progress[app.technology].level < app.target_level) {
          progress[app.technology] = {
            level: app.target_level,
            status: app.status,
            created_at: app.created_at
          };
        }
      });

      setVettingProgress(progress);

      // Load suggested content based on user's current level
      loadSuggestedContent(skillLevels);

    } catch (error) {
      console.error('Error loading vetting data:', error);
      toast.error('Failed to load vetting data');
    } finally {
      setLoading(false);
    }
  };

  const loadSuggestedContent = async (skillLevels) => {
    try {
      // Get user's current skill levels
      const userLevels = {};
      skillLevels?.forEach(skill => {
        userLevels[skill.technology] = skill.current_level;
      });

      // Load content suggestions based on current levels
      const { data: suggestions, error } = await supabase
        .from('video_submissions')
        .select(`
          *,
          votes:video_votes(vote_type),
          reviews:video_reviews(rating)
        `)
        .eq('status', 'approved')
        .eq('suggest_for_vetting', true)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      // Filter and rank suggestions based on user's level
      const rankedSuggestions = suggestions?.map(video => {
        const targetLevel = video.suggested_vetting_level || 1;
        const userLevel = userLevels[video.vetting_skills?.[0]] || 0;
        
        // Calculate relevance score
        let relevanceScore = 0;
        if (targetLevel === userLevel + 1) relevanceScore += 10; // Next level
        if (targetLevel === userLevel) relevanceScore += 5; // Current level
        if (video.vetting_skills?.some(skill => userLevels[skill] !== undefined)) relevanceScore += 3;
        
        return {
          ...video,
          relevanceScore,
          isNextLevel: targetLevel === userLevel + 1,
          isCurrentLevel: targetLevel === userLevel
        };
      }).sort((a, b) => b.relevanceScore - a.relevanceScore) || [];

      setSuggestedContent(rankedSuggestions);

    } catch (error) {
      console.error('Error loading suggested content:', error);
    }
  };

  const handleSuggestVideoForVetting = async () => {
    if (!suggestionForm.videoId || !suggestionForm.justification) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);

      const { error } = await supabase
        .from('vetting_content_suggestions')
        .insert({
          user_id: currentUser.id,
          video_id: suggestionForm.videoId,
          suggested_vetting_level: suggestionForm.vettingLevel,
          target_skills: suggestionForm.targetSkills,
          justification: suggestionForm.justification,
          estimated_completion_hours: parseInt(suggestionForm.estimatedHours) || null,
          prerequisites: suggestionForm.prerequisites,
          learning_objectives: suggestionForm.learningObjectives,
          status: 'pending_review'
        });

      if (error) throw error;

      toast.success('Video suggested for vetting review!');
      onSuggestClose();
      
      // Reset form
      setSuggestionForm({
        videoId: '',
        vettingLevel: 1,
        targetSkills: [],
        justification: '',
        estimatedHours: '',
        prerequisites: [],
        learningObjectives: []
      });

    } catch (error) {
      console.error('Error suggesting video for vetting:', error);
      toast.error('Failed to submit vetting suggestion');
    } finally {
      setLoading(false);
    }
  };

  const getSkillLevel = (technology) => {
    const skill = userSkills.find(s => s.technology === technology);
    return skill?.current_level || 0;
  };

  const getVettingLevelInfo = (level) => {
    return vettingLevels.find(v => v.level === level) || vettingLevels[0];
  };

  const renderVettingLevelCard = (levelInfo) => (
    <Card key={levelInfo.level} className="h-full">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <span className="text-2xl">{levelInfo.icon}</span>
            <div>
              <h3 className="font-semibold">{levelInfo.name}</h3>
              <p className="text-sm text-default-600">Level {levelInfo.level}</p>
            </div>
          </div>
          <Chip color={levelInfo.color} variant="flat" size="sm">
            {levelInfo.level === userSkillLevel ? 'Current' : 
             levelInfo.level === userSkillLevel + 1 ? 'Next' : ''}
          </Chip>
        </div>
      </CardHeader>
      <CardBody className="pt-0">
        <p className="text-sm text-default-700 mb-3">{levelInfo.description}</p>
        <div className="space-y-2">
          <div>
            <p className="text-xs font-medium text-default-600 mb-1">Learning Focus:</p>
            <p className="text-xs text-default-500">{levelInfo.learningFocus}</p>
          </div>
          <div>
            <p className="text-xs font-medium text-default-600 mb-1">Content Types:</p>
            <div className="flex flex-wrap gap-1">
              {levelInfo.contentTypes.map((type, index) => (
                <Chip key={index} size="sm" variant="flat" className="text-xs">
                  {type}
                </Chip>
              ))}
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );

  return (
    <div className="vetting-learning-integration space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="text-primary" />
            Vetting & Learning Integration
          </h2>
          <p className="text-default-600">
            Progress through our 6-level verification system with curated learning content
          </p>
        </div>
        <Button
          color="primary"
          startContent={<Target />}
          onClick={onSuggestOpen}
        >
          Suggest Content for Vetting
        </Button>
      </div>

      {/* Vetting Levels Overview */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Award className="text-warning" />
            Vetting Level Framework
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {vettingLevels.map(renderVettingLevelCard)}
          </div>
        </CardBody>
      </Card>

      {/* Suggested Content */}
      {suggestedContent.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <BookOpen className="text-success" />
              Recommended Learning Content
            </h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              {suggestedContent.slice(0, 5).map((video, index) => (
                <div key={video.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <img
                    src={video.thumbnail_url || `https://img.youtube.com/vi/${video.video_id}/mqdefault.jpg`}
                    alt={video.title}
                    className="w-20 h-12 object-cover rounded"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium">{video.title}</h4>
                    <p className="text-sm text-default-600 line-clamp-2">{video.description}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Chip 
                        color={getVettingLevelInfo(video.suggested_vetting_level).color}
                        size="sm"
                        variant="flat"
                      >
                        {getVettingLevelInfo(video.suggested_vetting_level).name}
                      </Chip>
                      {video.isNextLevel && (
                        <Chip color="success" size="sm" variant="flat">
                          Next Level
                        </Chip>
                      )}
                    </div>
                  </div>
                  <Button
                    color="primary"
                    variant="flat"
                    size="sm"
                    endContent={<ArrowRight size={16} />}
                    onClick={() => window.open(video.video_url, '_blank')}
                  >
                    Learn
                  </Button>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Suggest Content Modal */}
      <Modal isOpen={isSuggestOpen} onClose={onSuggestClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            <h2 className="text-xl font-bold">Suggest Content for Vetting</h2>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Video URL or ID"
                placeholder="https://youtube.com/watch?v=..."
                value={suggestionForm.videoId}
                onChange={(e) => setSuggestionForm(prev => ({
                  ...prev,
                  videoId: e.target.value
                }))}
              />
              
              <Select
                label="Suggested Vetting Level"
                placeholder="Select vetting level"
                selectedKeys={[suggestionForm.vettingLevel.toString()]}
                onSelectionChange={(keys) => setSuggestionForm(prev => ({
                  ...prev,
                  vettingLevel: parseInt(Array.from(keys)[0])
                }))}
              >
                {vettingLevels.slice(1).map((level) => (
                  <SelectItem key={level.level.toString()} value={level.level}>
                    {level.icon} {level.name} - {level.description}
                  </SelectItem>
                ))}
              </Select>

              <Textarea
                label="Justification"
                placeholder="Explain why this content should be included at this vetting level..."
                value={suggestionForm.justification}
                onChange={(e) => setSuggestionForm(prev => ({
                  ...prev,
                  justification: e.target.value
                }))}
                minRows={3}
              />

              <Input
                label="Estimated Completion Time (hours)"
                type="number"
                placeholder="2"
                value={suggestionForm.estimatedHours}
                onChange={(e) => setSuggestionForm(prev => ({
                  ...prev,
                  estimatedHours: e.target.value
                }))}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onSuggestClose}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={handleSuggestVideoForVetting}
              isLoading={loading}
            >
              Submit Suggestion
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default VettingLearningIntegration;
