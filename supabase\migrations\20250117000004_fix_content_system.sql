-- Fix Content System Migration
-- Addresses foreign key and column issues in the comprehensive content system

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create content categories table first (if not exists)
CREATE TABLE IF NOT EXISTS public.content_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50), -- Icon class or emoji
    color VARCHAR(7), -- Hex color code
    parent_category_id UUID,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraint for parent_category_id after table creation
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'content_categories_parent_category_id_fkey'
    ) THEN
        ALTER TABLE public.content_categories 
        ADD CONSTRAINT content_categories_parent_category_id_fkey 
        FOREIGN KEY (parent_category_id) REFERENCES public.content_categories(id);
    END IF;
END $$;

-- Create content series table (if not exists)
CREATE TABLE IF NOT EXISTS public.content_series (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL UNIQUE,
    description TEXT,
    thumbnail_url TEXT,
    total_parts INTEGER DEFAULT 0,
    estimated_total_time_minutes INTEGER,
    difficulty_level VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    skills_covered TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add missing columns first
ALTER TABLE public.learning_content ADD COLUMN IF NOT EXISTS parent_content_id UUID;
ALTER TABLE public.learning_content ADD COLUMN IF NOT EXISTS slug TEXT;
ALTER TABLE public.content_categories ADD COLUMN IF NOT EXISTS slug VARCHAR(100);
ALTER TABLE public.content_categories ADD COLUMN IF NOT EXISTS parent_category_id UUID;
ALTER TABLE public.content_series ADD COLUMN IF NOT EXISTS slug VARCHAR(100);

-- Add foreign key constraints to learning_content table if they don't exist
DO $$
BEGIN
    -- Add category_id foreign key if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'learning_content_category_id_fkey'
    ) THEN
        ALTER TABLE public.learning_content 
        ADD CONSTRAINT learning_content_category_id_fkey 
        FOREIGN KEY (category_id) REFERENCES public.content_categories(id);
    END IF;
    
    -- Add series_id foreign key if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'learning_content_series_id_fkey'
    ) THEN
        ALTER TABLE public.learning_content 
        ADD CONSTRAINT learning_content_series_id_fkey 
        FOREIGN KEY (series_id) REFERENCES public.content_series(id);
    END IF;
    
    -- Add parent_content_id foreign key if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'learning_content_parent_content_id_fkey'
    ) THEN
        ALTER TABLE public.learning_content 
        ADD CONSTRAINT learning_content_parent_content_id_fkey 
        FOREIGN KEY (parent_content_id) REFERENCES public.learning_content(id);
    END IF;
END $$;

-- Insert default content categories (if they don't exist)
INSERT INTO public.content_categories (name, slug, description, icon, color) VALUES
('Royaltea Platform', 'royaltea-platform', 'Learn about Royaltea features, revenue sharing, and project management', '👑', '#6366f1'),
('Revenue Sharing', 'revenue-sharing', 'Understanding royalty models, tranche systems, and payment distribution', '💰', '#10b981'),
('Project Management', 'project-management', 'Managing projects, teams, and contributors effectively', '📊', '#f59e0b'),
('Web Development', 'web-development', 'Frontend, backend, and full-stack development tutorials', '💻', '#3b82f6'),
('Design & UX', 'design-ux', 'UI/UX design, visual design, and user experience principles', '🎨', '#ec4899'),
('Business & Strategy', 'business-strategy', 'Business development, strategy, and entrepreneurship', '📈', '#8b5cf6'),
('Tools & Workflows', 'tools-workflows', 'Development tools, workflows, and productivity tips', '🛠️', '#06b6d4'),
('Community & Collaboration', 'community-collaboration', 'Building communities, team collaboration, and networking', '🤝', '#84cc16')
ON CONFLICT (slug) DO NOTHING;

-- Insert external content sources (if they don't exist)
INSERT INTO public.external_content_sources (name, base_url, supported_content_types, import_method) VALUES
('Unreal Engine Learning', 'https://dev.epicgames.com', ARRAY['tutorial', 'guide', 'article'], 'api'),
('Medium', 'https://medium.com', ARRAY['article', 'tutorial'], 'api'),
('Dev.to', 'https://dev.to', ARRAY['article', 'tutorial'], 'api'),
('GitHub Docs', 'https://docs.github.com', ARRAY['guide', 'tutorial'], 'scrape'),
('Personal Blogs', 'custom', ARRAY['article', 'tutorial', 'guide'], 'manual')
ON CONFLICT (name) DO NOTHING;

-- Create missing indexes if they don't exist
DO $$ 
BEGIN
    -- Content categories indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_content_categories_slug') THEN
        CREATE INDEX idx_content_categories_slug ON public.content_categories(slug);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_content_categories_parent') THEN
        CREATE INDEX idx_content_categories_parent ON public.content_categories(parent_category_id);
    END IF;
    
    -- Content series indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_content_series_slug') THEN
        CREATE INDEX idx_content_series_slug ON public.content_series(slug);
    END IF;
    
    -- Learning content additional indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_learning_content_slug') THEN
        CREATE INDEX idx_learning_content_slug ON public.learning_content(slug);
    END IF;
END $$;

-- Verify table structure and provide feedback
DO $$ 
DECLARE
    table_count INTEGER;
    category_count INTEGER;
    source_count INTEGER;
BEGIN
    -- Count tables
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_name IN ('learning_content', 'content_categories', 'content_series', 'content_interactions', 'content_progress', 'external_content_sources', 'content_import_jobs');
    
    -- Count categories
    SELECT COUNT(*) INTO category_count FROM public.content_categories;
    
    -- Count sources
    SELECT COUNT(*) INTO source_count FROM public.external_content_sources;
    
    RAISE NOTICE 'Content System Fix Applied Successfully!';
    RAISE NOTICE 'Tables available: % out of 7', table_count;
    RAISE NOTICE 'Content categories: %', category_count;
    RAISE NOTICE 'External sources: %', source_count;
    RAISE NOTICE 'Foreign key constraints added for proper relationships';
    RAISE NOTICE 'System ready for content creation and management';
END $$;
