-- Personal Learning Queue System
-- Allows users to create custom learning queues and track progress

-- Learning queue status enum
DO $$ BEGIN
    CREATE TYPE learning_queue_status AS ENUM (
      'active',
      'paused',
      'completed',
      'archived'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Learning queues table
CREATE TABLE IF NOT EXISTS learning_queues (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Owner information
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Queue details
  name TEXT NOT NULL,
  description TEXT,
  color TEXT DEFAULT '#3b82f6', -- Hex color for visual organization
  icon TEXT DEFAULT 'bi-collection-play', -- Bootstrap icon class
  
  -- Queue settings
  status learning_queue_status DEFAULT 'active',
  is_public BOOLEAN DEFAULT false,
  auto_advance BOOLEAN DEFAULT true, -- Automatically move to next item when completed
  
  -- Progress tracking
  total_items INTEGER DEFAULT 0,
  completed_items INTEGER DEFAULT 0,
  estimated_hours DECIMAL(6,2) DEFAULT 0,
  actual_hours DECIMAL(6,2) DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Learning queue items table
CREATE TABLE IF NOT EXISTS learning_queue_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Queue relationship
  queue_id UUID NOT NULL REFERENCES learning_queues(id) ON DELETE CASCADE,
  
  -- Item details
  item_type TEXT NOT NULL CHECK (item_type IN ('video', 'course', 'article', 'project', 'assessment')),
  external_id TEXT NOT NULL, -- Video ID, course ID, etc.
  title TEXT NOT NULL,
  description TEXT,
  url TEXT,
  thumbnail_url TEXT,
  
  -- Learning metadata
  provider TEXT, -- youtube, linkedin_learning, etc.
  duration_minutes INTEGER,
  difficulty_level TEXT,
  skills JSONB DEFAULT '[]'::jsonb,
  
  -- Queue positioning
  sequence_order INTEGER NOT NULL,
  is_required BOOLEAN DEFAULT true,
  
  -- Progress tracking
  status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'skipped')),
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  time_spent_minutes INTEGER DEFAULT 0,
  
  -- Completion tracking
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- User notes
  user_notes TEXT,
  user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique ordering within queue
  UNIQUE(queue_id, sequence_order)
);

-- Learning queue sharing table (for collaborative queues)
CREATE TABLE IF NOT EXISTS learning_queue_shares (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  queue_id UUID NOT NULL REFERENCES learning_queues(id) ON DELETE CASCADE,
  shared_with UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  shared_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Sharing permissions
  can_edit BOOLEAN DEFAULT false,
  can_add_items BOOLEAN DEFAULT false,
  can_reorder BOOLEAN DEFAULT false,
  
  -- Sharing details
  share_message TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(queue_id, shared_with)
);

-- Learning queue templates table (for admins to create official templates)
CREATE TABLE IF NOT EXISTS learning_queue_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Template details
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  difficulty_level TEXT,
  estimated_hours DECIMAL(6,2),
  
  -- Template metadata
  skills JSONB DEFAULT '[]'::jsonb,
  prerequisites TEXT[],
  learning_objectives TEXT[],
  
  -- Template settings
  is_official BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  
  -- Usage tracking
  usage_count INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0,
  
  -- Creator information
  created_by UUID REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning queue template items table
CREATE TABLE IF NOT EXISTS learning_queue_template_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  template_id UUID NOT NULL REFERENCES learning_queue_templates(id) ON DELETE CASCADE,
  
  -- Item details (same structure as queue items)
  item_type TEXT NOT NULL CHECK (item_type IN ('video', 'course', 'article', 'project', 'assessment')),
  external_id TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  url TEXT,
  thumbnail_url TEXT,
  
  -- Learning metadata
  provider TEXT,
  duration_minutes INTEGER,
  difficulty_level TEXT,
  skills JSONB DEFAULT '[]'::jsonb,
  
  -- Template positioning
  sequence_order INTEGER NOT NULL,
  is_required BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(template_id, sequence_order)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_learning_queues_user_id ON learning_queues(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_queues_status ON learning_queues(status);
CREATE INDEX IF NOT EXISTS idx_learning_queues_public ON learning_queues(is_public) WHERE is_public = true;

CREATE INDEX IF NOT EXISTS idx_learning_queue_items_queue_id ON learning_queue_items(queue_id);
CREATE INDEX IF NOT EXISTS idx_learning_queue_items_sequence ON learning_queue_items(queue_id, sequence_order);
CREATE INDEX IF NOT EXISTS idx_learning_queue_items_status ON learning_queue_items(status);
CREATE INDEX IF NOT EXISTS idx_learning_queue_items_skills ON learning_queue_items USING GIN(skills);

CREATE INDEX IF NOT EXISTS idx_learning_queue_shares_queue_id ON learning_queue_shares(queue_id);
CREATE INDEX IF NOT EXISTS idx_learning_queue_shares_shared_with ON learning_queue_shares(shared_with);

CREATE INDEX IF NOT EXISTS idx_learning_queue_templates_category ON learning_queue_templates(category);
CREATE INDEX IF NOT EXISTS idx_learning_queue_templates_featured ON learning_queue_templates(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_learning_queue_templates_skills ON learning_queue_templates USING GIN(skills);

-- Functions for queue management

-- Function to update queue progress when items change
CREATE OR REPLACE FUNCTION update_queue_progress()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the queue's progress counters
  UPDATE learning_queues 
  SET 
    total_items = (
      SELECT COUNT(*) 
      FROM learning_queue_items 
      WHERE queue_id = COALESCE(NEW.queue_id, OLD.queue_id)
    ),
    completed_items = (
      SELECT COUNT(*) 
      FROM learning_queue_items 
      WHERE queue_id = COALESCE(NEW.queue_id, OLD.queue_id) 
      AND status = 'completed'
    ),
    actual_hours = (
      SELECT COALESCE(SUM(time_spent_minutes), 0) / 60.0
      FROM learning_queue_items 
      WHERE queue_id = COALESCE(NEW.queue_id, OLD.queue_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.queue_id, OLD.queue_id);
  
  -- Mark queue as completed if all required items are done
  UPDATE learning_queues 
  SET 
    status = 'completed',
    completed_at = NOW()
  WHERE id = COALESCE(NEW.queue_id, OLD.queue_id)
    AND status != 'completed'
    AND total_items > 0
    AND completed_items = (
      SELECT COUNT(*) 
      FROM learning_queue_items 
      WHERE queue_id = COALESCE(NEW.queue_id, OLD.queue_id) 
      AND is_required = true
    );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to increment template usage
CREATE OR REPLACE FUNCTION increment_template_usage()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE learning_queue_templates 
  SET usage_count = usage_count + 1 
  WHERE id = NEW.template_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers
DROP TRIGGER IF EXISTS trigger_update_queue_progress ON learning_queue_items;
CREATE TRIGGER trigger_update_queue_progress
  AFTER INSERT OR UPDATE OR DELETE ON learning_queue_items
  FOR EACH ROW EXECUTE FUNCTION update_queue_progress();

DROP TRIGGER IF EXISTS update_learning_queues_updated_at ON learning_queues;
CREATE TRIGGER update_learning_queues_updated_at
  BEFORE UPDATE ON learning_queues
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_learning_queue_items_updated_at ON learning_queue_items;
CREATE TRIGGER update_learning_queue_items_updated_at
  BEFORE UPDATE ON learning_queue_items
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_learning_queue_templates_updated_at ON learning_queue_templates;
CREATE TRIGGER update_learning_queue_templates_updated_at
  BEFORE UPDATE ON learning_queue_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS)
ALTER TABLE learning_queues ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_queue_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_queue_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_queue_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_queue_template_items ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Drop existing policies first
DROP POLICY IF EXISTS "Users can view own queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can view public queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can view shared queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can create own queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can update own queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can delete own queues" ON learning_queues;
DROP POLICY IF EXISTS "Users can view items in accessible queues" ON learning_queue_items;
DROP POLICY IF EXISTS "Users can manage items in own queues" ON learning_queue_items;
DROP POLICY IF EXISTS "Users can view shares involving them" ON learning_queue_shares;
DROP POLICY IF EXISTS "Users can create shares for own queues" ON learning_queue_shares;
DROP POLICY IF EXISTS "Anyone can view active templates" ON learning_queue_templates;
DROP POLICY IF EXISTS "Anyone can view template items" ON learning_queue_template_items;

-- Learning queues: Users can manage their own queues and view public ones
CREATE POLICY "Users can view own queues" ON learning_queues
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view public queues" ON learning_queues
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view shared queues" ON learning_queues
  FOR SELECT USING (
    id IN (
      SELECT queue_id FROM learning_queue_shares 
      WHERE shared_with = auth.uid()
    )
  );

CREATE POLICY "Users can create own queues" ON learning_queues
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own queues" ON learning_queues
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own queues" ON learning_queues
  FOR DELETE USING (auth.uid() = user_id);

-- Learning queue items: Follow queue permissions
CREATE POLICY "Users can view items in accessible queues" ON learning_queue_items
  FOR SELECT USING (
    queue_id IN (
      SELECT id FROM learning_queues 
      WHERE user_id = auth.uid() 
         OR is_public = true 
         OR id IN (
           SELECT queue_id FROM learning_queue_shares 
           WHERE shared_with = auth.uid()
         )
    )
  );

CREATE POLICY "Users can manage items in own queues" ON learning_queue_items
  FOR ALL USING (
    queue_id IN (
      SELECT id FROM learning_queues 
      WHERE user_id = auth.uid()
    )
  );

-- Queue sharing policies
CREATE POLICY "Users can view shares involving them" ON learning_queue_shares
  FOR SELECT USING (shared_with = auth.uid() OR shared_by = auth.uid());

CREATE POLICY "Users can create shares for own queues" ON learning_queue_shares
  FOR INSERT WITH CHECK (
    shared_by = auth.uid() AND
    queue_id IN (SELECT id FROM learning_queues WHERE user_id = auth.uid())
  );

-- Template policies (public read, admin write)
CREATE POLICY "Anyone can view active templates" ON learning_queue_templates
  FOR SELECT USING (is_active = true);

CREATE POLICY "Anyone can view template items" ON learning_queue_template_items
  FOR SELECT USING (
    template_id IN (
      SELECT id FROM learning_queue_templates WHERE is_active = true
    )
  );

-- Comments
COMMENT ON TABLE learning_queues IS 'User-created learning queues for organizing educational content';
COMMENT ON TABLE learning_queue_items IS 'Individual items within learning queues';
COMMENT ON TABLE learning_queue_shares IS 'Sharing permissions for collaborative learning queues';
COMMENT ON TABLE learning_queue_templates IS 'Pre-built learning queue templates';
COMMENT ON TABLE learning_queue_template_items IS 'Items within learning queue templates';
