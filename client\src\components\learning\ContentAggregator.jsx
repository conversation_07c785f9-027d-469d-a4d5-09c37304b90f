import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Input, Select, SelectItem, Chip,
  Tabs, Tab, Badge, Divider, Progress, Avatar, Tooltip
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  BookOpen, Video, FileText, ExternalLink, Star, Clock, Users, 
  TrendingUp, Filter, Search, Grid, List, Award, Target, Eye,
  Play, Download, Bookmark, Heart, Share2
} from 'lucide-react';

// Import other components
import ContentViewer from './ContentViewer';
import YouTubeCourseCard from './YouTubeCourseCard';

/**
 * Content Aggregator Component
 * 
 * Unified interface that aggregates and displays content from multiple sources:
 * - Written articles and tutorials
 * - YouTube videos and courses
 * - External imported content
 * - Royaltea-specific guides
 * - Community-contributed content
 * 
 * Features unified vetting, search, filtering, and personalized recommendations.
 */
const ContentAggregator = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [content, setContent] = useState([]);
  const [filteredContent, setFilteredContent] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedContent, setSelectedContent] = useState(null);
  const [viewMode, setViewMode] = useState('grid');
  const [activeTab, setActiveTab] = useState('all');

  const [filters, setFilters] = useState({
    search: '',
    category: 'all',
    contentType: 'all',
    vettingLevel: 'all',
    difficulty: 'all',
    source: 'all'
  });

  const [stats, setStats] = useState({
    totalContent: 0,
    byType: {},
    byVettingLevel: {},
    byDifficulty: {},
    recentlyAdded: 0
  });

  useEffect(() => {
    loadContent();
    loadCategories();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [content, filters, activeTab]);

  const loadContent = async () => {
    try {
      setLoading(true);

      // Load written content
      const { data: writtenContent, error: writtenError } = await supabase
        .from('learning_content')
        .select(`
          *,
          category:content_categories(name, slug, icon, color),
          author:auth.users!author_id(user_metadata)
        `)
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      if (writtenError) throw writtenError;

      // Load YouTube content
      const { data: youtubeContent, error: youtubeError } = await supabase
        .from('course_catalog')
        .select('*')
        .eq('provider', 'youtube')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (youtubeError) throw youtubeError;

      // Load video submissions (approved)
      const { data: videoSubmissions, error: videoError } = await supabase
        .from('video_submissions')
        .select('*')
        .eq('status', 'approved')
        .order('created_at', { ascending: false });

      if (videoError) throw videoError;

      // Combine and normalize all content
      const allContent = [
        // Written content
        ...(writtenContent || []).map(item => ({
          ...item,
          source: 'written',
          sourceIcon: getContentTypeIcon(item.content_type),
          url: `/learn/content/${item.slug}`,
          thumbnail: item.thumbnail_url || item.featured_image_url,
          duration: item.estimated_read_time_minutes,
          type: 'article'
        })),
        
        // YouTube content
        ...(youtubeContent || []).map(item => ({
          ...item,
          source: 'youtube',
          sourceIcon: <Video className="w-4 h-4" />,
          url: item.course_url,
          thumbnail: item.thumbnail_url || `https://img.youtube.com/vi/${item.external_id}/hqdefault.jpg`,
          duration: item.estimated_duration_hours ? item.estimated_duration_hours * 60 : null,
          type: 'video',
          vetting_level: item.vetting_level || 0
        })),
        
        // Video submissions
        ...(videoSubmissions || []).map(item => ({
          ...item,
          source: 'community',
          sourceIcon: <Users className="w-4 h-4" />,
          url: item.video_url,
          thumbnail: item.thumbnail_url || `https://img.youtube.com/vi/${item.video_id}/hqdefault.jpg`,
          duration: item.duration_minutes,
          type: 'video',
          vetting_level: item.suggested_vetting_level || 0,
          title: item.title,
          description: item.description
        }))
      ];

      setContent(allContent);
      calculateStats(allContent);

    } catch (error) {
      console.error('Error loading content:', error);
      toast.error('Failed to load content');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('content_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const calculateStats = (contentList) => {
    const stats = {
      totalContent: contentList.length,
      byType: {},
      byVettingLevel: {},
      byDifficulty: {},
      recentlyAdded: 0
    };

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    contentList.forEach(item => {
      // Count by type
      const type = item.source || 'unknown';
      stats.byType[type] = (stats.byType[type] || 0) + 1;

      // Count by vetting level
      const level = item.vetting_level || 0;
      stats.byVettingLevel[level] = (stats.byVettingLevel[level] || 0) + 1;

      // Count by difficulty
      const difficulty = item.difficulty_level || 'unknown';
      stats.byDifficulty[difficulty] = (stats.byDifficulty[difficulty] || 0) + 1;

      // Count recently added
      if (new Date(item.created_at) > oneWeekAgo) {
        stats.recentlyAdded++;
      }
    });

    setStats(stats);
  };

  const applyFilters = () => {
    let filtered = [...content];

    // Apply tab filter
    if (activeTab !== 'all') {
      filtered = filtered.filter(item => {
        switch (activeTab) {
          case 'articles':
            return item.source === 'written';
          case 'videos':
            return item.type === 'video';
          case 'royaltea':
            return item.content_type === 'royaltea_guide' || item.tags?.includes('royaltea');
          case 'community':
            return item.source === 'community';
          default:
            return true;
        }
      });
    }

    // Apply search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(item =>
        item.title?.toLowerCase().includes(searchTerm) ||
        item.description?.toLowerCase().includes(searchTerm) ||
        item.skills_covered?.some(skill => skill.toLowerCase().includes(searchTerm)) ||
        item.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Apply category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(item => 
        item.category?.slug === filters.category ||
        item.categories?.includes(filters.category)
      );
    }

    // Apply content type filter
    if (filters.contentType !== 'all') {
      filtered = filtered.filter(item => item.content_type === filters.contentType);
    }

    // Apply vetting level filter
    if (filters.vettingLevel !== 'all') {
      const level = parseInt(filters.vettingLevel);
      filtered = filtered.filter(item => item.vetting_level === level);
    }

    // Apply difficulty filter
    if (filters.difficulty !== 'all') {
      filtered = filtered.filter(item => item.difficulty_level === filters.difficulty);
    }

    // Apply source filter
    if (filters.source !== 'all') {
      filtered = filtered.filter(item => item.source === filters.source);
    }

    setFilteredContent(filtered);
  };

  const getContentTypeIcon = (contentType) => {
    const icons = {
      'article': <FileText className="w-4 h-4" />,
      'tutorial': <BookOpen className="w-4 h-4" />,
      'guide': <Target className="w-4 h-4" />,
      'royaltea_guide': <Award className="w-4 h-4" />,
      'video': <Video className="w-4 h-4" />,
      'embedded': <ExternalLink className="w-4 h-4" />,
      'external_import': <Download className="w-4 h-4" />
    };
    return icons[contentType] || <FileText className="w-4 h-4" />;
  };

  const getVettingLevelInfo = (level) => {
    const levels = {
      0: { name: 'Unverified', color: 'default', icon: '⚪' },
      1: { name: 'Learning', color: 'warning', icon: '🟡' },
      2: { name: 'Peer Verified', color: 'secondary', icon: '🟠' },
      3: { name: 'Project Verified', color: 'success', icon: '🟢' },
      4: { name: 'Expert Verified', color: 'primary', icon: '🔵' },
      5: { name: 'Master Verified', color: 'danger', icon: '🟣' }
    };
    return levels[level] || levels[0];
  };

  const getDifficultyColor = (difficulty) => {
    const colors = {
      'beginner': 'success',
      'intermediate': 'warning',
      'advanced': 'danger',
      'expert': 'secondary'
    };
    return colors[difficulty] || 'default';
  };

  const handleContentClick = (item) => {
    if (item.source === 'written') {
      setSelectedContent(item);
    } else {
      window.open(item.url, '_blank');
    }
  };

  const renderStatsCards = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardBody className="p-4 text-center">
          <div className="text-2xl font-bold text-primary">{stats.totalContent}</div>
          <div className="text-sm text-default-600">Total Content</div>
        </CardBody>
      </Card>
      
      <Card>
        <CardBody className="p-4 text-center">
          <div className="text-2xl font-bold text-success">{stats.byType.written || 0}</div>
          <div className="text-sm text-default-600">Articles & Guides</div>
        </CardBody>
      </Card>
      
      <Card>
        <CardBody className="p-4 text-center">
          <div className="text-2xl font-bold text-warning">{(stats.byType.youtube || 0) + (stats.byType.community || 0)}</div>
          <div className="text-sm text-default-600">Videos</div>
        </CardBody>
      </Card>
      
      <Card>
        <CardBody className="p-4 text-center">
          <div className="text-2xl font-bold text-secondary">{stats.recentlyAdded}</div>
          <div className="text-sm text-default-600">Added This Week</div>
        </CardBody>
      </Card>
    </div>
  );

  const renderFilters = () => (
    <Card className="mb-6">
      <CardBody className="p-4">
        <div className="flex items-center gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <Search className="w-4 h-4 text-default-400" />
            <Input
              placeholder="Search content..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="w-64"
              size="sm"
            />
          </div>

          <Select
            placeholder="Category"
            selectedKeys={[filters.category]}
            onSelectionChange={(keys) => setFilters(prev => ({ ...prev, category: Array.from(keys)[0] }))}
            className="w-48"
            size="sm"
          >
            <SelectItem key="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.slug}>
                <div className="flex items-center gap-2">
                  <span>{category.icon}</span>
                  <span>{category.name}</span>
                </div>
              </SelectItem>
            ))}
          </Select>

          <Select
            placeholder="Vetting Level"
            selectedKeys={[filters.vettingLevel]}
            onSelectionChange={(keys) => setFilters(prev => ({ ...prev, vettingLevel: Array.from(keys)[0] }))}
            className="w-48"
            size="sm"
          >
            <SelectItem key="all">All Levels</SelectItem>
            <SelectItem key="0">⚪ Unverified</SelectItem>
            <SelectItem key="1">🟡 Learning</SelectItem>
            <SelectItem key="2">🟠 Peer Verified</SelectItem>
            <SelectItem key="3">🟢 Project Verified</SelectItem>
            <SelectItem key="4">🔵 Expert Verified</SelectItem>
            <SelectItem key="5">🟣 Master Verified</SelectItem>
          </Select>

          <Select
            placeholder="Difficulty"
            selectedKeys={[filters.difficulty]}
            onSelectionChange={(keys) => setFilters(prev => ({ ...prev, difficulty: Array.from(keys)[0] }))}
            className="w-48"
            size="sm"
          >
            <SelectItem key="all">All Difficulties</SelectItem>
            <SelectItem key="beginner">Beginner</SelectItem>
            <SelectItem key="intermediate">Intermediate</SelectItem>
            <SelectItem key="advanced">Advanced</SelectItem>
            <SelectItem key="expert">Expert</SelectItem>
          </Select>

          <div className="flex items-center gap-2 ml-auto">
            <Button
              size="sm"
              variant={viewMode === 'grid' ? 'solid' : 'flat'}
              isIconOnly
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant={viewMode === 'list' ? 'solid' : 'flat'}
              isIconOnly
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardBody>
    </Card>
  );

  const renderContentGrid = () => (
    <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
      {filteredContent.map((item, index) => (
        <motion.div
          key={`${item.source}-${item.id}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
            <CardBody className="p-0">
              {/* Thumbnail */}
              {item.thumbnail && (
                <div className="relative">
                  <img
                    src={item.thumbnail}
                    alt={item.title}
                    className="w-full h-48 object-cover"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                  <div className="absolute top-2 right-2 flex gap-2">
                    <Chip size="sm" variant="solid" color="default" className="bg-black/50 text-white">
                      {item.sourceIcon}
                    </Chip>
                    {item.vetting_level > 0 && (
                      <Chip 
                        size="sm" 
                        variant="solid" 
                        color={getVettingLevelInfo(item.vetting_level).color}
                      >
                        {getVettingLevelInfo(item.vetting_level).icon}
                      </Chip>
                    )}
                  </div>
                </div>
              )}

              <div className="p-4">
                {/* Title and Description */}
                <h3 className="font-semibold text-lg mb-2 line-clamp-2">{item.title}</h3>
                <p className="text-default-600 text-sm mb-3 line-clamp-3">{item.description}</p>

                {/* Metadata */}
                <div className="flex items-center gap-2 mb-3 text-sm text-default-500">
                  {item.duration && (
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{item.duration} min</span>
                    </div>
                  )}
                  
                  {item.difficulty_level && (
                    <Chip size="sm" color={getDifficultyColor(item.difficulty_level)} variant="flat">
                      {item.difficulty_level}
                    </Chip>
                  )}

                  {item.category && (
                    <Chip size="sm" variant="flat" color="primary">
                      {item.category.icon} {item.category.name}
                    </Chip>
                  )}
                </div>

                {/* Skills */}
                {item.skills_covered && item.skills_covered.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {item.skills_covered.slice(0, 3).map((skill, skillIndex) => (
                      <Chip key={skillIndex} size="sm" variant="flat" color="secondary">
                        {skill}
                      </Chip>
                    ))}
                    {item.skills_covered.length > 3 && (
                      <Chip size="sm" variant="flat" color="default">
                        +{item.skills_covered.length - 3}
                      </Chip>
                    )}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {item.view_count && (
                      <div className="flex items-center gap-1 text-xs text-default-500">
                        <Eye className="w-3 h-3" />
                        <span>{item.view_count}</span>
                      </div>
                    )}
                    
                    {item.like_count && (
                      <div className="flex items-center gap-1 text-xs text-default-500">
                        <Heart className="w-3 h-3" />
                        <span>{item.like_count}</span>
                      </div>
                    )}
                  </div>

                  <Button
                    size="sm"
                    color="primary"
                    variant="flat"
                    startContent={item.source === 'written' ? <BookOpen className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    onClick={() => handleContentClick(item)}
                  >
                    {item.source === 'written' ? 'Read' : 'Watch'}
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      ))}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading learning content...</p>
        </div>
      </div>
    );
  }

  if (selectedContent) {
    return (
      <ContentViewer
        contentId={selectedContent.id}
        onClose={() => setSelectedContent(null)}
      />
    );
  }

  return (
    <div className="content-aggregator space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Learning Center</h1>
          <p className="text-default-600 mt-1">
            Comprehensive learning resources from multiple sources with unified vetting
          </p>
        </div>
      </div>

      {/* Stats */}
      {renderStatsCards()}

      {/* Tabs */}
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={setActiveTab}
        className="w-full"
      >
        <Tab key="all" title={`All Content (${content.length})`} />
        <Tab key="articles" title={`Articles (${stats.byType.written || 0})`} />
        <Tab key="videos" title={`Videos (${(stats.byType.youtube || 0) + (stats.byType.community || 0)})`} />
        <Tab key="royaltea" title="Royaltea Guides" />
        <Tab key="community" title={`Community (${stats.byType.community || 0})`} />
      </Tabs>

      {/* Filters */}
      {renderFilters()}

      {/* Content Grid */}
      {filteredContent.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="w-16 h-16 mx-auto mb-4 text-default-400" />
          <h3 className="text-xl font-semibold mb-2">No content found</h3>
          <p className="text-default-600">
            Try adjusting your filters or search terms to find relevant content.
          </p>
        </div>
      ) : (
        renderContentGrid()
      )}
    </div>
  );
};

export default ContentAggregator;
