<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="11.652846000000002">
<testsuite name="e2e-comprehensive-flows.spec.js" timestamp="2025-07-15T01:57:36.752Z" hostname="Comprehensive Visual Tests - 5 Tests Only" tests="1" failures="1" skipped="0" time="4.806" errors="0">
<testcase name="Royaltea - 5 Comprehensive End-to-End Tests › 1. Learning Center Complete Flow" classname="e2e-comprehensive-flows.spec.js" time="4.806">
<failure message="e2e-comprehensive-flows.spec.js:255:3 1. Learning Center Complete Flow" type="FAILURE">
<![CDATA[  [Comprehensive Visual Tests - 5 Tests Only] › e2e-comprehensive-flows.spec.js:255:3 › Royaltea - 5 Comprehensive End-to-End Tests › 1. Learning Center Complete Flow 

    Error: Email input not found

      163 |         emailInput = anyInput;
      164 |       } else {
    > 165 |         throw new Error('Email input not found');
          |               ^
      166 |       }
      167 |     }
      168 |
        at loginUser (C:\Data\Projects\Royaltea\tests\e2e-comprehensive-flows.spec.js:165:15)
        at C:\Data\Projects\Royaltea\tests\e2e-comprehensive-flows.spec.js:259:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\e2e-comprehensive-flows-Ro-a09a6-arning-Center-Complete-Flow-Comprehensive-Visual-Tests---5-Tests-Only\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\e2e-comprehensive-flows-Ro-a09a6-arning-Center-Complete-Flow-Comprehensive-Visual-Tests---5-Tests-Only\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎓 Starting Learning Center Complete Flow Test
🔐 Attempting login for: <EMAIL>
🏠 First navigating to root page...
📸 Screenshot saved: Root Page - Initial Load
🏠 Root page - Inputs: 0, Forms: 0, Buttons: 0
🔄 No login form on root, navigating to /login...
📸 Screenshot saved: Login Page - Initial Load
📄 Page title: Royaltea - A Game Development Gigwork Platform
🌐 Page URL: http://localhost:5173/login
📝 Total inputs found: 0
📋 Input details: []
📋 Forms found: 0
🔘 Buttons found: 0
📄 Body text length: 15
📄 Body text preview: 
    
    
  

...
📄 Body HTML length: 87
📄 Body HTML preview: 
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  

...
⚛️ React root div found: 1
📜 Script tags found: 7
🎨 CSS files loaded: 0
⏳ Loading elements found: 0
❌ Error elements found: 0
📦 Div elements found: 1
⏳ Waiting additional 3 seconds for content to load...
📝 Inputs after wait: 0
📋 Forms after wait: 0
🔘 Buttons after wait: 0
❌ Selector not found: input[type="email"]
❌ Selector not found: input[id="email"]
❌ Selector not found: input[placeholder*="email"]
❌ Selector not found: input[placeholder*="Enter email"]
❌ Selector not found: [data-slot="input"][type="email"]
❌ Selector not found: .heroui-input input[type="email"]
❌ Selector not found: input
❌ Could not find email input, taking screenshot for debugging
📸 Screenshot saved: Login Page - Email Input Not Found

[[ATTACHMENT|test-results\e2e-comprehensive-flows-Ro-a09a6-arning-Center-Complete-Flow-Comprehensive-Visual-Tests---5-Tests-Only\test-failed-1.png]]

[[ATTACHMENT|test-results\e2e-comprehensive-flows-Ro-a09a6-arning-Center-Complete-Flow-Comprehensive-Visual-Tests---5-Tests-Only\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>