-- Add distribution_status and distributed_at columns to revenue_entries table if they don't exist

-- Check if the revenue_entries table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'revenue_entries'
    ) THEN
        -- Add distribution_status column if it doesn't exist
        IF NOT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'revenue_entries'
            AND column_name = 'distribution_status'
        ) THEN
            ALTER TABLE public.revenue_entries
            ADD COLUMN distribution_status TEXT DEFAULT 'pending';
            
            RAISE NOTICE 'Added distribution_status column to revenue_entries table';
        END IF;
        
        -- Add distributed_at column if it doesn't exist
        IF NOT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'revenue_entries'
            AND column_name = 'distributed_at'
        ) THEN
            ALTER TABLE public.revenue_entries
            ADD COLUMN distributed_at TIMESTAMP WITH TIME ZONE;
            
            RAISE NOTICE 'Added distributed_at column to revenue_entries table';
        END IF;
        
        -- Migrate data from metadata to new columns if needed
        UPDATE public.revenue_entries
        SET 
            distribution_status = COALESCE(
                (metadata->>'distribution_status')::text,
                distribution_status
            ),
            distributed_at = COALESCE(
                (metadata->>'distributed_at')::timestamp with time zone,
                distributed_at
            )
        WHERE 
            metadata IS NOT NULL AND 
            (
                metadata->>'distribution_status' IS NOT NULL OR
                metadata->>'distributed_at' IS NOT NULL
            );
            
        RAISE NOTICE 'Migrated distribution data from metadata to columns';
    ELSE
        RAISE NOTICE 'revenue_entries table does not exist, skipping column additions';
    END IF;
END $$;
