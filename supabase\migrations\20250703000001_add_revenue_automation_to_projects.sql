-- Add revenue automation settings to projects table
-- This enables projects to have automatic revenue distribution when enabled

-- Add revenue automation column to projects table
DO $$
BEGIN
    -- Add revenue_automation_enabled column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'projects' 
        AND column_name = 'revenue_automation_enabled'
    ) THEN
        ALTER TABLE public.projects 
        ADD COLUMN revenue_automation_enabled BOOLEAN DEFAULT false;
        
        COMMENT ON COLUMN public.projects.revenue_automation_enabled IS 'Enable automatic revenue distribution when revenue is detected via Teller webhooks';
        
        RAISE NOTICE 'Added revenue_automation_enabled column to projects table';
    ELSE
        RAISE NOTICE 'revenue_automation_enabled column already exists in projects table';
    END IF;
    
    -- Add revenue automation settings column for more complex automation rules
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'projects' 
        AND column_name = 'revenue_automation_settings'
    ) THEN
        ALTER TABLE public.projects 
        ADD COLUMN revenue_automation_settings JSONB DEFAULT '{
            "auto_approve_threshold": 1000,
            "require_manual_approval_above": 5000,
            "notification_settings": {
                "notify_on_detection": true,
                "notify_on_distribution": true,
                "notify_contributors": true
            },
            "distribution_delay_hours": 0
        }'::jsonb;
        
        COMMENT ON COLUMN public.projects.revenue_automation_settings IS 'Advanced automation settings for revenue distribution including thresholds and notification preferences';
        
        RAISE NOTICE 'Added revenue_automation_settings column to projects table';
    ELSE
        RAISE NOTICE 'revenue_automation_settings column already exists in projects table';
    END IF;
END $$;

-- Add source_type column to revenue_entries table to track revenue source
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'revenue_entries' 
        AND column_name = 'source_type'
    ) THEN
        ALTER TABLE public.revenue_entries 
        ADD COLUMN source_type TEXT DEFAULT 'manual' CHECK (source_type IN ('manual', 'bank_transfer', 'stripe', 'paypal', 'crypto', 'other'));
        
        COMMENT ON COLUMN public.revenue_entries.source_type IS 'Source type of the revenue entry for tracking and automation';
        
        RAISE NOTICE 'Added source_type column to revenue_entries table';
    ELSE
        RAISE NOTICE 'source_type column already exists in revenue_entries table';
    END IF;
    
    -- Add teller_transaction_id column to link revenue entries to Teller transactions
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'revenue_entries' 
        AND column_name = 'teller_transaction_id'
    ) THEN
        ALTER TABLE public.revenue_entries 
        ADD COLUMN teller_transaction_id TEXT;
        
        COMMENT ON COLUMN public.revenue_entries.teller_transaction_id IS 'Teller transaction ID for automatic revenue detection';
        
        RAISE NOTICE 'Added teller_transaction_id column to revenue_entries table';
    ELSE
        RAISE NOTICE 'teller_transaction_id column already exists in revenue_entries table';
    END IF;
END $$;

-- Create teller_accounts table if it doesn't exist (for linking bank accounts to projects)
CREATE TABLE IF NOT EXISTS public.teller_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    
    -- Teller account details
    teller_account_id TEXT NOT NULL UNIQUE,
    teller_item_id TEXT NOT NULL,
    account_name TEXT NOT NULL,
    account_type TEXT NOT NULL, -- 'checking', 'savings', 'credit', etc.
    account_subtype TEXT,
    
    -- Account status
    is_active BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for teller_accounts
ALTER TABLE public.teller_accounts ENABLE ROW LEVEL SECURITY;

-- Users can only see their own linked accounts
CREATE POLICY "Users can view their own teller accounts" ON public.teller_accounts
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own teller accounts
CREATE POLICY "Users can insert their own teller accounts" ON public.teller_accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own teller accounts
CREATE POLICY "Users can update their own teller accounts" ON public.teller_accounts
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own teller accounts
CREATE POLICY "Users can delete their own teller accounts" ON public.teller_accounts
    FOR DELETE USING (auth.uid() = user_id);

-- Add missing columns to teller_accounts table
ALTER TABLE public.teller_accounts ADD COLUMN IF NOT EXISTS project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_teller_accounts_user_id ON public.teller_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_teller_accounts_project_id ON public.teller_accounts(project_id);
CREATE INDEX IF NOT EXISTS idx_teller_accounts_teller_account_id ON public.teller_accounts(teller_account_id);
CREATE INDEX IF NOT EXISTS idx_revenue_entries_teller_transaction_id ON public.revenue_entries(teller_transaction_id);

-- Add comments
COMMENT ON TABLE public.teller_accounts IS 'Links Teller bank accounts to projects for automatic revenue detection';
