-- Task-Gig Integration System (Fixed)
-- This migration creates additional tables for task-gig integration
-- Core tables (projects, tasks, collaboration_requests, etc.) are already created

-- Task-Gig relationship tracking
CREATE TABLE IF NOT EXISTS task_gig_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationship details
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
  gig_id UUID REFERENCES collaboration_requests(id) ON DELETE CASCADE,
  
  -- Relationship type
  relationship_type TEXT DEFAULT 'converted_from_gig' CHECK (
    relationship_type IN ('converted_from_gig', 'converted_to_gig', 'linked', 'derived')
  ),
  
  -- Conversion metadata
  converted_by UUID REFERENCES auth.users(id),
  conversion_reason TEXT,
  conversion_notes TEXT,
  
  -- Status tracking
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique relationships
  UNIQUE(task_id, gig_id)
);

-- Gig application tracking (enhanced version of collaboration_request_applications)
CREATE TABLE IF NOT EXISTS gig_applications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Application details
  gig_id UUID NOT NULL REFERENCES collaboration_requests(id) ON DELETE CASCADE,
  applicant_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Application content
  cover_letter TEXT,
  proposed_approach TEXT,
  portfolio_links TEXT[],
  relevant_experience TEXT,
  
  -- Proposal details
  proposed_rate DECIMAL(10,2),
  proposed_timeline_days INTEGER,
  availability_start_date DATE,
  estimated_completion_date DATE,
  
  -- Application status
  status TEXT DEFAULT 'pending' CHECK (
    status IN ('pending', 'under_review', 'accepted', 'rejected', 'withdrawn', 'expired')
  ),
  
  -- Review process
  reviewed_by UUID REFERENCES auth.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  review_notes TEXT,
  rejection_reason TEXT,
  
  -- Timestamps
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(gig_id, applicant_id)
);

-- Task conversion history
CREATE TABLE IF NOT EXISTS task_conversion_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Source and target
  source_id UUID NOT NULL, -- Can be task or gig ID
  target_id UUID NOT NULL, -- Can be task or gig ID
  source_type TEXT NOT NULL CHECK (source_type IN ('task', 'gig')),
  target_type TEXT NOT NULL CHECK (target_type IN ('task', 'gig')),
  
  -- Conversion details
  converted_by UUID NOT NULL REFERENCES auth.users(id),
  conversion_reason TEXT,
  conversion_notes TEXT,
  
  -- Data mapping
  field_mappings JSONB DEFAULT '{}', -- Track how fields were mapped
  preserved_data JSONB DEFAULT '{}', -- Store original data
  
  -- Status
  conversion_status TEXT DEFAULT 'completed' CHECK (
    conversion_status IN ('pending', 'in_progress', 'completed', 'failed', 'rolled_back')
  ),
  
  -- Timestamps
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_task_gig_relationships_task_id ON task_gig_relationships(task_id);
CREATE INDEX IF NOT EXISTS idx_task_gig_relationships_gig_id ON task_gig_relationships(gig_id);
CREATE INDEX IF NOT EXISTS idx_task_gig_relationships_status ON task_gig_relationships(status);

CREATE INDEX IF NOT EXISTS idx_gig_applications_gig_id ON gig_applications(gig_id);
CREATE INDEX IF NOT EXISTS idx_gig_applications_applicant_id ON gig_applications(applicant_id);
CREATE INDEX IF NOT EXISTS idx_gig_applications_status ON gig_applications(status);

CREATE INDEX IF NOT EXISTS idx_task_conversion_history_source ON task_conversion_history(source_id, source_type);
CREATE INDEX IF NOT EXISTS idx_task_conversion_history_target ON task_conversion_history(target_id, target_type);
CREATE INDEX IF NOT EXISTS idx_task_conversion_history_converted_by ON task_conversion_history(converted_by);

-- Row Level Security
ALTER TABLE task_gig_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE gig_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_conversion_history ENABLE ROW LEVEL SECURITY;

-- RLS Policies
DROP POLICY IF EXISTS "Users can view task-gig relationships for their projects" ON task_gig_relationships;
CREATE POLICY "Users can view task-gig relationships for their projects" ON task_gig_relationships
  FOR SELECT USING (
    task_id IN (
      SELECT id FROM tasks WHERE created_by = auth.uid() OR assignee_id = auth.uid()
    ) OR
    gig_id IN (
      SELECT id FROM collaboration_requests WHERE requester_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Users can manage their own gig applications" ON gig_applications;
CREATE POLICY "Users can manage their own gig applications" ON gig_applications
  FOR ALL USING (applicant_id = auth.uid());

DROP POLICY IF EXISTS "Gig owners can view applications" ON gig_applications;
CREATE POLICY "Gig owners can view applications" ON gig_applications
  FOR SELECT USING (
    gig_id IN (
      SELECT id FROM collaboration_requests WHERE requester_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Users can view their conversion history" ON task_conversion_history;
CREATE POLICY "Users can view their conversion history" ON task_conversion_history
  FOR SELECT USING (converted_by = auth.uid());

-- Comments
COMMENT ON TABLE task_gig_relationships IS 'Tracks relationships between tasks and gigs for conversion and linking';
COMMENT ON TABLE gig_applications IS 'Enhanced gig application system with detailed proposal tracking';
COMMENT ON TABLE task_conversion_history IS 'Audit trail for task-gig conversions and transformations';

-- Functions for task-gig conversion
CREATE OR REPLACE FUNCTION convert_gig_to_task(
  gig_id UUID,
  project_id UUID DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  new_task_id UUID;
  gig_record RECORD;
BEGIN
  -- Get gig details
  SELECT * INTO gig_record FROM collaboration_requests WHERE id = gig_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Gig not found';
  END IF;
  
  -- Create new task
  INSERT INTO tasks (
    title,
    description,
    project_id,
    created_by,
    status,
    priority
  ) VALUES (
    gig_record.project_title,
    gig_record.project_description,
    project_id,
    gig_record.requester_id,
    'todo',
    'medium'
  ) RETURNING id INTO new_task_id;
  
  -- Create relationship record
  INSERT INTO task_gig_relationships (
    task_id,
    gig_id,
    relationship_type,
    converted_by
  ) VALUES (
    new_task_id,
    gig_id,
    'converted_from_gig',
    auth.uid()
  );
  
  -- Record conversion history
  INSERT INTO task_conversion_history (
    source_id,
    target_id,
    source_type,
    target_type,
    converted_by,
    conversion_reason,
    completed_at
  ) VALUES (
    gig_id,
    new_task_id,
    'gig',
    'task',
    auth.uid(),
    'Manual conversion from gig to task',
    NOW()
  );
  
  RETURN new_task_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
