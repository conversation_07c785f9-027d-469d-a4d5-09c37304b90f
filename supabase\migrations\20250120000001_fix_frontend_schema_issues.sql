-- Fix Frontend Schema Issues
-- This migration addresses all the database schema issues causing frontend errors

-- ============================================================================
-- 1. ADD MISSING COLUMNS TO EXISTING TABLES
-- ============================================================================

-- Fix users table - add missing columns
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS bio TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS social_links JSONB DEFAULT '{}';
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS stats JSONB DEFAULT '{}';

-- Fix projects table - add missing columns
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS total_revenue DECIMAL(12,2) DEFAULT 0.00;
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS revenue_distribution_model TEXT DEFAULT 'equal';

-- Fix project_contributors table - add missing columns
ALTER TABLE public.project_contributors ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;

-- Fix team_members table - add missing columns
ALTER TABLE public.team_members ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'removed'));

-- ============================================================================
-- 2. CREATE MISSING TABLES
-- ============================================================================

-- Create active_timers table
CREATE TABLE IF NOT EXISTS public.active_timers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ended_at TIMESTAMP WITH TIME ZONE,
    total_seconds INTEGER DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create contribution_tracking_config table
CREATE TABLE IF NOT EXISTS public.contribution_tracking_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    tracking_enabled BOOLEAN DEFAULT TRUE,
    auto_track_commits BOOLEAN DEFAULT FALSE,
    auto_track_time BOOLEAN DEFAULT FALSE,
    contribution_weights JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    UNIQUE(project_id)
);

-- Create contributions table
CREATE TABLE IF NOT EXISTS public.contributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    contribution_type TEXT NOT NULL,
    hours_worked DECIMAL(8,2) DEFAULT 0.00,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'completed')),
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add missing columns to existing team_invitations table
ALTER TABLE public.team_invitations ADD COLUMN IF NOT EXISTS project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE;
ALTER TABLE public.team_invitations ADD COLUMN IF NOT EXISTS message TEXT;

-- ============================================================================
-- 3. CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Active timers indexes
CREATE INDEX IF NOT EXISTS idx_active_timers_user_id ON public.active_timers(user_id);
CREATE INDEX IF NOT EXISTS idx_active_timers_project_id ON public.active_timers(project_id);
CREATE INDEX IF NOT EXISTS idx_active_timers_is_active ON public.active_timers(is_active);

-- Contribution tracking config indexes
CREATE INDEX IF NOT EXISTS idx_contribution_tracking_config_project_id ON public.contribution_tracking_config(project_id);

-- Contributions indexes
CREATE INDEX IF NOT EXISTS idx_contributions_user_id ON public.contributions(user_id);
CREATE INDEX IF NOT EXISTS idx_contributions_project_id ON public.contributions(project_id);
CREATE INDEX IF NOT EXISTS idx_contributions_status ON public.contributions(status);

-- Team invitations indexes
CREATE INDEX IF NOT EXISTS idx_team_invitations_team_id ON public.team_invitations(team_id);
CREATE INDEX IF NOT EXISTS idx_team_invitations_invited_email ON public.team_invitations(invited_email);
CREATE INDEX IF NOT EXISTS idx_team_invitations_status ON public.team_invitations(status);

-- ============================================================================
-- 4. CREATE TRIGGERS FOR UPDATED_AT COLUMNS
-- ============================================================================

-- Active timers trigger
CREATE TRIGGER update_active_timers_updated_at
BEFORE UPDATE ON public.active_timers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Contribution tracking config trigger
CREATE TRIGGER update_contribution_tracking_config_updated_at
BEFORE UPDATE ON public.contribution_tracking_config
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Contributions trigger
CREATE TRIGGER update_contributions_updated_at
BEFORE UPDATE ON public.contributions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Team invitations trigger
CREATE TRIGGER update_team_invitations_updated_at
BEFORE UPDATE ON public.team_invitations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 5. ENABLE ROW LEVEL SECURITY
-- ============================================================================

-- Enable RLS on new tables
ALTER TABLE public.active_timers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contribution_tracking_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contributions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_invitations ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 6. CREATE RLS POLICIES
-- ============================================================================

-- Active timers policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'active_timers' AND policyname = 'Users can manage their own timers') THEN
        CREATE POLICY "Users can manage their own timers" ON public.active_timers
            FOR ALL USING (user_id = auth.uid());
    END IF;
END $$;

-- Contribution tracking config policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'contribution_tracking_config' AND policyname = 'Project owners can manage tracking config') THEN
        CREATE POLICY "Project owners can manage tracking config" ON public.contribution_tracking_config
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM public.projects p
                    WHERE p.id = contribution_tracking_config.project_id
                    AND p.created_by = auth.uid()
                )
            );
    END IF;
END $$;

-- Contributions policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'contributions' AND policyname = 'Users can view project contributions') THEN
        CREATE POLICY "Users can view project contributions" ON public.contributions
            FOR SELECT USING (
                user_id = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM public.projects p
                    WHERE p.id = contributions.project_id
                    AND p.created_by = auth.uid()
                )
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'contributions' AND policyname = 'Users can manage their own contributions') THEN
        CREATE POLICY "Users can manage their own contributions" ON public.contributions
            FOR ALL USING (user_id = auth.uid());
    END IF;
END $$;

-- Team invitations policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'team_invitations' AND policyname = 'Team admins can manage invitations') THEN
        CREATE POLICY "Team admins can manage invitations" ON public.team_invitations
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM public.team_members tm
                    WHERE tm.team_id = team_invitations.team_id
                    AND tm.user_id = auth.uid()
                    AND tm.role IN ('owner', 'admin')
                )
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'team_invitations' AND policyname = 'Users can view their own invitations') THEN
        CREATE POLICY "Users can view their own invitations" ON public.team_invitations
            FOR SELECT USING (
                invited_email = (SELECT email FROM auth.users WHERE id = auth.uid()) OR
                invited_user_id = auth.uid()
            );
    END IF;
END $$;

-- ============================================================================
-- 7. GRANT PERMISSIONS
-- ============================================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.active_timers TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.contribution_tracking_config TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.contributions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.team_invitations TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- 8. ADD COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.active_timers IS 'Tracks active time tracking sessions for tasks and projects';
COMMENT ON TABLE public.contribution_tracking_config IS 'Configuration settings for contribution tracking per project';
COMMENT ON TABLE public.contributions IS 'Records user contributions to projects including time and work type';
COMMENT ON TABLE public.team_invitations IS 'Manages team and project invitations sent to users';

COMMENT ON COLUMN public.users.is_premium IS 'Whether the user has a premium subscription';
COMMENT ON COLUMN public.users.bio IS 'User biography or description';
COMMENT ON COLUMN public.users.social_links IS 'JSON object containing social media links';
COMMENT ON COLUMN public.users.stats IS 'JSON object containing user statistics';

COMMENT ON COLUMN public.projects.is_active IS 'Whether the project is currently active';
COMMENT ON COLUMN public.projects.total_revenue IS 'Total revenue generated by the project';
COMMENT ON COLUMN public.projects.revenue_distribution_model IS 'Model used for distributing revenue among contributors';

COMMENT ON COLUMN public.project_contributors.is_admin IS 'Whether the contributor has admin privileges for the project';
COMMENT ON COLUMN public.team_members.status IS 'Current status of the team member';
