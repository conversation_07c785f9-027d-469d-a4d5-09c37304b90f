import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Chip, Progress, Modal, ModalContent, 
  ModalHeader, ModalBody, ModalFooter, useDisclosure, Textarea, Select, 
  SelectItem, Input, Tabs, Tab, Table, TableHeader, TableColumn, TableBody, 
  TableRow, TableCell, Spinner, Badge
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  CheckCircle, XCircle, Clock, TrendingUp, Users, Star, 
  ThumbsUp, ThumbsDown, Eye, ExternalLink, AlertTriangle 
} from 'lucide-react';

/**
 * Video Vetting Dashboard Component
 * 
 * Admin interface for reviewing and approving/rejecting video submissions
 * with detailed review criteria and community voting integration.
 */
const VideoVettingDashboard = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [submissions, setSubmissions] = useState([]);
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [reviewForm, setReviewForm] = useState({
    action: '',
    reviewNotes: '',
    qualityRating: 5,
    educationalValue: 5,
    contentAccuracy: 5,
    productionQuality: 5,
    targetSkills: [],
    recommendedDifficulty: '',
    addToCatalog: false,
    markForVetting: false,
    // Vetting-specific fields
    approveVettingSuggestion: false,
    vettingLevel: 1,
    vettingSkills: [],
    vettingJustification: '',
    estimatedCompletionTime: ''
  });

  const { isOpen: isReviewOpen, onOpen: onReviewOpen, onClose: onReviewClose } = useDisclosure();

  // Vetting level definitions (matching the vetting system)
  const vettingLevels = [
    {
      level: 1,
      name: 'Learning',
      color: 'warning',
      description: 'Foundational education content for beginners',
      requirements: 'Basic educational content, clear explanations, suitable for newcomers'
    },
    {
      level: 2,
      name: 'Peer Verified',
      color: 'secondary',
      description: 'Community-validated intermediate content',
      requirements: 'Practical examples, hands-on projects, peer-reviewable content'
    },
    {
      level: 3,
      name: 'Project Verified',
      color: 'success',
      description: 'Professional-level content with real-world applications',
      requirements: 'Industry best practices, complex projects, client-ready skills'
    },
    {
      level: 4,
      name: 'Expert Verified',
      color: 'primary',
      description: 'Advanced content for experienced professionals',
      requirements: 'Cutting-edge techniques, expert insights, advanced problem-solving'
    },
    {
      level: 5,
      name: 'Master Verified',
      color: 'danger',
      description: 'Master-level content for industry leaders',
      requirements: 'Thought leadership, innovation, mastery-level expertise'
    }
  ];

  useEffect(() => {
    loadSubmissions();
  }, [activeTab]);

  const loadSubmissions = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('admin_video_review_queue')
        .select('*');

      // Filter by status based on active tab
      switch (activeTab) {
        case 'pending':
          query = query.eq('status', 'pending');
          break;
        case 'approved':
          query = query.eq('status', 'approved');
          break;
        case 'rejected':
          query = query.eq('status', 'rejected');
          break;
        case 'high_priority':
          query = query.eq('review_priority', 'high_priority');
          break;
        case 'auto_approve':
          query = query.eq('review_priority', 'auto_approve_ready');
          break;
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      setSubmissions(data || []);

    } catch (error) {
      console.error('Error loading submissions:', error);
      toast.error('Failed to load video submissions');
    } finally {
      setLoading(false);
    }
  };

  const handleReviewSubmission = (submission) => {
    setSelectedSubmission(submission);
    setReviewForm({
      action: '',
      reviewNotes: '',
      qualityRating: 5,
      educationalValue: 5,
      contentAccuracy: 5,
      productionQuality: 5,
      targetSkills: submission.skills || [],
      recommendedDifficulty: submission.difficulty_level || 'intermediate',
      addToCatalog: false,
      markForVetting: false,
      // Pre-fill vetting fields if submission has vetting suggestion
      approveVettingSuggestion: submission.suggest_for_vetting || false,
      vettingLevel: submission.suggested_vetting_level || 1,
      vettingSkills: submission.vetting_skills || [],
      vettingJustification: submission.vetting_justification || '',
      estimatedCompletionTime: submission.estimated_completion_time || ''
    });
    onReviewOpen();
  };

  const submitReview = async () => {
    if (!selectedSubmission || !reviewForm.action) {
      toast.error('Please select an action');
      return;
    }

    try {
      setLoading(true);

      // Update submission status
      const { error: updateError } = await supabase
        .from('video_submissions')
        .update({
          status: reviewForm.action,
          reviewed_by: currentUser.id,
          reviewed_at: new Date().toISOString(),
          review_notes: reviewForm.reviewNotes,
          quality_rating: reviewForm.action === 'approved' ? 'excellent' : 'poor'
        })
        .eq('id', selectedSubmission.id);

      if (updateError) throw updateError;

      // If approved and should add to catalog
      if (reviewForm.action === 'approved' && reviewForm.addToCatalog) {
        const catalogData = {
          external_id: selectedSubmission.video_id,
          provider: 'youtube',
          title: selectedSubmission.title,
          description: selectedSubmission.description,
          duration_minutes: selectedSubmission.duration_minutes,
          instructor_name: selectedSubmission.channel_name,
          thumbnail_url: selectedSubmission.thumbnail_url || `https://img.youtube.com/vi/${selectedSubmission.video_id}/mqdefault.jpg`,
          course_url: selectedSubmission.video_url,
          skills: reviewForm.targetSkills,
          categories: selectedSubmission.categories,
          difficulty_level: reviewForm.recommendedDifficulty,
          is_active: true,
          is_featured: false,
          rating: (reviewForm.qualityRating + reviewForm.educationalValue + reviewForm.contentAccuracy + reviewForm.productionQuality) / 4,
          metadata: {
            reviewed_by: currentUser.id,
            review_date: new Date().toISOString(),
            quality_scores: {
              overall: reviewForm.qualityRating,
              educational: reviewForm.educationalValue,
              accuracy: reviewForm.contentAccuracy,
              production: reviewForm.productionQuality
            },
            marked_for_vetting: reviewForm.markForVetting
          }
        };

        const { error: catalogError } = await supabase
          .from('course_catalog')
          .upsert([catalogData], { onConflict: 'external_id,provider' });

        if (catalogError) {
          console.error('Error adding to catalog:', catalogError);
          toast.error('Video approved but failed to add to catalog');
        }
      }

      toast.success(`Video ${reviewForm.action} successfully!`);
      onReviewClose();
      loadSubmissions();

    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'success';
      case 'rejected': return 'danger';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'auto_approve_ready': return 'success';
      case 'high_priority': return 'danger';
      case 'normal': return 'primary';
      default: return 'default';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center py-12">
        <Card className="max-w-md">
          <CardBody className="text-center p-8">
            <AlertTriangle className="w-16 h-16 mx-auto mb-4 text-warning" />
            <h2 className="text-xl font-bold mb-2">Access Denied</h2>
            <p className="text-default-600">
              You need to be logged in as an admin to access the video vetting dashboard.
            </p>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="video-vetting-dashboard space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Video Vetting Dashboard</h1>
          <p className="text-default-600 mt-1">
            Review and approve community-submitted educational videos
          </p>
        </div>
        <Button
          color="primary"
          variant="flat"
          onPress={loadSubmissions}
          startContent={loading ? <Spinner size="sm" /> : null}
        >
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardBody className="text-center p-4">
            <Clock className="w-8 h-8 mx-auto mb-2 text-warning" />
            <div className="text-2xl font-bold text-warning">
              {submissions.filter(s => s.status === 'pending').length}
            </div>
            <div className="text-sm text-default-600">Pending Review</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center p-4">
            <CheckCircle className="w-8 h-8 mx-auto mb-2 text-success" />
            <div className="text-2xl font-bold text-success">
              {submissions.filter(s => s.status === 'approved').length}
            </div>
            <div className="text-sm text-default-600">Approved</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center p-4">
            <XCircle className="w-8 h-8 mx-auto mb-2 text-danger" />
            <div className="text-2xl font-bold text-danger">
              {submissions.filter(s => s.status === 'rejected').length}
            </div>
            <div className="text-sm text-default-600">Rejected</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center p-4">
            <TrendingUp className="w-8 h-8 mx-auto mb-2 text-primary" />
            <div className="text-2xl font-bold text-primary">
              {submissions.filter(s => s.review_priority === 'auto_approve_ready').length}
            </div>
            <div className="text-sm text-default-600">Auto-Approve Ready</div>
          </CardBody>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs 
        selectedKey={activeTab} 
        onSelectionChange={setActiveTab}
        className="w-full"
      >
        <Tab key="pending" title="Pending Review">
          <SubmissionsTable
            submissions={submissions.filter(s => s.status === 'pending')}
            loading={loading}
            onReview={handleReviewSubmission}
          />
        </Tab>
        <Tab key="high_priority" title="High Priority">
          <SubmissionsTable
            submissions={submissions.filter(s => s.review_priority === 'high_priority')}
            loading={loading}
            onReview={handleReviewSubmission}
          />
        </Tab>
        <Tab key="auto_approve" title="Auto-Approve Ready">
          <SubmissionsTable
            submissions={submissions.filter(s => s.review_priority === 'auto_approve_ready')}
            loading={loading}
            onReview={handleReviewSubmission}
          />
        </Tab>
        <Tab key="approved" title="Approved">
          <SubmissionsTable
            submissions={submissions.filter(s => s.status === 'approved')}
            loading={loading}
            onReview={handleReviewSubmission}
            showActions={false}
          />
        </Tab>
        <Tab key="rejected" title="Rejected">
          <SubmissionsTable
            submissions={submissions.filter(s => s.status === 'rejected')}
            loading={loading}
            onReview={handleReviewSubmission}
            showActions={false}
          />
        </Tab>
      </Tabs>

      {/* Review Modal */}
      <Modal
        isOpen={isReviewOpen}
        onClose={onReviewClose}
        size="4xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex flex-col">
              <h3 className="text-xl font-bold">Review Video Submission</h3>
              {selectedSubmission && (
                <p className="text-sm text-default-600 font-normal">
                  {selectedSubmission.title}
                </p>
              )}
            </div>
          </ModalHeader>
          <ModalBody>
            {selectedSubmission && (
              <div className="space-y-6">
                {/* Video Preview */}
                <div className="flex gap-4">
                  <img
                    src={selectedSubmission.thumbnail_url || `https://img.youtube.com/vi/${selectedSubmission.video_id}/mqdefault.jpg`}
                    alt={selectedSubmission.title}
                    className="w-48 h-36 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h4 className="font-semibold text-lg mb-2">{selectedSubmission.title}</h4>
                    <p className="text-sm text-default-600 mb-2">
                      by {selectedSubmission.channel_name}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-default-500 mb-2">
                      {selectedSubmission.duration_minutes && (
                        <span>{selectedSubmission.duration_minutes} minutes</span>
                      )}
                      <span>{selectedSubmission.difficulty_level}</span>
                    </div>
                    <div className="flex items-center gap-2 mb-2">
                      <ThumbsUp className="w-4 h-4 text-success" />
                      <span className="text-sm">{selectedSubmission.upvotes || 0}</span>
                      <ThumbsDown className="w-4 h-4 text-danger" />
                      <span className="text-sm">{selectedSubmission.downvotes || 0}</span>
                      <Badge color="primary" variant="flat">
                        Net: {(selectedSubmission.upvotes || 0) - (selectedSubmission.downvotes || 0)}
                      </Badge>
                    </div>
                    <Button
                      size="sm"
                      color="primary"
                      variant="flat"
                      as="a"
                      href={selectedSubmission.video_url}
                      target="_blank"
                      startContent={<ExternalLink className="w-4 h-4" />}
                    >
                      Watch Video
                    </Button>
                  </div>
                </div>

                {/* Submission Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="font-semibold mb-2">Skills</h5>
                    <div className="flex flex-wrap gap-1">
                      {selectedSubmission.skills?.map((skill, index) => (
                        <Chip key={index} size="sm" variant="flat" color="primary">
                          {skill}
                        </Chip>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h5 className="font-semibold mb-2">Categories</h5>
                    <div className="flex flex-wrap gap-1">
                      {selectedSubmission.categories?.map((category, index) => (
                        <Chip key={index} size="sm" variant="flat" color="secondary">
                          {category}
                        </Chip>
                      ))}
                    </div>
                  </div>
                </div>

                {selectedSubmission.description && (
                  <div>
                    <h5 className="font-semibold mb-2">Description</h5>
                    <p className="text-sm text-default-600">{selectedSubmission.description}</p>
                  </div>
                )}

                {selectedSubmission.submission_reason && (
                  <div>
                    <h5 className="font-semibold mb-2">Submission Reason</h5>
                    <p className="text-sm text-default-600">{selectedSubmission.submission_reason}</p>
                  </div>
                )}

                {selectedSubmission.target_audience && (
                  <div>
                    <h5 className="font-semibold mb-2">Target Audience</h5>
                    <p className="text-sm text-default-600">{selectedSubmission.target_audience}</p>
                  </div>
                )}

                {/* Review Form */}
                <div className="border-t pt-4">
                  <h5 className="font-semibold mb-4">Review Decision</h5>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <Select
                      label="Action *"
                      placeholder="Select review action"
                      selectedKeys={reviewForm.action ? [reviewForm.action] : []}
                      onSelectionChange={(keys) => setReviewForm({
                        ...reviewForm,
                        action: Array.from(keys)[0] || ''
                      })}
                      isRequired
                    >
                      <SelectItem key="approved" value="approved">
                        ✅ Approve
                      </SelectItem>
                      <SelectItem key="rejected" value="rejected">
                        ❌ Reject
                      </SelectItem>
                      <SelectItem key="needs_revision" value="needs_revision">
                        🔄 Needs Revision
                      </SelectItem>
                    </Select>

                    <Select
                      label="Recommended Difficulty"
                      selectedKeys={reviewForm.recommendedDifficulty ? [reviewForm.recommendedDifficulty] : []}
                      onSelectionChange={(keys) => setReviewForm({
                        ...reviewForm,
                        recommendedDifficulty: Array.from(keys)[0] || ''
                      })}
                    >
                      <SelectItem key="beginner">Beginner</SelectItem>
                      <SelectItem key="intermediate">Intermediate</SelectItem>
                      <SelectItem key="advanced">Advanced</SelectItem>
                    </Select>
                  </div>

                  <Textarea
                    label="Review Notes"
                    placeholder="Provide detailed feedback about this submission..."
                    value={reviewForm.reviewNotes}
                    onChange={(e) => setReviewForm({ ...reviewForm, reviewNotes: e.target.value })}
                    minRows={3}
                    className="mb-4"
                  />

                  {reviewForm.action === 'approved' && (
                    <div className="space-y-3 p-4 bg-success-50 rounded-lg">
                      <h6 className="font-semibold text-success">Approval Options</h6>
                      <div className="flex flex-col gap-2">
                        <label className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={reviewForm.addToCatalog}
                            onChange={(e) => setReviewForm({
                              ...reviewForm,
                              addToCatalog: e.target.checked
                            })}
                          />
                          <span className="text-sm">Add to course catalog immediately</span>
                        </label>
                        <label className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={reviewForm.markForVetting}
                            onChange={(e) => setReviewForm({
                              ...reviewForm,
                              markForVetting: e.target.checked
                            })}
                          />
                          <span className="text-sm">Mark for skill vetting system</span>
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onReviewClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={submitReview}
              isLoading={loading}
              isDisabled={!reviewForm.action}
            >
              Submit Review
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

// Submissions Table Component
const SubmissionsTable = ({ submissions, loading, onReview, showActions = true }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'success';
      case 'rejected': return 'danger';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'auto_approve_ready': return 'success';
      case 'high_priority': return 'danger';
      case 'normal': return 'primary';
      default: return 'default';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  if (submissions.length === 0) {
    return (
      <div className="text-center py-12">
        <Eye className="w-16 h-16 mx-auto mb-4 text-default-400" />
        <h3 className="text-xl font-semibold mb-2">No submissions found</h3>
        <p className="text-default-600">
          There are no video submissions in this category.
        </p>
      </div>
    );
  }

  return (
    <Table aria-label="Video submissions table">
      <TableHeader>
        <TableColumn>VIDEO</TableColumn>
        <TableColumn>SUBMITTER</TableColumn>
        <TableColumn>STATUS</TableColumn>
        <TableColumn>COMMUNITY VOTES</TableColumn>
        <TableColumn>SUBMITTED</TableColumn>
        {showActions && <TableColumn>ACTIONS</TableColumn>}
      </TableHeader>
      <TableBody>
        {submissions.map((submission) => (
          <TableRow key={submission.id}>
            <TableCell>
              <div className="flex items-center gap-3">
                <img
                  src={submission.thumbnail_url || `https://img.youtube.com/vi/${submission.video_id}/mqdefault.jpg`}
                  alt={submission.title}
                  className="w-16 h-12 object-cover rounded"
                />
                <div>
                  <div className="font-semibold line-clamp-1">{submission.title}</div>
                  <div className="text-sm text-default-600">
                    by {submission.channel_name}
                  </div>
                  {submission.duration_minutes && (
                    <div className="text-xs text-default-500">
                      {submission.duration_minutes} minutes
                    </div>
                  )}
                </div>
              </div>
            </TableCell>
            <TableCell>
              <div>
                <div className="font-medium">
                  {submission.submitter_full_name || 'Unknown'}
                </div>
                <div className="text-sm text-default-600">
                  {submission.submitter_email}
                </div>
              </div>
            </TableCell>
            <TableCell>
              <div className="flex flex-col gap-1">
                <Chip
                  size="sm"
                  color={getStatusColor(submission.status)}
                  variant="flat"
                >
                  {submission.status}
                </Chip>
                {submission.review_priority !== 'normal' && (
                  <Chip
                    size="sm"
                    color={getPriorityColor(submission.review_priority)}
                    variant="dot"
                  >
                    {submission.review_priority.replace('_', ' ')}
                  </Chip>
                )}
              </div>
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <ThumbsUp className="w-4 h-4 text-success" />
                  <span className="text-sm">{submission.upvotes || 0}</span>
                </div>
                <div className="flex items-center gap-1">
                  <ThumbsDown className="w-4 h-4 text-danger" />
                  <span className="text-sm">{submission.downvotes || 0}</span>
                </div>
                <Badge
                  color={(submission.net_votes || 0) >= 0 ? 'success' : 'danger'}
                  variant="flat"
                >
                  {submission.net_votes || 0}
                </Badge>
              </div>
            </TableCell>
            <TableCell>
              <div className="text-sm">
                {formatDate(submission.created_at)}
              </div>
            </TableCell>
            {showActions && (
              <TableCell>
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  onPress={() => onReview(submission)}
                >
                  Review
                </Button>
              </TableCell>
            )}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default VideoVettingDashboard;
