-- Diagnos<PERSON> and Fix Script for Earn Page Database Issues
-- This script will check what exists and create what's missing

-- ============================================================================
-- DIAGNOSTIC QUERIES - Check what exists
-- ============================================================================

-- Check if tables exist
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename IN ('payment_transactions', 'escrow_accounts', 'collaboration_requests', 'revenue_entries')
ORDER BY tablename;

-- Check projects table columns
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if uuid extension is enabled
SELECT * FROM pg_extension WHERE extname = 'uuid-ossp';

-- ============================================================================
-- ENABLE REQUIRED EXTENSIONS
-- ============================================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- CREATE TABLES WITH EXPLICIT SCHEMA
-- ============================================================================

-- Drop and recreate payment_transactions table
DROP TABLE IF EXISTS public.payment_transactions CASCADE;
CREATE TABLE public.payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Transaction parties
    from_user_id UUID NOT NULL,
    to_user_id UUID NOT NULL,
    
    -- Transaction details
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    payment_method TEXT DEFAULT 'ach_standard',
    
    -- Transaction status
    status TEXT DEFAULT 'pending',
    transaction_id TEXT,
    
    -- Transaction metadata
    description TEXT,
    reference_id TEXT,
    reference_type TEXT,
    
    -- Timing information
    initiated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Fee information
    platform_fee DECIMAL(10,2) DEFAULT 0,
    total_fees DECIMAL(10,2) DEFAULT 0,
    
    -- Error handling
    failure_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT payment_transactions_status_check 
        CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled'))
);

-- Drop and recreate escrow_accounts table
DROP TABLE IF EXISTS public.escrow_accounts CASCADE;
CREATE TABLE public.escrow_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Account parties
    depositor_user_id UUID NOT NULL,
    beneficiary_user_id UUID NOT NULL,
    
    -- Account details
    balance DECIMAL(15,2) DEFAULT 0,
    currency TEXT DEFAULT 'USD',
    
    -- Account status
    status TEXT DEFAULT 'active',
    
    -- Account metadata
    project_id UUID,
    description TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT escrow_accounts_status_check 
        CHECK (status IN ('active', 'frozen', 'closed'))
);

-- Drop and recreate collaboration_requests table
DROP TABLE IF EXISTS public.collaboration_requests CASCADE;
CREATE TABLE public.collaboration_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Request details
    title TEXT NOT NULL,
    description TEXT,
    project_type TEXT DEFAULT 'software',
    
    -- Requirements
    required_skills TEXT[],
    experience_level TEXT DEFAULT 'intermediate',
    estimated_hours INTEGER,
    
    -- Compensation
    budget_min DECIMAL(10,2),
    budget_max DECIMAL(10,2),
    payment_type TEXT DEFAULT 'fixed',
    
    -- Request metadata
    created_by UUID NOT NULL,
    project_id UUID,
    
    -- Status and timing
    status TEXT DEFAULT 'open',
    deadline DATE,
    
    -- Location and remote work
    location TEXT,
    is_remote BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT collaboration_requests_experience_check 
        CHECK (experience_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    CONSTRAINT collaboration_requests_payment_type_check 
        CHECK (payment_type IN ('fixed', 'hourly', 'milestone', 'equity')),
    CONSTRAINT collaboration_requests_status_check 
        CHECK (status IN ('open', 'in_progress', 'completed', 'cancelled'))
);

-- Drop and recreate revenue_entries table
DROP TABLE IF EXISTS public.revenue_entries CASCADE;
CREATE TABLE public.revenue_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Revenue details
    project_id UUID NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    
    -- Revenue metadata
    source TEXT,
    description TEXT,
    
    -- Status and verification
    status TEXT DEFAULT 'pending',
    verified_by UUID,
    verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Timing
    revenue_date DATE DEFAULT CURRENT_DATE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT revenue_entries_status_check 
        CHECK (status IN ('pending', 'verified', 'disputed', 'cancelled'))
);

-- ============================================================================
-- ADD MISSING COLUMNS TO PROJECTS TABLE
-- ============================================================================

-- Add total_revenue column to projects table
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'projects' 
        AND column_name = 'total_revenue'
    ) THEN
        ALTER TABLE public.projects ADD COLUMN total_revenue DECIMAL(15,2) DEFAULT 0;
        RAISE NOTICE 'Added total_revenue column to projects table';
    ELSE
        RAISE NOTICE 'total_revenue column already exists in projects table';
    END IF;
END $$;

-- Add revenue_distribution_model column to projects table
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'projects' 
        AND column_name = 'revenue_distribution_model'
    ) THEN
        ALTER TABLE public.projects ADD COLUMN revenue_distribution_model TEXT DEFAULT 'equal_split';
        RAISE NOTICE 'Added revenue_distribution_model column to projects table';
    ELSE
        RAISE NOTICE 'revenue_distribution_model column already exists in projects table';
    END IF;
END $$;

-- ============================================================================
-- CREATE INDEXES
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_payment_transactions_from_user ON public.payment_transactions(from_user_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_to_user ON public.payment_transactions(to_user_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON public.payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_created_at ON public.payment_transactions(created_at);

CREATE INDEX IF NOT EXISTS idx_escrow_accounts_depositor ON public.escrow_accounts(depositor_user_id);
CREATE INDEX IF NOT EXISTS idx_escrow_accounts_beneficiary ON public.escrow_accounts(beneficiary_user_id);
CREATE INDEX IF NOT EXISTS idx_escrow_accounts_project ON public.escrow_accounts(project_id);

CREATE INDEX IF NOT EXISTS idx_collaboration_requests_created_by ON public.collaboration_requests(created_by);
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_status ON public.collaboration_requests(status);
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_project_type ON public.collaboration_requests(project_type);

CREATE INDEX IF NOT EXISTS idx_revenue_entries_project_id ON public.revenue_entries(project_id);
CREATE INDEX IF NOT EXISTS idx_revenue_entries_status ON public.revenue_entries(status);

-- ============================================================================
-- ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.escrow_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.revenue_entries ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- CREATE RLS POLICIES
-- ============================================================================

-- Payment transactions policies
DROP POLICY IF EXISTS "payment_transactions_select_policy" ON public.payment_transactions;
CREATE POLICY "payment_transactions_select_policy" ON public.payment_transactions
    FOR SELECT USING (
        auth.uid() = from_user_id OR 
        auth.uid() = to_user_id OR
        auth.uid() IS NULL  -- Allow anonymous access for now
    );

DROP POLICY IF EXISTS "payment_transactions_insert_policy" ON public.payment_transactions;
CREATE POLICY "payment_transactions_insert_policy" ON public.payment_transactions
    FOR INSERT WITH CHECK (auth.uid() = from_user_id OR auth.uid() IS NULL);

-- Escrow accounts policies
DROP POLICY IF EXISTS "escrow_accounts_select_policy" ON public.escrow_accounts;
CREATE POLICY "escrow_accounts_select_policy" ON public.escrow_accounts
    FOR SELECT USING (
        auth.uid() = depositor_user_id OR 
        auth.uid() = beneficiary_user_id OR
        auth.uid() IS NULL  -- Allow anonymous access for now
    );

-- Collaboration requests policies
DROP POLICY IF EXISTS "collaboration_requests_select_policy" ON public.collaboration_requests;
CREATE POLICY "collaboration_requests_select_policy" ON public.collaboration_requests
    FOR SELECT USING (true);  -- Allow all users to view for now

DROP POLICY IF EXISTS "collaboration_requests_insert_policy" ON public.collaboration_requests;
CREATE POLICY "collaboration_requests_insert_policy" ON public.collaboration_requests
    FOR INSERT WITH CHECK (auth.uid() = created_by OR auth.uid() IS NULL);

DROP POLICY IF EXISTS "collaboration_requests_update_policy" ON public.collaboration_requests;
CREATE POLICY "collaboration_requests_update_policy" ON public.collaboration_requests
    FOR UPDATE USING (auth.uid() = created_by OR auth.uid() IS NULL);

-- Revenue entries policies
DROP POLICY IF EXISTS "revenue_entries_select_policy" ON public.revenue_entries;
CREATE POLICY "revenue_entries_select_policy" ON public.revenue_entries
    FOR SELECT USING (true);  -- Allow all users to view for now

DROP POLICY IF EXISTS "revenue_entries_all_policy" ON public.revenue_entries;
CREATE POLICY "revenue_entries_all_policy" ON public.revenue_entries
    FOR ALL USING (true);  -- Allow all users to manage for now

-- ============================================================================
-- INSERT SAMPLE DATA
-- ============================================================================

-- Insert sample collaboration requests
INSERT INTO public.collaboration_requests (
    title,
    description,
    project_type,
    required_skills,
    experience_level,
    estimated_hours,
    budget_min,
    budget_max,
    payment_type,
    created_by,
    status,
    is_remote
) VALUES
(
    'Frontend Developer for React Dashboard',
    'Looking for an experienced React developer to build a modern dashboard interface with real-time data visualization.',
    'software',
    ARRAY['React', 'JavaScript', 'CSS', 'TypeScript'],
    'intermediate',
    40,
    2000.00,
    3500.00,
    'fixed',
    '********-0000-0000-0000-********0000',  -- Placeholder UUID
    'open',
    true
),
(
    'UI/UX Designer for Mobile App',
    'Need a creative designer to create user interfaces and user experience flows for a new mobile application.',
    'design',
    ARRAY['Figma', 'UI Design', 'UX Research', 'Prototyping'],
    'intermediate',
    30,
    1500.00,
    2500.00,
    'fixed',
    '********-0000-0000-0000-********0000',  -- Placeholder UUID
    'open',
    true
),
(
    'Backend API Development',
    'Seeking a backend developer to create RESTful APIs and database architecture for a SaaS platform.',
    'software',
    ARRAY['Node.js', 'PostgreSQL', 'API Design', 'Authentication'],
    'advanced',
    60,
    3000.00,
    5000.00,
    'fixed',
    '********-0000-0000-0000-********0000',  -- Placeholder UUID
    'open',
    true
) ON CONFLICT DO NOTHING;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify tables were created
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename IN ('payment_transactions', 'escrow_accounts', 'collaboration_requests', 'revenue_entries')
ORDER BY tablename;

-- Verify projects table columns
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND table_schema = 'public'
AND column_name IN ('total_revenue', 'revenue_distribution_model')
ORDER BY column_name;

-- Count sample data
SELECT 'collaboration_requests' as table_name, COUNT(*) as row_count FROM public.collaboration_requests
UNION ALL
SELECT 'payment_transactions' as table_name, COUNT(*) as row_count FROM public.payment_transactions
UNION ALL
SELECT 'escrow_accounts' as table_name, COUNT(*) as row_count FROM public.escrow_accounts
UNION ALL
SELECT 'revenue_entries' as table_name, COUNT(*) as row_count FROM public.revenue_entries;

-- Add table comments
COMMENT ON TABLE public.payment_transactions IS 'Records all payment transactions between users';
COMMENT ON TABLE public.escrow_accounts IS 'Manages escrow accounts for secure project funding';
COMMENT ON TABLE public.collaboration_requests IS 'Stores gigwork and collaboration opportunity requests';
COMMENT ON TABLE public.revenue_entries IS 'Tracks revenue entries for projects and royalty calculations';
