-- Targeted Terminology Update Migration
-- Based on actual database state: minimal data, safe to add new columns
-- Date: 2025-06-25

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- PHASE 1: ADD STUDIO TERMINOLOGY TO TEAMS TABLE
-- ============================================================================

-- Add studio_type column to teams table
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS studio_type TEXT DEFAULT 'emerging' 
CHECK (studio_type IN ('emerging', 'established', 'solo'));

COMMENT ON COLUMN public.teams.studio_type IS 'Type of studio: emerging (new/growing), established (mature), solo (individual)';

-- ============================================================================
-- PHASE 2: ADD PROJECT TERMINOLOGY TO PROJECTS TABLE  
-- ============================================================================

-- Add project_type column if it doesn't exist (it might already exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_type') THEN
        ALTER TABLE public.projects ADD COLUMN project_type TEXT DEFAULT 'software' 
        CHECK (project_type IN ('software', 'game', 'film', 'music', 'art', 'business', 'research', 'other'));
    END IF;
END $$;

-- Add studio_id column to link projects to studios
ALTER TABLE public.projects 
ADD COLUMN IF NOT EXISTS studio_id UUID REFERENCES public.teams(id);

COMMENT ON COLUMN public.projects.project_type IS 'Type of project: software, game, film, music, art, business, research, other';
COMMENT ON COLUMN public.projects.studio_id IS 'Reference to the studio that owns this project';

-- ============================================================================
-- PHASE 3: ADD MISSION TERMINOLOGY TO TASKS TABLE
-- ============================================================================

-- Add task_category column for mission/bounty/task classification
ALTER TABLE public.tasks 
ADD COLUMN IF NOT EXISTS task_category TEXT DEFAULT 'task' 
CHECK (task_category IN ('mission', 'bounty', 'task'));

-- Add mission-specific columns
ALTER TABLE public.tasks 
ADD COLUMN IF NOT EXISTS mission_type TEXT 
CHECK (mission_type IN ('skill', 'collaboration', 'achievement', 'exploration', 'social'));

ALTER TABLE public.tasks 
ADD COLUMN IF NOT EXISTS mission_requirements JSONB;

ALTER TABLE public.tasks 
ADD COLUMN IF NOT EXISTS mission_rewards JSONB;

-- Add comments for mission columns
COMMENT ON COLUMN public.tasks.task_category IS 'Category: mission (gamified internal), bounty (public reward), task (simple work item)';
COMMENT ON COLUMN public.tasks.mission_type IS 'Type of mission: skill, collaboration, achievement, exploration, social';
COMMENT ON COLUMN public.tasks.mission_requirements IS 'JSON object defining mission completion requirements and criteria';
COMMENT ON COLUMN public.tasks.mission_rewards IS 'JSON object defining mission completion rewards and benefits';

-- ============================================================================
-- PHASE 4: ADD PEOPLE TYPE SYSTEM TO TEAM_MEMBERS TABLE
-- ============================================================================

-- Add collaboration type system
ALTER TABLE public.team_members 
ADD COLUMN IF NOT EXISTS collaboration_type TEXT DEFAULT 'studio_member' 
CHECK (collaboration_type IN ('studio_member', 'contractor', 'specialist'));

ALTER TABLE public.team_members 
ADD COLUMN IF NOT EXISTS engagement_duration TEXT DEFAULT 'permanent'
CHECK (engagement_duration IN ('permanent', 'project_based', 'one_off'));

ALTER TABLE public.team_members 
ADD COLUMN IF NOT EXISTS specialization TEXT[];

-- Add comments for people type columns
COMMENT ON COLUMN public.team_members.collaboration_type IS 'Type of collaboration: studio_member (permanent), contractor (project-based), specialist (one-off)';
COMMENT ON COLUMN public.team_members.engagement_duration IS 'Duration of engagement: permanent, project_based, one_off';
COMMENT ON COLUMN public.team_members.specialization IS 'Array of specializations for specialists (skills, expertise areas)';

-- ============================================================================
-- PHASE 5: CREATE NEW TERMINOLOGY TABLES
-- ============================================================================

-- Create studio_invitations table
CREATE TABLE IF NOT EXISTS public.studio_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    studio_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    invited_user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    invited_by_user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'member',
    collaboration_type TEXT DEFAULT 'studio_member' CHECK (collaboration_type IN ('studio_member', 'contractor', 'specialist')),
    engagement_duration TEXT DEFAULT 'permanent' CHECK (engagement_duration IN ('permanent', 'project_based', 'one_off')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- Create studio_preferences table
CREATE TABLE IF NOT EXISTS public.studio_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    studio_id UUID REFERENCES public.teams(id) ON DELETE CASCADE UNIQUE,
    settings JSONB DEFAULT '{}',
    notification_preferences JSONB DEFAULT '{}',
    collaboration_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_missions table
CREATE TABLE IF NOT EXISTS public.user_missions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    mission_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'in_progress', 'completed', 'failed', 'abandoned')),
    progress JSONB DEFAULT '{}',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, mission_id)
);

-- Add table comments
COMMENT ON TABLE public.studio_invitations IS 'Invitations to join studios with role-based access and collaboration types';
COMMENT ON TABLE public.studio_preferences IS 'Studio-specific configuration and preferences';
COMMENT ON TABLE public.user_missions IS 'User mission progress tracking for gamified collaboration';

-- ============================================================================
-- PHASE 6: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes for new columns
CREATE INDEX IF NOT EXISTS idx_teams_studio_type ON public.teams(studio_type) WHERE studio_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_projects_studio_id ON public.projects(studio_id) WHERE studio_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_projects_project_type ON public.projects(project_type) WHERE project_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_task_category ON public.tasks(task_category) WHERE task_category IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_mission_type ON public.tasks(mission_type) WHERE mission_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_team_members_collaboration_type ON public.team_members(collaboration_type) WHERE collaboration_type IS NOT NULL;

-- Indexes for new tables
CREATE INDEX IF NOT EXISTS idx_studio_invitations_studio_id ON public.studio_invitations(studio_id);
CREATE INDEX IF NOT EXISTS idx_studio_invitations_invited_user_id ON public.studio_invitations(invited_user_id);
CREATE INDEX IF NOT EXISTS idx_studio_invitations_status ON public.studio_invitations(status);
CREATE INDEX IF NOT EXISTS idx_studio_preferences_studio_id ON public.studio_preferences(studio_id);
CREATE INDEX IF NOT EXISTS idx_user_missions_user_id ON public.user_missions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_missions_mission_id ON public.user_missions(mission_id);
CREATE INDEX IF NOT EXISTS idx_user_missions_status ON public.user_missions(status);

-- ============================================================================
-- PHASE 7: UPDATE EXISTING TASKS (MINIMAL DATA)
-- ============================================================================

-- Update the 4 existing tasks to have default task_category
UPDATE public.tasks 
SET task_category = 'task' 
WHERE task_category IS NULL;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 Targeted Terminology Update Migration Complete!';
    RAISE NOTICE '📋 Summary of changes:';
    RAISE NOTICE '   • Added studio_type column to teams table';
    RAISE NOTICE '   • Added project_type and studio_id columns to projects table';
    RAISE NOTICE '   • Added mission columns to tasks table (task_category, mission_type, mission_requirements, mission_rewards)';
    RAISE NOTICE '   • Added people type system to team_members table (collaboration_type, engagement_duration, specialization)';
    RAISE NOTICE '   • Created studio_invitations, studio_preferences, and user_missions tables';
    RAISE NOTICE '   • Created indexes for all new columns and tables';
    RAISE NOTICE '   • Updated 4 existing tasks with default task_category';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 New terminology is ready to use:';
    RAISE NOTICE '   • Alliance → Studio';
    RAISE NOTICE '   • Venture → Project';
    RAISE NOTICE '   • Quest → Mission';
    RAISE NOTICE '   • People Types: Studio Members, Contractors, Specialists';
    RAISE NOTICE '';
    RAISE NOTICE '📊 Database state: 4 tasks updated, all new columns and tables created';
END $$;
