-- ============================================================================
-- COLLABORATION & COMMUNICATION SYSTEM MIGRATION
-- ============================================================================
-- Creates comprehensive messaging, file sharing, and activity feed infrastructure
-- for alliance collaboration and team communication.

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- DIRECT MESSAGING SYSTEM
-- ============================================================================

-- Create conversations table for organizing messages
CREATE TABLE IF NOT EXISTS public.conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_type VARCHAR(20) DEFAULT 'direct' CHECK (conversation_type IN ('direct', 'group', 'alliance', 'project')),
    title VARCHAR(255), -- For group conversations
    description TEXT, -- For group conversations
    
    -- Context linking
    alliance_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    
    -- Conversation settings
    is_archived BOOLEAN DEFAULT false,
    is_muted BOOLEAN DEFAULT false,
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Metadata
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create conversation_participants table for managing who's in each conversation
CREATE TABLE IF NOT EXISTS public.conversation_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member')),
    
    -- Participation settings
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    left_at TIMESTAMP WITH TIME ZONE,
    is_muted BOOLEAN DEFAULT false,
    last_read_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Permissions
    can_add_members BOOLEAN DEFAULT false,
    can_remove_members BOOLEAN DEFAULT false,
    can_edit_conversation BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure unique participation per conversation
    CONSTRAINT conversation_participants_unique UNIQUE (conversation_id, user_id)
);

-- Create messages table for storing all messages
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Message content
    content TEXT,
    message_type VARCHAR(30) DEFAULT 'text' CHECK (
        message_type IN ('text', 'file', 'image', 'system', 'project_invite', 'collaboration_request', 'task_assignment')
    ),
    
    -- Threading support
    reply_to_id UUID REFERENCES public.messages(id) ON DELETE SET NULL,
    thread_id UUID, -- For grouping threaded messages
    
    -- File attachments
    attachment_url TEXT,
    attachment_name TEXT,
    attachment_size INTEGER, -- Size in bytes
    attachment_type VARCHAR(100), -- MIME type
    
    -- Message metadata
    metadata JSONB DEFAULT '{}', -- Additional context data
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create message_reactions table for emoji reactions
CREATE TABLE IF NOT EXISTS public.message_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reaction VARCHAR(10) NOT NULL, -- Emoji or reaction code
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure unique reaction per user per message
    CONSTRAINT message_reactions_unique UNIQUE (message_id, user_id, reaction)
);

-- ============================================================================
-- FILE SHARING SYSTEM
-- ============================================================================

-- Create shared_files table for file sharing and collaboration
CREATE TABLE IF NOT EXISTS public.shared_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- File details
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL, -- Size in bytes
    file_type VARCHAR(100) NOT NULL, -- MIME type
    file_url TEXT NOT NULL, -- Storage URL
    file_hash VARCHAR(64), -- For deduplication
    
    -- Upload details
    uploaded_by UUID NOT NULL REFERENCES auth.users(id),
    upload_source VARCHAR(50) DEFAULT 'direct' CHECK (
        upload_source IN ('direct', 'message', 'project', 'alliance', 'task')
    ),
    
    -- Context linking
    conversation_id UUID REFERENCES public.conversations(id) ON DELETE SET NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    alliance_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    
    -- File metadata
    description TEXT,
    tags JSONB DEFAULT '[]', -- Array of tags
    metadata JSONB DEFAULT '{}', -- Additional file metadata
    
    -- Access control
    visibility VARCHAR(20) DEFAULT 'private' CHECK (visibility IN ('public', 'alliance', 'project', 'private')),
    download_count INTEGER DEFAULT 0,
    
    -- File status
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create file_permissions table for granular file access control
CREATE TABLE IF NOT EXISTS public.file_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_id UUID NOT NULL REFERENCES public.shared_files(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    
    -- Permission types
    permission_type VARCHAR(20) DEFAULT 'view' CHECK (permission_type IN ('view', 'download', 'edit', 'delete', 'share')),
    granted_by UUID NOT NULL REFERENCES auth.users(id),
    expires_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Either user_id or team_id must be provided
    CONSTRAINT file_permissions_target_check CHECK (
        (user_id IS NOT NULL) OR (team_id IS NOT NULL)
    )
);

-- ============================================================================
-- ACTIVITY FEEDS & NOTIFICATIONS
-- ============================================================================

-- Create activity_feeds table for tracking user and team activities
CREATE TABLE IF NOT EXISTS public.activity_feeds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Activity details
    activity_type VARCHAR(50) NOT NULL CHECK (
        activity_type IN (
            'message_sent', 'file_shared', 'project_created', 'project_updated', 
            'task_completed', 'alliance_joined', 'alliance_created', 'skill_endorsed',
            'friend_request_sent', 'friend_request_accepted', 'collaboration_started'
        )
    ),
    activity_title VARCHAR(255) NOT NULL,
    activity_description TEXT,
    
    -- Actor and targets
    actor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Context linking
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    alliance_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    conversation_id UUID REFERENCES public.conversations(id) ON DELETE CASCADE,
    message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE,
    file_id UUID REFERENCES public.shared_files(id) ON DELETE CASCADE,
    
    -- Activity metadata
    metadata JSONB DEFAULT '{}', -- Additional context data
    visibility VARCHAR(20) DEFAULT 'alliance' CHECK (visibility IN ('public', 'alliance', 'project', 'private')),
    
    -- Engagement tracking
    view_count INTEGER DEFAULT 0,
    reaction_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create activity_reactions table for activity engagement
CREATE TABLE IF NOT EXISTS public.activity_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    activity_id UUID NOT NULL REFERENCES public.activity_feeds(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reaction_type VARCHAR(20) DEFAULT 'like' CHECK (
        reaction_type IN ('like', 'love', 'celebrate', 'support', 'insightful')
    ),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure unique reaction per user per activity
    CONSTRAINT activity_reactions_unique UNIQUE (activity_id, user_id, reaction_type)
);

-- Create notification_preferences table for user notification settings
CREATE TABLE IF NOT EXISTS public.notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    
    -- Message notifications
    direct_messages BOOLEAN DEFAULT true,
    group_messages BOOLEAN DEFAULT true,
    message_reactions BOOLEAN DEFAULT true,
    
    -- Activity notifications
    friend_requests BOOLEAN DEFAULT true,
    project_invitations BOOLEAN DEFAULT true,
    task_assignments BOOLEAN DEFAULT true,
    skill_endorsements BOOLEAN DEFAULT true,
    
    -- Alliance notifications
    alliance_updates BOOLEAN DEFAULT true,
    alliance_invitations BOOLEAN DEFAULT true,
    new_members BOOLEAN DEFAULT true,
    
    -- File sharing notifications
    file_shares BOOLEAN DEFAULT true,
    file_comments BOOLEAN DEFAULT false,
    
    -- Delivery preferences
    email_notifications BOOLEAN DEFAULT true,
    push_notifications BOOLEAN DEFAULT true,
    in_app_notifications BOOLEAN DEFAULT true,
    
    -- Frequency settings
    digest_frequency VARCHAR(20) DEFAULT 'daily' CHECK (digest_frequency IN ('immediate', 'hourly', 'daily', 'weekly', 'never')),
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- ADD MISSING COLUMNS
-- ============================================================================

-- Add missing columns to existing tables
ALTER TABLE public.conversation_participants ADD COLUMN IF NOT EXISTS conversation_id UUID REFERENCES public.conversations(id) ON DELETE CASCADE;
ALTER TABLE public.conversation_participants ADD COLUMN IF NOT EXISTS left_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.conversation_participants ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.messages ADD COLUMN IF NOT EXISTS conversation_id UUID REFERENCES public.conversations(id) ON DELETE CASCADE;
ALTER TABLE public.messages ADD COLUMN IF NOT EXISTS sender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.message_reactions ADD COLUMN IF NOT EXISTS message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE;
ALTER TABLE public.message_reactions ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.shared_files ADD COLUMN IF NOT EXISTS conversation_id UUID REFERENCES public.conversations(id) ON DELETE CASCADE;
ALTER TABLE public.shared_files ADD COLUMN IF NOT EXISTS uploaded_by UUID REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.shared_files ADD COLUMN IF NOT EXISTS alliance_id UUID;
ALTER TABLE public.shared_files ADD COLUMN IF NOT EXISTS project_id UUID;

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Conversations indexes
CREATE INDEX IF NOT EXISTS idx_conversations_type ON public.conversations(conversation_type);
CREATE INDEX IF NOT EXISTS idx_conversations_alliance ON public.conversations(alliance_id);
CREATE INDEX IF NOT EXISTS idx_conversations_project ON public.conversations(project_id);
CREATE INDEX IF NOT EXISTS idx_conversations_last_message ON public.conversations(last_message_at DESC);

-- Conversation participants indexes
CREATE INDEX IF NOT EXISTS idx_conversation_participants_conversation ON public.conversation_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_user ON public.conversation_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_active ON public.conversation_participants(user_id) WHERE left_at IS NULL;

-- Messages indexes
CREATE INDEX IF NOT EXISTS idx_messages_conversation ON public.messages(conversation_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_sender ON public.messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_thread ON public.messages(thread_id, created_at ASC);
CREATE INDEX IF NOT EXISTS idx_messages_reply_to ON public.messages(reply_to_id);
CREATE INDEX IF NOT EXISTS idx_messages_type ON public.messages(message_type);

-- Message reactions indexes
CREATE INDEX IF NOT EXISTS idx_message_reactions_message ON public.message_reactions(message_id);
CREATE INDEX IF NOT EXISTS idx_message_reactions_user ON public.message_reactions(user_id);

-- Shared files indexes
CREATE INDEX IF NOT EXISTS idx_shared_files_uploader ON public.shared_files(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_shared_files_conversation ON public.shared_files(conversation_id);
CREATE INDEX IF NOT EXISTS idx_shared_files_project ON public.shared_files(project_id);
CREATE INDEX IF NOT EXISTS idx_shared_files_alliance ON public.shared_files(alliance_id);
CREATE INDEX IF NOT EXISTS idx_shared_files_type ON public.shared_files(file_type);
CREATE INDEX IF NOT EXISTS idx_shared_files_visibility ON public.shared_files(visibility);

-- File permissions indexes
CREATE INDEX IF NOT EXISTS idx_file_permissions_file ON public.file_permissions(file_id);
CREATE INDEX IF NOT EXISTS idx_file_permissions_user ON public.file_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_file_permissions_team ON public.file_permissions(team_id);

-- Activity feeds indexes
CREATE INDEX IF NOT EXISTS idx_activity_feeds_actor ON public.activity_feeds(actor_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_target ON public.activity_feeds(target_user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_type ON public.activity_feeds(activity_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_alliance ON public.activity_feeds(alliance_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_project ON public.activity_feeds(project_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_visibility ON public.activity_feeds(visibility, created_at DESC);

-- Activity reactions indexes
CREATE INDEX IF NOT EXISTS idx_activity_reactions_activity ON public.activity_reactions(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_reactions_user ON public.activity_reactions(user_id);

-- Notification preferences indexes
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user ON public.notification_preferences(user_id);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shared_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_feeds ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;

-- Conversations policies
CREATE POLICY "Users can view conversations they participate in" ON public.conversations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = conversations.id 
            AND cp.user_id = auth.uid()
            AND cp.left_at IS NULL
        )
    );

CREATE POLICY "Users can create conversations" ON public.conversations
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Conversation admins can update conversations" ON public.conversations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = conversations.id 
            AND cp.user_id = auth.uid()
            AND cp.role IN ('admin', 'moderator')
        )
    );

-- Conversation participants policies
CREATE POLICY "Users can view participants in their conversations" ON public.conversation_participants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = conversation_participants.conversation_id 
            AND cp.user_id = auth.uid()
            AND cp.left_at IS NULL
        )
    );

CREATE POLICY "Users can join conversations they're invited to" ON public.conversation_participants
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Messages policies
CREATE POLICY "Users can view messages in their conversations" ON public.messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = messages.conversation_id 
            AND cp.user_id = auth.uid()
            AND cp.left_at IS NULL
        )
    );

CREATE POLICY "Users can send messages to their conversations" ON public.messages
    FOR INSERT WITH CHECK (
        sender_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = messages.conversation_id 
            AND cp.user_id = auth.uid()
            AND cp.left_at IS NULL
        )
    );

CREATE POLICY "Users can edit their own messages" ON public.messages
    FOR UPDATE USING (sender_id = auth.uid());

-- Shared files policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'shared_files' AND policyname = 'Users can view files they have access to') THEN
        CREATE POLICY "Users can view files they have access to" ON public.shared_files
            FOR SELECT USING (
                uploaded_by = auth.uid() OR
                visibility = 'public' OR
                EXISTS (
                    SELECT 1 FROM public.file_permissions fp
                    WHERE fp.file_id = shared_files.id
                    AND fp.user_id = auth.uid()
                )
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'shared_files' AND policyname = 'Users can upload files') THEN
        CREATE POLICY "Users can upload files" ON public.shared_files
            FOR INSERT WITH CHECK (uploaded_by = auth.uid());
    END IF;
END $$;

-- Activity feeds policies
CREATE POLICY "Users can view relevant activities" ON public.activity_feeds
    FOR SELECT USING (
        actor_id = auth.uid() OR
        target_user_id = auth.uid() OR
        visibility = 'public' OR
        (visibility = 'alliance' AND EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = activity_feeds.alliance_id 
            AND tm.user_id = auth.uid()
        ))
    );

CREATE POLICY "Users can create activities" ON public.activity_feeds
    FOR INSERT WITH CHECK (actor_id = auth.uid());

-- Notification preferences policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_preferences' AND policyname = 'Users can manage their own notification preferences') THEN
        CREATE POLICY "Users can manage their own notification preferences" ON public.notification_preferences
            FOR ALL USING (user_id = auth.uid());
    END IF;
END $$;

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to create a direct conversation between two users
CREATE OR REPLACE FUNCTION create_direct_conversation(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
    conversation_id UUID;
    existing_conversation_id UUID;
BEGIN
    -- Check if conversation already exists
    SELECT c.id INTO existing_conversation_id
    FROM conversations c
    JOIN conversation_participants cp1 ON c.id = cp1.conversation_id
    JOIN conversation_participants cp2 ON c.id = cp2.conversation_id
    WHERE c.conversation_type = 'direct'
    AND cp1.user_id = user1_id AND cp1.left_at IS NULL
    AND cp2.user_id = user2_id AND cp2.left_at IS NULL;
    
    IF existing_conversation_id IS NOT NULL THEN
        RETURN existing_conversation_id;
    END IF;
    
    -- Create new conversation
    INSERT INTO conversations (conversation_type, created_by)
    VALUES ('direct', user1_id)
    RETURNING id INTO conversation_id;
    
    -- Add both participants
    INSERT INTO conversation_participants (conversation_id, user_id, role)
    VALUES 
        (conversation_id, user1_id, 'member'),
        (conversation_id, user2_id, 'member');
    
    RETURN conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get unread message count for a user
CREATE OR REPLACE FUNCTION get_unread_message_count(user_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER
        FROM messages m
        JOIN conversation_participants cp ON m.conversation_id = cp.conversation_id
        WHERE cp.user_id = user_id
        AND cp.left_at IS NULL
        AND m.created_at > cp.last_read_at
        AND m.sender_id != user_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.conversations TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.conversation_participants TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.messages TO authenticated;
GRANT SELECT, INSERT, DELETE ON public.message_reactions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.shared_files TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.file_permissions TO authenticated;
GRANT SELECT, INSERT ON public.activity_feeds TO authenticated;
GRANT SELECT, INSERT, DELETE ON public.activity_reactions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.notification_preferences TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant execute on functions
GRANT EXECUTE ON FUNCTION create_direct_conversation TO authenticated;
GRANT EXECUTE ON FUNCTION get_unread_message_count TO authenticated;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.conversations IS 'Manages conversation threads for direct messages, groups, and project communication';
COMMENT ON TABLE public.conversation_participants IS 'Tracks who participates in each conversation with roles and permissions';
COMMENT ON TABLE public.messages IS 'Stores all messages with threading support and file attachments';
COMMENT ON TABLE public.message_reactions IS 'Emoji reactions and engagement on messages';
COMMENT ON TABLE public.shared_files IS 'File sharing system with access control and context linking';
COMMENT ON TABLE public.file_permissions IS 'Granular file access permissions for users and teams';
COMMENT ON TABLE public.activity_feeds IS 'Activity tracking for social feeds and collaboration insights';
COMMENT ON TABLE public.activity_reactions IS 'User engagement and reactions on activities';
COMMENT ON TABLE public.notification_preferences IS 'User preferences for notification delivery and frequency';

COMMENT ON FUNCTION create_direct_conversation(UUID, UUID) IS 'Creates or returns existing direct conversation between two users';
COMMENT ON FUNCTION get_unread_message_count(UUID) IS 'Returns count of unread messages for a user across all conversations';
