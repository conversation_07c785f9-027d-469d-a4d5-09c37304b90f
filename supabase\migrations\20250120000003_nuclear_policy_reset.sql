-- Nuclear Policy Reset - Complete RLS Policy Cleanup
-- This migration completely removes all RLS policies and creates simple, working ones

-- ============================================================================
-- 1. DISABLE RLS TEMPORARILY TO AVOID RECURSION
-- ============================================================================

-- Temporarily disable RLS to allow cleanup
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_contributors DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.contributions DISABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 2. DROP ALL EXISTING POLICIES COMPLETELY
-- ============================================================================

-- Drop ALL policies on users table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'users' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON public.users';
    END LOOP;
END $$;

-- Drop ALL policies on projects table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'projects' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON public.projects';
    END LOOP;
END $$;

-- Drop ALL policies on team_members table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'team_members' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON public.team_members';
    END LOOP;
END $$;

-- Drop ALL policies on project_contributors table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'project_contributors' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON public.project_contributors';
    END LOOP;
END $$;

-- Drop ALL policies on contributions table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'contributions' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON public.contributions';
    END LOOP;
END $$;

-- ============================================================================
-- 3. RE-ENABLE RLS
-- ============================================================================

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_contributors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contributions ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 4. CREATE SIMPLE, NON-RECURSIVE POLICIES
-- ============================================================================

-- USERS TABLE - Ultra simple policies
CREATE POLICY "users_select_own" ON public.users
    FOR SELECT USING (id = auth.uid());

CREATE POLICY "users_update_own" ON public.users
    FOR UPDATE USING (id = auth.uid());

CREATE POLICY "users_insert_own" ON public.users
    FOR INSERT WITH CHECK (id = auth.uid());

-- PROJECTS TABLE - Ultra simple policies
CREATE POLICY "projects_select_own" ON public.projects
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "projects_insert_own" ON public.projects
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "projects_update_own" ON public.projects
    FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "projects_delete_own" ON public.projects
    FOR DELETE USING (created_by = auth.uid());

-- TEAM_MEMBERS TABLE - Ultra simple policies
CREATE POLICY "team_members_select_own" ON public.team_members
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "team_members_insert_own" ON public.team_members
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "team_members_update_own" ON public.team_members
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "team_members_delete_own" ON public.team_members
    FOR DELETE USING (user_id = auth.uid());

-- PROJECT_CONTRIBUTORS TABLE - Ultra simple policies
CREATE POLICY "project_contributors_select_own" ON public.project_contributors
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "project_contributors_insert_own" ON public.project_contributors
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "project_contributors_update_own" ON public.project_contributors
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "project_contributors_delete_own" ON public.project_contributors
    FOR DELETE USING (user_id = auth.uid());

-- CONTRIBUTIONS TABLE - Ultra simple policies
CREATE POLICY "contributions_select_own" ON public.contributions
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "contributions_insert_own" ON public.contributions
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "contributions_update_own" ON public.contributions
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "contributions_delete_own" ON public.contributions
    FOR DELETE USING (user_id = auth.uid());

-- ============================================================================
-- 5. GRANT BASIC PERMISSIONS
-- ============================================================================

-- Grant basic permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.projects TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.team_members TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.project_contributors TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.contributions TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- 6. CREATE USER RECORD IF IT DOESN'T EXIST
-- ============================================================================

-- Insert user record for the current authenticated user if it doesn't exist
INSERT INTO public.users (id, email, display_name, is_premium, bio, social_links, stats)
SELECT 
    auth.uid(),
    (SELECT email FROM auth.users WHERE id = auth.uid()),
    'User',
    false,
    '',
    '{}',
    '{}'
WHERE auth.uid() IS NOT NULL
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 7. VERIFICATION QUERIES
-- ============================================================================

-- Test that we can now query without recursion
SELECT 'Testing users table - should work now' as test_name;
SELECT COUNT(*) as user_count FROM public.users WHERE id = auth.uid();

SELECT 'Testing projects table - should work now' as test_name;
SELECT COUNT(*) as project_count FROM public.projects WHERE created_by = auth.uid();

SELECT 'Testing team_members table - should work now' as test_name;
SELECT COUNT(*) as member_count FROM public.team_members WHERE user_id = auth.uid();

SELECT 'Testing project_contributors table - should work now' as test_name;
SELECT COUNT(*) as contributor_count FROM public.project_contributors WHERE user_id = auth.uid();

SELECT 'Testing contributions table - should work now' as test_name;
SELECT COUNT(*) as contribution_count FROM public.contributions WHERE user_id = auth.uid();

-- ============================================================================
-- 8. FINAL STATUS
-- ============================================================================

SELECT '🚀 NUCLEAR POLICY RESET COMPLETE' as status;
SELECT 'All RLS policies have been completely reset' as result_1;
SELECT 'Simple, non-recursive policies are now in place' as result_2;
SELECT 'All infinite recursion should be eliminated' as result_3;
SELECT 'Database should now work without 500 errors' as result_4;
