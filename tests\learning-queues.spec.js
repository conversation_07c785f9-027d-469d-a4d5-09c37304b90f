import { test, expect } from '@playwright/test';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Learning Queues', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
    
    // Navigate to learning center
    await page.goto('/learn');
    await page.waitForLoadState('networkidle');
  });

  test('should open learning queues modal', async ({ page }) => {
    // Open learning queues
    await page.click('button:has-text("My Queues")');
    
    // Check modal opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('h2:has-text("Learning Queues")')).toBeVisible();
    
    // Check stats cards
    await expect(page.locator('text=My Queues')).toBeVisible();
    await expect(page.locator('text=Total Items')).toBeVisible();
    await expect(page.locator('text=Completed')).toBeVisible();
    await expect(page.locator('text=Avg Progress')).toBeVisible();
    
    // Check tabs
    await expect(page.locator('[role="tab"]:has-text("My Queues")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Shared Queues")')).toBeVisible();
  });

  test('should create new learning queue', async ({ page }) => {
    await page.click('button:has-text("My Queues")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Click create new queue button
    const newQueueButton = page.locator('button:has-text("New Queue")');
    if (await newQueueButton.isVisible()) {
      await newQueueButton.click();
      await page.waitForTimeout(1000);
      
      // Check if create queue form opens
      await expect(page.locator('input[label*="Name"]')).toBeVisible();
      await expect(page.locator('textarea[label*="Description"]')).toBeVisible();
      
      // Fill queue details
      await page.fill('input[label*="Name"]', 'Test Learning Queue');
      await page.fill('textarea[label*="Description"]', 'A test queue for learning new skills');
      
      // Select difficulty level
      await page.selectOption('select[label*="Difficulty"]', 'beginner');
      
      // Fill estimated duration
      await page.fill('input[label*="Duration"]', '10');
      
      // Select some skills
      const skillChips = page.locator('[data-testid="skill-chip"]');
      const skillCount = await skillChips.count();
      if (skillCount > 0) {
        await skillChips.first().click();
        await skillChips.nth(1).click();
      }
      
      // Select categories
      const categoryChips = page.locator('[data-testid="category-chip"]');
      const categoryCount = await categoryChips.count();
      if (categoryCount > 0) {
        await categoryChips.first().click();
      }
      
      // Submit form
      await page.click('button:has-text("Create")');
      await page.waitForTimeout(2000);
      
      // Check for success message
      const successMessage = page.locator('text=created successfully');
      if (await successMessage.isVisible()) {
        console.log('Queue created successfully');
      }
    } else {
      // If no new queue button, check for empty state
      const emptyState = page.locator('text=No queues yet');
      if (await emptyState.isVisible()) {
        const createFirstButton = page.locator('button:has-text("Create your first queue")');
        if (await createFirstButton.isVisible()) {
          await createFirstButton.click();
          await page.waitForTimeout(1000);
          
          // Fill basic queue info
          await page.fill('input[label*="Name"]', 'My First Queue');
          await page.click('button:has-text("Create")');
          await page.waitForTimeout(2000);
        }
      }
    }
  });

  test('should manage queue items', async ({ page }) => {
    await page.click('button:has-text("My Queues")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Look for existing queues
    const queueCards = page.locator('[data-testid="queue-card"]');
    const queueCount = await queueCards.count();
    
    if (queueCount > 0) {
      // Select first queue
      await queueCards.first().click();
      await page.waitForTimeout(1000);
      
      // Check queue content area
      const queueContent = page.locator('[data-testid="queue-content"]');
      if (await queueContent.isVisible()) {
        // Look for add item button
        const addItemButton = page.locator('button:has-text("Add Item")');
        if (await addItemButton.isVisible()) {
          await addItemButton.click();
          await page.waitForTimeout(1000);
          
          // Check add item form
          await expect(page.locator('input[label*="Title"]')).toBeVisible();
          await expect(page.locator('textarea[label*="Description"]')).toBeVisible();
          
          // Fill item details
          await page.fill('input[label*="Title"]', 'Test Learning Item');
          await page.fill('textarea[label*="Description"]', 'A test learning item');
          await page.fill('input[label*="URL"]', 'https://www.youtube.com/watch?v=test');
          
          // Submit item
          await page.click('button:has-text("Add")');
          await page.waitForTimeout(1000);
        }
        
        // Test item interactions
        const queueItems = page.locator('[data-testid="queue-item"]');
        const itemCount = await queueItems.count();
        
        if (itemCount > 0) {
          // Test item reordering
          const moveUpButton = page.locator('button[aria-label*="Move up"]');
          const moveDownButton = page.locator('button[aria-label*="Move down"]');
          
          if (await moveUpButton.count() > 0) {
            await moveUpButton.first().click();
            await page.waitForTimeout(500);
          }
          
          if (await moveDownButton.count() > 0) {
            await moveDownButton.first().click();
            await page.waitForTimeout(500);
          }
          
          // Test item completion
          const completeButton = page.locator('button:has-text("Complete")');
          if (await completeButton.count() > 0) {
            await completeButton.first().click();
            await page.waitForTimeout(1000);
          }
        }
      }
    }
  });

  test('should browse shared queues', async ({ page }) => {
    await page.click('button:has-text("My Queues")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Switch to shared queues tab
    await page.click('[role="tab"]:has-text("Shared Queues")');
    await page.waitForTimeout(1000);
    
    // Check search functionality
    const searchInput = page.locator('input[placeholder*="Search shared queues"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('JavaScript');
      await page.waitForTimeout(1000);
    }
    
    // Check filter functionality
    const filterSelect = page.locator('select[placeholder*="Filter"]');
    if (await filterSelect.isVisible()) {
      await filterSelect.selectOption('popular');
      await page.waitForTimeout(1000);
    }
    
    // Look for shared queue cards
    const sharedQueueCards = page.locator('[data-testid="shared-queue-card"]');
    const sharedCount = await sharedQueueCards.count();
    
    if (sharedCount > 0) {
      // Test adding shared queue to personal collection
      const addToMyQueuesButton = page.locator('button:has-text("Add to My Queues")');
      if (await addToMyQueuesButton.count() > 0) {
        await addToMyQueuesButton.first().click();
        await page.waitForTimeout(2000);
        
        // Check for success message
        const successMessage = page.locator('text=added to your collection');
        if (await successMessage.isVisible()) {
          console.log('Shared queue added successfully');
        }
      }
      
      // Test queue preview
      const previewButton = page.locator('button[aria-label*="Preview"]');
      if (await previewButton.count() > 0) {
        await previewButton.first().click();
        await page.waitForTimeout(1000);
        // Preview functionality test
      }
      
      // Test sharing
      const shareButton = page.locator('button[aria-label*="Share"]');
      if (await shareButton.count() > 0) {
        await shareButton.first().click();
        await page.waitForTimeout(1000);
        
        // Check if share modal opens
        const shareModal = page.locator('[role="dialog"]:has-text("Share")');
        if (await shareModal.isVisible()) {
          await page.click('button:has-text("Cancel")');
        }
      }
    } else {
      // Check empty state
      await expect(page.locator('text=No shared queues found')).toBeVisible();
    }
  });

  test('should handle queue settings and preferences', async ({ page }) => {
    await page.click('button:has-text("My Queues")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Look for queue settings
    const queueCards = page.locator('[data-testid="queue-card"]');
    const queueCount = await queueCards.count();
    
    if (queueCount > 0) {
      // Select first queue
      await queueCards.first().click();
      await page.waitForTimeout(1000);
      
      // Look for queue settings/edit button
      const editButton = page.locator('button:has-text("Edit")').or(page.locator('button[aria-label*="Edit"]'));
      if (await editButton.isVisible()) {
        await editButton.click();
        await page.waitForTimeout(1000);
        
        // Check edit form
        const nameInput = page.locator('input[label*="Name"]');
        if (await nameInput.isVisible()) {
          // Test editing queue name
          await nameInput.fill('Updated Queue Name');
          
          // Test auto-advance setting
          const autoAdvanceCheckbox = page.locator('input[type="checkbox"]:near(text="Auto advance")');
          if (await autoAdvanceCheckbox.isVisible()) {
            await autoAdvanceCheckbox.click();
          }
          
          // Test public setting
          const publicCheckbox = page.locator('input[type="checkbox"]:near(text="Public")');
          if (await publicCheckbox.isVisible()) {
            await publicCheckbox.click();
          }
          
          // Save changes
          await page.click('button:has-text("Save")');
          await page.waitForTimeout(1000);
        }
      }
      
      // Test queue deletion
      const deleteButton = page.locator('button:has-text("Delete")').or(page.locator('button[aria-label*="Delete"]'));
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
        await page.waitForTimeout(500);
        
        // Handle confirmation dialog
        const confirmButton = page.locator('button:has-text("Confirm")').or(page.locator('button:has-text("Delete")'));
        if (await confirmButton.isVisible()) {
          // Don't actually delete in test
          await page.click('button:has-text("Cancel")');
        }
      }
    }
  });

  test('should track learning progress in queues', async ({ page }) => {
    await page.click('button:has-text("My Queues")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Check progress statistics
    const statsCards = page.locator('[data-testid="stats-card"]');
    const statsCount = await statsCards.count();
    
    expect(statsCount).toBeGreaterThanOrEqual(4); // Should have 4 stats cards
    
    // Look for progress bars
    const progressBars = page.locator('[role="progressbar"]');
    const progressCount = await progressBars.count();
    
    if (progressCount > 0) {
      console.log(`Found ${progressCount} progress indicators`);
    }
    
    // Check queue completion percentages
    const queueCards = page.locator('[data-testid="queue-card"]');
    const queueCount = await queueCards.count();
    
    for (let i = 0; i < Math.min(queueCount, 3); i++) {
      const queueCard = queueCards.nth(i);
      const progressBar = queueCard.locator('[role="progressbar"]');
      
      if (await progressBar.isVisible()) {
        const progressValue = await progressBar.getAttribute('aria-valuenow');
        console.log(`Queue ${i + 1} progress: ${progressValue}%`);
      }
    }
  });

  test('should handle queue sharing and collaboration', async ({ page }) => {
    await page.click('button:has-text("My Queues")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    const queueCards = page.locator('[data-testid="queue-card"]');
    const queueCount = await queueCards.count();
    
    if (queueCount > 0) {
      // Select first queue
      await queueCards.first().click();
      await page.waitForTimeout(1000);
      
      // Look for share button
      const shareButton = page.locator('button:has-text("Share")').or(page.locator('button[aria-label*="Share"]'));
      if (await shareButton.isVisible()) {
        await shareButton.click();
        await page.waitForTimeout(1000);
        
        // Check share modal
        const shareModal = page.locator('[role="dialog"]:has-text("Share")');
        if (await shareModal.isVisible()) {
          // Test different sharing options
          await expect(page.locator('[role="tab"]:has-text("Share Link")')).toBeVisible();
          await expect(page.locator('[role="tab"]:has-text("Share with Users")')).toBeVisible();
          
          // Test link sharing
          const copyButton = page.locator('button:has-text("Copy Link")');
          if (await copyButton.isVisible()) {
            await copyButton.click();
            await page.waitForTimeout(500);
          }
          
          // Test user sharing
          await page.click('[role="tab"]:has-text("Share with Users")');
          await page.waitForTimeout(500);
          
          const userSearch = page.locator('input[placeholder*="Search by name"]');
          if (await userSearch.isVisible()) {
            await userSearch.fill('test');
            await page.waitForTimeout(1000);
          }
          
          // Close share modal
          await page.click('button:has-text("Cancel")');
        }
      }
    }
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.click('button:has-text("My Queues")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Check if modal is properly sized for mobile
    const modal = page.locator('[role="dialog"]');
    const modalBox = await modal.boundingBox();
    
    expect(modalBox.width).toBeLessThanOrEqual(375);
    
    // Check if tabs are accessible on mobile
    const tabs = page.locator('[role="tab"]');
    const tabCount = await tabs.count();
    expect(tabCount).toBeGreaterThan(0);
    
    // Test mobile navigation within modal
    if (await page.locator('[role="tab"]:has-text("Shared Queues")').isVisible()) {
      await page.click('[role="tab"]:has-text("Shared Queues")');
      await page.waitForTimeout(1000);
    }
    
    // Close modal
    await page.click('button:has-text("Close")');
  });
});
