{"config": {"configFile": "C:\\Data\\Projects\\Royaltea\\playwright-visual.config.js", "rootDir": "C:/Data/Projects/Royaltea/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": "C:\\Data\\Projects\\Royaltea\\tests\\global-setup.js", "globalTeardown": "C:\\Data\\Projects\\Royaltea\\tests\\global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report-visual"}], ["json", {"outputFile": "test-results-visual.json"}], ["junit", {"outputFile": "test-results-visual.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Data/Projects/Royaltea/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Comprehensive Visual Tests - 5 Tests Only", "name": "Comprehensive Visual Tests - 5 Tests Only", "testDir": "C:/Data/Projects/Royaltea/tests", "testIgnore": [], "testMatch": ["**/e2e-comprehensive-*.spec.js"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.0", "workers": 1, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "cwd": "./client", "reuseExistingServer": true, "timeout": 120000, "stdout": "ignore", "stderr": "pipe"}}, "suites": [{"title": "e2e-comprehensive-flows.spec.js", "file": "e2e-comprehensive-flows.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Royaltea - 5 Comprehensive End-to-End Tests", "file": "e2e-comprehensive-flows.spec.js", "line": 14, "column": 6, "specs": [{"title": "1. Learning Center Complete Flow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Comprehensive Visual Tests - 5 Tests Only", "projectName": "Comprehensive Visual Tests - 5 Tests Only", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 4806, "error": {"message": "Error: Email input not found", "stack": "Error: Email input not found\n    at loginUser (C:\\Data\\Projects\\Royaltea\\tests\\e2e-comprehensive-flows.spec.js:165:15)\n    at C:\\Data\\Projects\\Royaltea\\tests\\e2e-comprehensive-flows.spec.js:259:5", "location": {"file": "C:\\Data\\Projects\\Royaltea\\tests\\e2e-comprehensive-flows.spec.js", "column": 15, "line": 165}, "snippet": "\u001b[0m \u001b[90m 163 |\u001b[39m         emailInput \u001b[33m=\u001b[39m anyInput\u001b[33m;\u001b[39m\n \u001b[90m 164 |\u001b[39m       } \u001b[36melse\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 165 |\u001b[39m         \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'Email input not found'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 166 |\u001b[39m       }\n \u001b[90m 167 |\u001b[39m     }\n \u001b[90m 168 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Data\\Projects\\Royaltea\\tests\\e2e-comprehensive-flows.spec.js", "column": 15, "line": 165}, "message": "Error: Email input not found\n\n\u001b[0m \u001b[90m 163 |\u001b[39m         emailInput \u001b[33m=\u001b[39m anyInput\u001b[33m;\u001b[39m\n \u001b[90m 164 |\u001b[39m       } \u001b[36melse\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 165 |\u001b[39m         \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'Email input not found'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 166 |\u001b[39m       }\n \u001b[90m 167 |\u001b[39m     }\n \u001b[90m 168 |\u001b[39m\u001b[0m\n\u001b[2m    at loginUser (C:\\Data\\Projects\\Royaltea\\tests\\e2e-comprehensive-flows.spec.js:165:15)\u001b[22m\n\u001b[2m    at C:\\Data\\Projects\\Royaltea\\tests\\e2e-comprehensive-flows.spec.js:259:5\u001b[22m"}], "stdout": [{"text": "🎓 Starting Learning Center Complete Flow Test\n"}, {"text": "🔐 Attempting login for: <EMAIL>\n"}, {"text": "🏠 First navigating to root page...\n"}, {"text": "📸 Screenshot saved: Root Page - Initial Load\n"}, {"text": "🏠 Root page - Inputs: 0, Forms: 0, Buttons: 0\n"}, {"text": "🔄 No login form on root, navigating to /login...\n"}, {"text": "📸 Screenshot saved: <PERSON><PERSON> Page - Initial Load\n"}, {"text": "📄 Page title: Royaltea - A Game Development Gigwork Platform\n"}, {"text": "🌐 Page URL: http://localhost:5173/login\n"}, {"text": "📝 Total inputs found: 0\n"}, {"text": "📋 Input details: []\n"}, {"text": "📋 Forms found: 0\n"}, {"text": "🔘 Buttons found: 0\n"}, {"text": "📄 Body text length: 15\n"}, {"text": "📄 Body text preview: \n    \n    \n  \n\n...\n"}, {"text": "📄 Body HTML length: 87\n"}, {"text": "📄 Body HTML preview: \n    <div id=\"root\"></div>\n    <script type=\"module\" src=\"/src/main.jsx\"></script>\n  \n\n...\n"}, {"text": "⚛️ React root div found: 1\n"}, {"text": "📜 Script tags found: 7\n"}, {"text": "🎨 CSS files loaded: 0\n"}, {"text": "⏳ Loading elements found: 0\n"}, {"text": "❌ Error elements found: 0\n"}, {"text": "📦 Div elements found: 1\n"}, {"text": "⏳ Waiting additional 3 seconds for content to load...\n"}, {"text": "📝 Inputs after wait: 0\n"}, {"text": "📋 Forms after wait: 0\n"}, {"text": "🔘 Buttons after wait: 0\n"}, {"text": "❌ Selector not found: input[type=\"email\"]\n"}, {"text": "❌ Selector not found: input[id=\"email\"]\n"}, {"text": "❌ Selector not found: input[placeholder*=\"email\"]\n"}, {"text": "❌ Selector not found: input[placeholder*=\"Enter email\"]\n"}, {"text": "❌ Selector not found: [data-slot=\"input\"][type=\"email\"]\n"}, {"text": "❌ Selector not found: .heroui-input input[type=\"email\"]\n"}, {"text": "❌ Selector not found: input\n"}, {"text": "❌ Could not find email input, taking screenshot for debugging\n"}, {"text": "📸 Screenshot saved: <PERSON><PERSON> Page - Email Input Not Found\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-15T01:57:37.278Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Data\\Projects\\Royaltea\\test-results\\e2e-comprehensive-flows-Ro-a09a6-arning-Center-Complete-Flow-Comprehensive-Visual-Tests---5-Tests-Only\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Data\\Projects\\Royaltea\\test-results\\e2e-comprehensive-flows-Ro-a09a6-arning-Center-Complete-Flow-Comprehensive-Visual-Tests---5-Tests-Only\\video.webm"}], "errorLocation": {"file": "C:\\Data\\Projects\\Royaltea\\tests\\e2e-comprehensive-flows.spec.js", "column": 15, "line": 165}}], "status": "unexpected"}], "id": "411f33630deb4999a28d-5909b11236427251e280", "file": "e2e-comprehensive-flows.spec.js", "line": 255, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-15T01:57:30.723Z", "duration": 11652.846000000001, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}