// Fix Migration Version Conflicts
// Rename migrations to avoid duplicate version numbers

import fs from 'fs';
import path from 'path';

const migrationsDir = 'supabase/migrations';

async function fixMigrationVersions() {
  console.log('🔧 Fixing Migration Version Conflicts...\n');

  try {
    // Get all migration files
    const files = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    console.log(`Found ${files.length} migration files`);

    // Group by version prefix to find duplicates
    const versionGroups = {};
    files.forEach(file => {
      const version = file.split('_')[0];
      if (!versionGroups[version]) {
        versionGroups[version] = [];
      }
      versionGroups[version].push(file);
    });

    // Find and fix duplicates
    let renamedCount = 0;
    for (const [version, fileList] of Object.entries(versionGroups)) {
      if (fileList.length > 1) {
        console.log(`\n⚠️ Found ${fileList.length} files with version ${version}:`);
        fileList.forEach((file, index) => {
          console.log(`   ${index + 1}. ${file}`);
        });

        // Rename duplicates (keep first one, rename others)
        for (let i = 1; i < fileList.length; i++) {
          const oldFile = fileList[i];
          const oldPath = path.join(migrationsDir, oldFile);
          
          // Create new version by incrementing
          const baseName = oldFile.substring(oldFile.indexOf('_'));
          const newVersion = incrementVersion(version, i);
          const newFile = newVersion + baseName;
          const newPath = path.join(migrationsDir, newFile);

          console.log(`   🔄 Renaming: ${oldFile} → ${newFile}`);
          
          try {
            fs.renameSync(oldPath, newPath);
            renamedCount++;
          } catch (error) {
            console.log(`   ❌ Error renaming ${oldFile}: ${error.message}`);
          }
        }
      }
    }

    console.log(`\n✅ Fixed ${renamedCount} migration version conflicts`);

    // Show final list
    const finalFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    console.log(`\n📋 Final migration list (${finalFiles.length} files):`);
    finalFiles.forEach((file, index) => {
      console.log(`${(index + 1).toString().padStart(2, '0')}. ${file}`);
    });

  } catch (error) {
    console.error('❌ Error fixing migration versions:', error.message);
  }
}

function incrementVersion(version, increment) {
  // Parse version like "20240116000006" 
  // Format: YYYYMMDDHHMMSS
  const year = version.substring(0, 4);
  const month = version.substring(4, 6);
  const day = version.substring(6, 8);
  const hour = version.substring(8, 10);
  const minute = version.substring(10, 12);
  const second = parseInt(version.substring(12, 14));

  // Increment seconds
  const newSecond = (second + increment).toString().padStart(2, '0');
  
  return year + month + day + hour + minute + newSecond;
}

// Run the fix
fixMigrationVersions();
