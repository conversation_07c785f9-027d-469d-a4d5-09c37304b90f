-- Terminology Update Migration: Alliance → Studio, Venture → Project, Quest → Mission
-- Adds new terminology columns to support the simplified naming convention
-- Date: 2025-06-25

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- PHASE 1: ADD NEW TERMINOLOGY COLUMNS
-- ============================================================================

-- Add studio terminology to teams table
DO $$
BEGIN
    -- Add studio_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'studio_type') THEN
        ALTER TABLE public.teams ADD COLUMN studio_type TEXT DEFAULT 'emerging' CHECK (studio_type IN ('emerging', 'established', 'solo'));
        RAISE NOTICE 'Added studio_type column to teams table';
    ELSE
        RAISE NOTICE 'studio_type column already exists in teams table';
    END IF;
END $$;

-- Add project terminology to projects table
DO $$
BEGIN
    -- Add project_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_type') THEN
        ALTER TABLE public.projects ADD COLUMN project_type TEXT DEFAULT 'software' CHECK (project_type IN ('software', 'game', 'film', 'music', 'art', 'business', 'research', 'other'));
        RAISE NOTICE 'Added project_type column to projects table';
    ELSE
        RAISE NOTICE 'project_type column already exists in projects table';
    END IF;

    -- Add studio_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'studio_id') THEN
        ALTER TABLE public.projects ADD COLUMN studio_id UUID REFERENCES public.teams(id);
        RAISE NOTICE 'Added studio_id column to projects table';
    ELSE
        RAISE NOTICE 'studio_id column already exists in projects table';
    END IF;
END $$;

-- Add mission terminology to tasks table
DO $$
BEGIN
    -- Add task_category column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'task_category') THEN
        ALTER TABLE public.tasks ADD COLUMN task_category TEXT DEFAULT 'task' CHECK (task_category IN ('mission', 'bounty', 'task'));
        RAISE NOTICE 'Added task_category column to tasks table';
    ELSE
        -- Update constraint to include mission
        ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_task_category_check;
        ALTER TABLE public.tasks ADD CONSTRAINT tasks_task_category_check
            CHECK (task_category IN ('mission', 'bounty', 'task'));
        RAISE NOTICE 'Updated task_category constraint to include mission';
    END IF;

    -- Add mission_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_type') THEN
        ALTER TABLE public.tasks ADD COLUMN mission_type TEXT CHECK (mission_type IN ('skill', 'collaboration', 'achievement', 'exploration', 'social'));
        RAISE NOTICE 'Added mission_type column to tasks table';
    ELSE
        RAISE NOTICE 'mission_type column already exists in tasks table';
    END IF;

    -- Add mission_requirements column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_requirements') THEN
        ALTER TABLE public.tasks ADD COLUMN mission_requirements JSONB;
        RAISE NOTICE 'Added mission_requirements column to tasks table';
    ELSE
        RAISE NOTICE 'mission_requirements column already exists in tasks table';
    END IF;

    -- Add mission_rewards column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_rewards') THEN
        ALTER TABLE public.tasks ADD COLUMN mission_rewards JSONB;
        RAISE NOTICE 'Added mission_rewards column to tasks table';
    ELSE
        RAISE NOTICE 'mission_rewards column already exists in tasks table';
    END IF;
END $$;

-- ============================================================================
-- PHASE 2: CREATE NEW TERMINOLOGY TABLES
-- ============================================================================

-- Create studio_invitations table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.studio_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    studio_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    invited_user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    invited_by_user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'member',
    collaboration_type TEXT DEFAULT 'studio_member' CHECK (collaboration_type IN ('studio_member', 'contractor', 'specialist')),
    engagement_duration TEXT DEFAULT 'permanent' CHECK (engagement_duration IN ('permanent', 'project_based', 'one_off')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- Create studio_preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.studio_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    studio_id UUID REFERENCES public.teams(id) ON DELETE CASCADE UNIQUE,
    settings JSONB DEFAULT '{}',
    notification_preferences JSONB DEFAULT '{}',
    collaboration_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_missions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_missions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    mission_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'in_progress', 'completed', 'failed', 'abandoned')),
    progress JSONB DEFAULT '{}',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, mission_id)
);

-- ============================================================================
-- PHASE 3: ADD PEOPLE TYPE SYSTEM
-- ============================================================================

-- Add people type system to team_members table
DO $$
BEGIN
    -- Add collaboration_type column for the new people type system
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'collaboration_type') THEN
        ALTER TABLE public.team_members ADD COLUMN collaboration_type TEXT DEFAULT 'studio_member'
            CHECK (collaboration_type IN ('studio_member', 'contractor', 'specialist'));
        RAISE NOTICE 'Added collaboration_type column to team_members table';
    ELSE
        RAISE NOTICE 'collaboration_type column already exists in team_members table';
    END IF;

    -- Add engagement_duration column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'engagement_duration') THEN
        ALTER TABLE public.team_members ADD COLUMN engagement_duration TEXT DEFAULT 'permanent'
            CHECK (engagement_duration IN ('permanent', 'project_based', 'one_off'));
        RAISE NOTICE 'Added engagement_duration column to team_members table';
    ELSE
        RAISE NOTICE 'engagement_duration column already exists in team_members table';
    END IF;

    -- Add specialization column for specialists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'specialization') THEN
        ALTER TABLE public.team_members ADD COLUMN specialization TEXT[];
        RAISE NOTICE 'Added specialization column to team_members table';
    ELSE
        RAISE NOTICE 'specialization column already exists in team_members table';
    END IF;
END $$;

-- ============================================================================
-- PHASE 4: CREATE INDEXES FOR NEW TERMINOLOGY
-- ============================================================================

-- Create indexes for new terminology columns
CREATE INDEX IF NOT EXISTS idx_teams_studio_type ON public.teams(studio_type) WHERE studio_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_projects_studio_id ON public.projects(studio_id) WHERE studio_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_projects_project_type ON public.projects(project_type) WHERE project_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_mission_type ON public.tasks(mission_type) WHERE mission_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_task_category ON public.tasks(task_category) WHERE task_category IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_team_members_collaboration_type ON public.team_members(collaboration_type) WHERE collaboration_type IS NOT NULL;

-- Create indexes for new tables
CREATE INDEX IF NOT EXISTS idx_studio_invitations_studio_id ON public.studio_invitations(studio_id);
CREATE INDEX IF NOT EXISTS idx_studio_invitations_invited_user_id ON public.studio_invitations(invited_user_id);
CREATE INDEX IF NOT EXISTS idx_studio_invitations_status ON public.studio_invitations(status);
CREATE INDEX IF NOT EXISTS idx_studio_preferences_studio_id ON public.studio_preferences(studio_id);
CREATE INDEX IF NOT EXISTS idx_user_missions_user_id ON public.user_missions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_missions_mission_id ON public.user_missions(mission_id);
CREATE INDEX IF NOT EXISTS idx_user_missions_status ON public.user_missions(status);

-- ============================================================================
-- PHASE 5: UPDATE COMMENTS AND DOCUMENTATION
-- ============================================================================

-- Update table comments
COMMENT ON TABLE public.studio_invitations IS 'Invitations to join studios with role-based access and collaboration types';
COMMENT ON TABLE public.studio_preferences IS 'Studio-specific configuration and preferences';
COMMENT ON TABLE public.user_missions IS 'User mission progress tracking for gamified collaboration';

-- Update column comments
COMMENT ON COLUMN public.teams.studio_type IS 'Type of studio: emerging (new/growing), established (mature), solo (individual)';
COMMENT ON COLUMN public.projects.project_type IS 'Type of project: software, game, film, music, art, business, research, other';
COMMENT ON COLUMN public.projects.studio_id IS 'Reference to the studio that owns this project';
COMMENT ON COLUMN public.tasks.task_category IS 'Category: mission (gamified internal), bounty (public reward), task (simple work item)';
COMMENT ON COLUMN public.tasks.mission_type IS 'Type of mission: skill, collaboration, achievement, exploration, social';
COMMENT ON COLUMN public.tasks.mission_requirements IS 'JSON object defining mission completion requirements and criteria';
COMMENT ON COLUMN public.tasks.mission_rewards IS 'JSON object defining mission completion rewards and benefits';
COMMENT ON COLUMN public.team_members.collaboration_type IS 'Type of collaboration: studio_member (permanent), contractor (project-based), specialist (one-off)';
COMMENT ON COLUMN public.team_members.engagement_duration IS 'Duration of engagement: permanent, project_based, one_off';
COMMENT ON COLUMN public.team_members.specialization IS 'Array of specializations for specialists (skills, expertise areas)';

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 Terminology Update Migration Complete!';
    RAISE NOTICE '📋 Summary of changes:';
    RAISE NOTICE '   • Added studio_type column to teams table';
    RAISE NOTICE '   • Added project_type and studio_id columns to projects table';
    RAISE NOTICE '   • Added mission columns to tasks table (task_category, mission_type, mission_requirements, mission_rewards)';
    RAISE NOTICE '   • Added people type system to team_members table (collaboration_type, engagement_duration, specialization)';
    RAISE NOTICE '   • Created studio_invitations, studio_preferences, and user_missions tables';
    RAISE NOTICE '   • Created indexes for all new columns and tables';
    RAISE NOTICE '   • Updated table and column comments';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 New terminology is ready to use:';
    RAISE NOTICE '   • Alliance → Studio';
    RAISE NOTICE '   • Venture → Project';
    RAISE NOTICE '   • Quest → Mission';
    RAISE NOTICE '   • People Types: Studio Members, Contractors, Specialists';
END $$;
