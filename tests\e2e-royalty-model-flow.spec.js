import { test, expect } from '@playwright/test';

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PROJECT_OWNER = {
  email: '<EMAIL>',
  password: 'ProjectOwner123!'
};

test.describe('Royalty Model - Complete User Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as project owner
    await page.goto('/login');
    await page.fill('input[type="email"]', PROJECT_OWNER.email);
    await page.fill('input[type="password"]', PROJECT_OWNER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
  });

  test('Complete Royalty Setup and Distribution Flow', async ({ page }) => {
    // Step 1: Navigate to Earn page
    await page.goto('/earn');
    await page.waitForLoadState('networkidle');
    
    // Verify earn page loads
    await expect(page.locator('h1:has-text("Earn")')).toBeVisible();
    
    // Step 2: Set up bank account for revenue monitoring
    const addPaymentButton = page.locator('button:has-text("+ Add Payment Method")');
    if (await addPaymentButton.isVisible()) {
      await addPaymentButton.click();
      await page.waitForTimeout(1000);
      
      // Check if Teller integration modal opens
      const tellerModal = page.locator('[role="dialog"]:has-text("Link Bank Account")');
      if (await tellerModal.isVisible()) {
        // Test bank selection (mock flow)
        const bankSelect = page.locator('select[label*="Bank"]');
        if (await bankSelect.isVisible()) {
          await bankSelect.selectOption({ index: 1 });
        }
        
        // Note: In real flow, this would open Teller OAuth
        // For testing, we'll close the modal
        await page.click('button:has-text("Cancel")');
      }
    }

    // Step 3: Configure revenue distribution settings
    const configureButton = page.locator('button:has-text("💰 Configure Distribution")');
    if (await configureButton.isVisible()) {
      await configureButton.click();
      await page.waitForTimeout(1000);
      
      // Check if royalty calculator opens
      const calculatorModal = page.locator('[role="dialog"]:has-text("Revenue Distribution")');
      if (await calculatorModal.isVisible()) {
        // Test distribution settings
        const distributionType = page.locator('select[label*="Distribution Type"]');
        if (await distributionType.isVisible()) {
          await distributionType.selectOption('manual');
        }
        
        // Test contributor management
        const addContributorButton = page.locator('button:has-text("Add Contributor")');
        if (await addContributorButton.isVisible()) {
          await addContributorButton.click();
          await page.waitForTimeout(500);
          
          // Fill contributor details
          await page.fill('input[label*="Email"]', TEST_USER.email);
          await page.fill('input[label*="Percentage"]', '25');
          await page.fill('textarea[label*="Role"]', 'Frontend Developer');
          
          await page.click('button:has-text("Add")');
          await page.waitForTimeout(1000);
        }
        
        // Test royalty percentage settings
        const royaltySlider = page.locator('input[type="range"]');
        if (await royaltySlider.isVisible()) {
          await royaltySlider.fill('15'); // 15% royalty rate
        }
        
        // Save configuration
        await page.click('button:has-text("Save Configuration")');
        await page.waitForTimeout(2000);
        
        // Verify success message
        const successMessage = page.locator('text=Configuration saved');
        if (await successMessage.isVisible()) {
          console.log('Revenue distribution configured successfully');
        }
        
        await page.click('button:has-text("Close")');
      }
    }

    // Step 4: Simulate revenue detection
    // In real flow, this would be triggered by bank webhooks
    // For testing, we'll check if revenue entries are displayed
    const revenueCards = page.locator('[data-testid="revenue-card"]');
    const revenueCount = await revenueCards.count();
    
    if (revenueCount > 0) {
      console.log(`Found ${revenueCount} revenue entries`);
      
      // Test revenue details view
      const viewDetailsButton = revenueCards.first().locator('button:has-text("View Details")');
      if (await viewDetailsButton.isVisible()) {
        await viewDetailsButton.click();
        await page.waitForTimeout(1000);
        
        // Check revenue details modal
        const detailsModal = page.locator('[role="dialog"]:has-text("Revenue Details")');
        if (await detailsModal.isVisible()) {
          // Verify revenue information
          await expect(page.locator('text=Amount')).toBeVisible();
          await expect(page.locator('text=Source')).toBeVisible();
          await expect(page.locator('text=Contributors')).toBeVisible();
          
          await page.click('button:has-text("Close")');
        }
      }
    }

    // Step 5: Test manual revenue distribution
    const distributeButton = page.locator('button:has-text("Start Distribution")');
    if (await distributeButton.isVisible()) {
      await distributeButton.click();
      await page.waitForTimeout(1000);
      
      // Check distribution confirmation modal
      const confirmModal = page.locator('[role="dialog"]:has-text("Confirm Distribution")');
      if (await confirmModal.isVisible()) {
        // Review distribution details
        await expect(page.locator('text=Total Amount')).toBeVisible();
        await expect(page.locator('text=Contributors')).toBeVisible();
        await expect(page.locator('text=Distribution Method')).toBeVisible();
        
        // For testing, cancel instead of actually distributing
        await page.click('button:has-text("Cancel")');
      }
    }

    // Step 6: Check escrow management
    const escrowSection = page.locator('[data-testid="escrow-section"]');
    if (await escrowSection.isVisible()) {
      const releaseButton = page.locator('button:has-text("Release Funds")');
      if (await releaseButton.isVisible()) {
        await releaseButton.click();
        await page.waitForTimeout(1000);
        
        // Check escrow release modal
        const releaseModal = page.locator('[role="dialog"]:has-text("Release Escrow")');
        if (await releaseModal.isVisible()) {
          // Verify escrow details
          await expect(page.locator('text=Escrow Amount')).toBeVisible();
          await expect(page.locator('text=Release Conditions')).toBeVisible();
          
          await page.click('button:has-text("Cancel")');
        }
      }
    }

    // Step 7: View transaction history
    const historyTab = page.locator('[role="tab"]:has-text("Transaction History")');
    if (await historyTab.isVisible()) {
      await historyTab.click();
      await page.waitForTimeout(1000);
      
      // Verify transaction history elements
      await expect(page.locator('text=Recent Transactions')).toBeVisible();
      
      const transactionRows = page.locator('[data-testid="transaction-row"]');
      const transactionCount = await transactionRows.count();
      
      if (transactionCount > 0) {
        console.log(`Found ${transactionCount} transaction records`);
        
        // Test transaction details
        const firstTransaction = transactionRows.first();
        await expect(firstTransaction.locator('text=Amount')).toBeVisible();
        await expect(firstTransaction.locator('text=Status')).toBeVisible();
        await expect(firstTransaction.locator('text=Date')).toBeVisible();
      }
    }

    console.log('✅ Complete royalty model flow test passed');
  });

  test('Revenue Monitoring and Alert System', async ({ page }) => {
    await page.goto('/earn');
    await page.waitForLoadState('networkidle');
    
    // Test revenue monitoring dashboard
    const monitoringSection = page.locator('[data-testid="revenue-monitoring"]');
    if (await monitoringSection.isVisible()) {
      // Check monitoring status
      await expect(page.locator('text=Revenue Monitoring')).toBeVisible();
      
      // Test alert settings
      const alertButton = page.locator('button:has-text("Alert Settings")');
      if (await alertButton.isVisible()) {
        await alertButton.click();
        await page.waitForTimeout(1000);
        
        const alertModal = page.locator('[role="dialog"]:has-text("Revenue Alerts")');
        if (await alertModal.isVisible()) {
          // Configure alert thresholds
          const thresholdInput = page.locator('input[label*="Threshold"]');
          if (await thresholdInput.isVisible()) {
            await thresholdInput.fill('1000');
          }
          
          // Enable email notifications
          const emailCheckbox = page.locator('input[type="checkbox"]:near(text="Email notifications")');
          if (await emailCheckbox.isVisible()) {
            await emailCheckbox.check();
          }
          
          await page.click('button:has-text("Save Settings")');
          await page.waitForTimeout(1000);
          
          await page.click('button:has-text("Close")');
        }
      }
    }
    
    console.log('✅ Revenue monitoring and alert system test passed');
  });

  test('Multi-Project Royalty Management', async ({ page }) => {
    await page.goto('/earn');
    await page.waitForLoadState('networkidle');
    
    // Test project selector for multi-project royalties
    const projectSelector = page.locator('select[label*="Project"]');
    if (await projectSelector.isVisible()) {
      // Switch between projects
      const projectOptions = await projectSelector.locator('option').count();
      if (projectOptions > 1) {
        await projectSelector.selectOption({ index: 1 });
        await page.waitForTimeout(1000);
        
        // Verify project-specific revenue data loads
        const projectRevenue = page.locator('[data-testid="project-revenue"]');
        if (await projectRevenue.isVisible()) {
          console.log('Project-specific revenue data loaded');
        }
      }
    }
    
    // Test cross-project revenue comparison
    const compareButton = page.locator('button:has-text("Compare Projects")');
    if (await compareButton.isVisible()) {
      await compareButton.click();
      await page.waitForTimeout(1000);
      
      const comparisonModal = page.locator('[role="dialog"]:has-text("Project Comparison")');
      if (await comparisonModal.isVisible()) {
        // Verify comparison charts and data
        await expect(page.locator('text=Revenue Comparison')).toBeVisible();
        await expect(page.locator('text=Performance Metrics')).toBeVisible();
        
        await page.click('button:has-text("Close")');
      }
    }
    
    console.log('✅ Multi-project royalty management test passed');
  });
});

test.describe('Royalty Model - Contributor Perspective', () => {
  test.beforeEach(async ({ page }) => {
    // Login as contributor
    await page.goto('/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
  });

  test('Contributor Earnings Dashboard', async ({ page }) => {
    await page.goto('/earn');
    await page.waitForLoadState('networkidle');
    
    // Verify contributor earnings view
    await expect(page.locator('h1:has-text("Earn")')).toBeVisible();
    
    // Check earnings summary cards
    const earningsCards = page.locator('[data-testid="earnings-card"]');
    const cardCount = await earningsCards.count();
    
    if (cardCount > 0) {
      // Verify earnings information
      await expect(page.locator('text=Total Earnings')).toBeVisible();
      await expect(page.locator('text=Pending Payments')).toBeVisible();
      await expect(page.locator('text=This Month')).toBeVisible();
      
      console.log(`Found ${cardCount} earnings cards`);
    }
    
    // Test earnings breakdown
    const breakdownButton = page.locator('button:has-text("View Breakdown")');
    if (await breakdownButton.isVisible()) {
      await breakdownButton.click();
      await page.waitForTimeout(1000);
      
      const breakdownModal = page.locator('[role="dialog"]:has-text("Earnings Breakdown")');
      if (await breakdownModal.isVisible()) {
        // Verify breakdown details
        await expect(page.locator('text=Project Contributions')).toBeVisible();
        await expect(page.locator('text=Payment History')).toBeVisible();
        
        await page.click('button:has-text("Close")');
      }
    }
    
    // Test payment method setup
    const paymentMethodButton = page.locator('button:has-text("Payment Methods")');
    if (await paymentMethodButton.isVisible()) {
      await paymentMethodButton.click();
      await page.waitForTimeout(1000);
      
      const paymentModal = page.locator('[role="dialog"]:has-text("Payment Methods")');
      if (await paymentModal.isVisible()) {
        // Test adding payment method
        const addMethodButton = page.locator('button:has-text("Add Method")');
        if (await addMethodButton.isVisible()) {
          await addMethodButton.click();
          await page.waitForTimeout(500);
          
          // For testing, just verify the form opens
          const methodForm = page.locator('form');
          if (await methodForm.isVisible()) {
            await page.click('button:has-text("Cancel")');
          }
        }
        
        await page.click('button:has-text("Close")');
      }
    }
    
    console.log('✅ Contributor earnings dashboard test passed');
  });

  test('Payment Request and Withdrawal Flow', async ({ page }) => {
    await page.goto('/earn');
    await page.waitForLoadState('networkidle');
    
    // Test payment request functionality
    const requestPaymentButton = page.locator('button:has-text("Request Payment")');
    if (await requestPaymentButton.isVisible()) {
      await requestPaymentButton.click();
      await page.waitForTimeout(1000);
      
      const requestModal = page.locator('[role="dialog"]:has-text("Request Payment")');
      if (await requestModal.isVisible()) {
        // Fill payment request form
        const amountInput = page.locator('input[label*="Amount"]');
        if (await amountInput.isVisible()) {
          await amountInput.fill('500');
        }
        
        const reasonTextarea = page.locator('textarea[label*="Reason"]');
        if (await reasonTextarea.isVisible()) {
          await reasonTextarea.fill('Payment request for completed project milestones');
        }
        
        // For testing, cancel instead of submitting
        await page.click('button:has-text("Cancel")');
      }
    }
    
    // Test withdrawal history
    const historyTab = page.locator('[role="tab"]:has-text("Withdrawal History")');
    if (await historyTab.isVisible()) {
      await historyTab.click();
      await page.waitForTimeout(1000);
      
      // Verify withdrawal history elements
      const withdrawalRows = page.locator('[data-testid="withdrawal-row"]');
      const withdrawalCount = await withdrawalRows.count();
      
      if (withdrawalCount > 0) {
        console.log(`Found ${withdrawalCount} withdrawal records`);
        
        // Test withdrawal status tracking
        const statusBadges = page.locator('[data-testid="status-badge"]');
        const badgeCount = await statusBadges.count();
        expect(badgeCount).toBeGreaterThan(0);
      }
    }
    
    console.log('✅ Payment request and withdrawal flow test passed');
  });
});
