// Revenue Flow Testing Utility
// Creates mock data to test the complete revenue distribution flow

import { supabase } from '../supabase/supabase.utils.js';

export class RevenueFlowTester {
  constructor() {
    this.testData = {
      project: null,
      contributors: [],
      bankAccounts: [],
      revenueEntry: null,
      distribution: null
    };
  }

  /**
   * Create a complete test scenario for revenue distribution
   */
  async createTestScenario(userId) {
    try {
      console.log('🧪 Creating revenue flow test scenario...');

      // Step 1: Create test project
      await this.createTestProject(userId);
      
      // Step 2: Create test contributors
      await this.createTestContributors();
      
      // Step 3: Create mock bank accounts
      await this.createMockBankAccounts();
      
      // Step 4: Create mock revenue entry
      await this.createMockRevenueEntry();
      
      // Step 5: Create test distribution
      await this.createTestDistribution();

      console.log('✅ Test scenario created successfully!');
      return this.testData;

    } catch (error) {
      console.error('❌ Failed to create test scenario:', error);
      throw error;
    }
  }

  /**
   * Create a test project
   */
  async createTestProject(userId) {
    const projectData = {
      name: 'Revenue Test Project',
      title: 'Testing Revenue Distribution',
      description: 'A test project for validating revenue distribution flow',
      created_by: userId,
      status: 'active',
      revenue_automation_enabled: true,
      project_type: 'venture'
    };

    const { data: project, error } = await supabase
      .from('projects')
      .insert([projectData])
      .select()
      .single();

    if (error) throw error;

    this.testData.project = project;
    console.log('📁 Test project created:', project.name);
  }

  /**
   * Create test contributors
   */
  async createTestContributors() {
    // Note: In real testing, you'd use actual user accounts
    // For now, we'll create mock contributor data
    this.testData.contributors = [
      {
        id: 'contributor-1',
        name: 'Alice Developer',
        email: '<EMAIL>',
        role: 'developer',
        contribution_percentage: 40
      },
      {
        id: 'contributor-2', 
        name: 'Bob Designer',
        email: '<EMAIL>',
        role: 'designer',
        contribution_percentage: 30
      },
      {
        id: 'contributor-3',
        name: 'Carol Manager',
        email: '<EMAIL>', 
        role: 'manager',
        contribution_percentage: 30
      }
    ];

    console.log('👥 Test contributors created:', this.testData.contributors.length);
  }

  /**
   * Create mock bank accounts (simulates Teller accounts)
   */
  async createMockBankAccounts() {
    const projectAccount = {
      user_id: this.testData.project.created_by,
      project_id: this.testData.project.id,
      teller_account_id: 'mock_project_account_123',
      teller_item_id: 'mock_item_456',
      account_name: 'Test Project Business Checking',
      account_type: 'checking',
      institution_name: 'Mock Bank',
      is_active: true,
      supports_ach: true
    };

    // Insert project account
    const { data: projAccount, error: projError } = await supabase
      .from('teller_accounts')
      .insert([projectAccount])
      .select()
      .single();

    if (projError) throw projError;

    this.testData.bankAccounts.push(projAccount);

    // Create personal accounts for each contributor
    for (const contributor of this.testData.contributors) {
      const personalAccount = {
        user_id: contributor.id,
        project_id: null, // Personal account
        teller_account_id: `mock_personal_${contributor.id}`,
        teller_item_id: `mock_item_${contributor.id}`,
        account_name: `${contributor.name} Personal Checking`,
        account_type: 'checking',
        institution_name: 'Mock Bank',
        is_active: true,
        supports_ach: true
      };

      // Note: In real testing, you'd need actual user IDs
      // For mock testing, we'll skip the database insert
      this.testData.bankAccounts.push(personalAccount);
    }

    console.log('🏦 Mock bank accounts created:', this.testData.bankAccounts.length);
  }

  /**
   * Create mock revenue entry
   */
  async createMockRevenueEntry() {
    const revenueData = {
      project_id: this.testData.project.id,
      amount: 10000.00, // $10,000 test revenue
      currency: 'USD',
      source: 'test_revenue',
      description: 'Mock revenue for testing distribution',
      status: 'pending',
      detected_at: new Date().toISOString()
    };

    // Note: You'd insert into your revenue_tracking table
    this.testData.revenueEntry = revenueData;
    console.log('💰 Mock revenue entry created: $10,000');
  }

  /**
   * Create test distribution
   */
  async createTestDistribution() {
    const distributionData = {
      project_id: this.testData.project.id,
      total_amount: 10000.00,
      distribution_model: 'percentage',
      status: 'pending',
      contributors: this.testData.contributors.map(c => ({
        ...c,
        amount: 10000 * (c.contribution_percentage / 100)
      }))
    };

    this.testData.distribution = distributionData;
    console.log('📊 Test distribution created');
  }

  /**
   * Simulate the complete revenue flow
   */
  async simulateRevenueFlow() {
    console.log('🎬 Simulating complete revenue flow...');
    
    const steps = [
      '1. 💳 Revenue detected in project account: $10,000',
      '2. 🔔 System creates revenue entry for approval',
      '3. 👤 Project owner reviews and approves distribution',
      '4. ⚖️ System calculates individual amounts:',
      `   • Alice (40%): $${this.testData.distribution.contributors[0].amount}`,
      `   • Bob (30%): $${this.testData.distribution.contributors[1].amount}`,
      `   • Carol (30%): $${this.testData.distribution.contributors[2].amount}`,
      '5. 💸 System executes bank transfers:',
      '   • Project Account → Alice Personal Account',
      '   • Project Account → Bob Personal Account', 
      '   • Project Account → Carol Personal Account',
      '6. ✅ Contributors receive money in personal accounts'
    ];

    steps.forEach(step => console.log(step));
    
    return {
      success: true,
      message: 'Revenue flow simulation completed',
      testData: this.testData
    };
  }

  /**
   * Clean up test data
   */
  async cleanup() {
    try {
      console.log('🧹 Cleaning up test data...');
      
      // Delete test project (cascades to related data)
      if (this.testData.project) {
        await supabase
          .from('projects')
          .delete()
          .eq('id', this.testData.project.id);
      }

      // Delete mock bank accounts
      for (const account of this.testData.bankAccounts) {
        if (account.id) {
          await supabase
            .from('teller_accounts')
            .delete()
            .eq('id', account.id);
        }
      }

      console.log('✅ Test cleanup completed');
    } catch (error) {
      console.error('❌ Cleanup failed:', error);
    }
  }
}

// Usage example:
export const runRevenueFlowTest = async (userId) => {
  const tester = new RevenueFlowTester();
  
  try {
    // Create test scenario
    const testData = await tester.createTestScenario(userId);
    
    // Simulate the flow
    const result = await tester.simulateRevenueFlow();
    
    console.log('🎉 Revenue flow test completed successfully!');
    return { testData, result };
    
  } catch (error) {
    console.error('❌ Revenue flow test failed:', error);
    throw error;
  } finally {
    // Clean up
    await tester.cleanup();
  }
};
