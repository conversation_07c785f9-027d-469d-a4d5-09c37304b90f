// Revenue Flow Test Page
// Provides UI for testing the complete revenue distribution flow

import React, { useState, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Divider, Code } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { RevenueFlowTester, runRevenueFlowTest } from '../../utils/testing/revenueFlowTester.js';
import { toast } from 'react-hot-toast';
import { 
  TestTube, 
  Play, 
  CheckCircle, 
  XCircle, 
  DollarSign,
  Users,
  Building2,
  ArrowRight,
  Trash2
} from 'lucide-react';

const RevenueFlowTestPage = () => {
  const { currentUser } = useContext(UserContext);
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState(null);
  const [testData, setTestData] = useState(null);

  const runSimulationTest = async () => {
    if (!currentUser) {
      toast.error('Please log in to run tests');
      return;
    }

    setIsRunning(true);
    try {
      console.log('🧪 Starting revenue flow simulation test...');
      
      const result = await runRevenueFlowTest(currentUser.id);
      
      setTestResults(result.result);
      setTestData(result.testData);
      
      toast.success('Revenue flow test completed successfully!');
      
    } catch (error) {
      console.error('Test failed:', error);
      toast.error(`Test failed: ${error.message}`);
      setTestResults({ success: false, error: error.message });
    } finally {
      setIsRunning(false);
    }
  };

  const runRealFlowTest = () => {
    toast.info('Real flow testing requires actual bank accounts. See instructions below.');
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-2">
          <TestTube className="text-blue-500" size={32} />
          Revenue Flow Testing
        </h1>
        <p className="text-gray-600">
          Test the complete revenue distribution system from detection to payout
        </p>
      </div>

      {/* Test Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Simulation Test */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Play className="text-green-500" size={20} />
              <h3 className="text-lg font-semibold">Simulation Test</h3>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <p className="text-sm text-gray-600">
              Safe testing with mock data. No real money or bank accounts involved.
            </p>
            
            <div className="space-y-2">
              <h4 className="font-medium">What it tests:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Project and contributor setup</li>
                <li>• Revenue detection simulation</li>
                <li>• Distribution calculation</li>
                <li>• Transfer logic validation</li>
              </ul>
            </div>

            <Button
              color="primary"
              size="lg"
              className="w-full"
              startContent={<Play size={16} />}
              onPress={runSimulationTest}
              isLoading={isRunning}
            >
              Run Simulation Test
            </Button>
          </CardBody>
        </Card>

        {/* Real Flow Test */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Building2 className="text-orange-500" size={20} />
              <h3 className="text-lg font-semibold">Real Flow Test</h3>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <p className="text-sm text-gray-600">
              Test with actual bank accounts and small amounts ($1-5).
            </p>
            
            <div className="space-y-2">
              <h4 className="font-medium">Requirements:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Project bank account connected</li>
                <li>• Contributors with personal accounts</li>
                <li>• Small test amounts only</li>
              </ul>
            </div>

            <Button
              color="warning"
              size="lg"
              className="w-full"
              startContent={<Building2 size={16} />}
              onPress={runRealFlowTest}
            >
              See Real Test Instructions
            </Button>
          </CardBody>
        </Card>
      </div>

      {/* Test Results */}
      {testResults && (
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              {testResults.success ? (
                <CheckCircle className="text-green-500" size={20} />
              ) : (
                <XCircle className="text-red-500" size={20} />
              )}
              <h3 className="text-lg font-semibold">Test Results</h3>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            {testResults.success ? (
              <div className="space-y-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-green-800 font-medium">✅ Test Passed!</p>
                  <p className="text-green-600 text-sm">{testResults.message}</p>
                </div>

                {testData && (
                  <div className="space-y-4">
                    <h4 className="font-medium">Test Scenario Created:</h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <DollarSign className="text-blue-500" size={16} />
                          <span className="font-medium text-sm">Project</span>
                        </div>
                        <p className="text-xs text-gray-600">{testData.project?.name}</p>
                        <p className="text-xs text-gray-600">Revenue: $10,000</p>
                      </div>

                      <div className="bg-purple-50 p-3 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Users className="text-purple-500" size={16} />
                          <span className="font-medium text-sm">Contributors</span>
                        </div>
                        <p className="text-xs text-gray-600">{testData.contributors?.length} people</p>
                        <p className="text-xs text-gray-600">Alice, Bob, Carol</p>
                      </div>

                      <div className="bg-orange-50 p-3 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Building2 className="text-orange-500" size={16} />
                          <span className="font-medium text-sm">Bank Accounts</span>
                        </div>
                        <p className="text-xs text-gray-600">{testData.bankAccounts?.length} accounts</p>
                        <p className="text-xs text-gray-600">1 project + 3 personal</p>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Distribution Breakdown:</h5>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Alice (40%)</span>
                          <span>$4,000</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Bob (30%)</span>
                          <span>$3,000</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Carol (30%)</span>
                          <span>$3,000</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-red-50 p-4 rounded-lg">
                <p className="text-red-800 font-medium">❌ Test Failed</p>
                <p className="text-red-600 text-sm">{testResults.error}</p>
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {/* Real Testing Instructions */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Real Flow Testing Instructions</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="space-y-4">
            <div className="bg-yellow-50 p-4 rounded-lg">
              <p className="text-yellow-800 font-medium">⚠️ Use Small Amounts Only</p>
              <p className="text-yellow-700 text-sm">Test with $1-5 to avoid financial risk</p>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium">Step-by-Step Real Testing:</h4>
              
              <div className="space-y-2">
                <div className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">1</span>
                  <div>
                    <p className="font-medium text-sm">Setup Project Bank Account</p>
                    <p className="text-xs text-gray-600">Go to project → Revenue Settings → Connect Project Bank Account</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">2</span>
                  <div>
                    <p className="font-medium text-sm">Setup Personal Accounts</p>
                    <p className="text-xs text-gray-600">Each contributor: Earn page → Connect Personal Bank Account</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">3</span>
                  <div>
                    <p className="font-medium text-sm">Send Test Revenue</p>
                    <p className="text-xs text-gray-600">Transfer $5 to the project bank account</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">4</span>
                  <div>
                    <p className="font-medium text-sm">Check Revenue Detection</p>
                    <p className="text-xs text-gray-600">Revenue page should show detected revenue entry</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">5</span>
                  <div>
                    <p className="font-medium text-sm">Process Distribution</p>
                    <p className="text-xs text-gray-600">Create and execute distribution to contributors</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">6</span>
                  <div>
                    <p className="font-medium text-sm">Verify Transfers</p>
                    <p className="text-xs text-gray-600">Check contributors' personal accounts for received funds</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default RevenueFlowTestPage;
