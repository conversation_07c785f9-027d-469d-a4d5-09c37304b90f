-- Fix Invitation System Database Structure
-- This script safely adds missing columns and tables for the invitation system

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- FIX USER_ALLIES TABLE
-- ============================================================================

-- Add missing columns to user_allies table if they don't exist
DO $$ 
BEGIN
    -- Add connection_type column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'connection_type') THEN
        ALTER TABLE public.user_allies ADD COLUMN connection_type VARCHAR(20) DEFAULT 'friend';
        RAISE NOTICE 'Added connection_type column to user_allies';
    END IF;
    
    -- Add requested_at column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'requested_at') THEN
        ALTER TABLE public.user_allies ADD COLUMN requested_at TIMESTAMP WITH TIME ZONE DEFAULT now();
        RAISE NOTICE 'Added requested_at column to user_allies';
    END IF;
    
    -- Add accepted_at column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'accepted_at') THEN
        ALTER TABLE public.user_allies ADD COLUMN accepted_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added accepted_at column to user_allies';
    END IF;
    
    -- Add declined_at column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'declined_at') THEN
        ALTER TABLE public.user_allies ADD COLUMN declined_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added declined_at column to user_allies';
    END IF;
    
    -- Add blocked_at column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'blocked_at') THEN
        ALTER TABLE public.user_allies ADD COLUMN blocked_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added blocked_at column to user_allies';
    END IF;
    
    -- Add created_by column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'created_by') THEN
        ALTER TABLE public.user_allies ADD COLUMN created_by UUID REFERENCES auth.users(id);
        RAISE NOTICE 'Added created_by column to user_allies';
    END IF;
    
    -- Add request_message column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'request_message') THEN
        ALTER TABLE public.user_allies ADD COLUMN request_message TEXT;
        RAISE NOTICE 'Added request_message column to user_allies';
    END IF;
    
    -- Add notes column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'notes') THEN
        ALTER TABLE public.user_allies ADD COLUMN notes TEXT;
        RAISE NOTICE 'Added notes column to user_allies';
    END IF;
END $$;

-- ============================================================================
-- CREATE FRIEND_REQUESTS TABLE IF MISSING
-- ============================================================================

-- Create friend_requests table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.friend_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    recipient_email VARCHAR(255), -- For inviting users not yet on platform
    message TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired', 'cancelled')),
    request_type VARCHAR(20) DEFAULT 'friend' CHECK (request_type IN ('friend', 'colleague', 'collaborator')),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (now() + INTERVAL '30 days'),
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    responded_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Either recipient_id or recipient_email must be provided
    CONSTRAINT friend_requests_recipient_check CHECK (
        (recipient_id IS NOT NULL) OR (recipient_email IS NOT NULL)
    )
);

-- ============================================================================
-- ADD INVITATION TOKEN COLUMNS
-- ============================================================================

-- Add invitation token columns to project_contributors table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_contributors') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'project_contributors' AND column_name = 'invitation_token') THEN
            ALTER TABLE public.project_contributors ADD COLUMN invitation_token TEXT;
            RAISE NOTICE 'Added invitation_token column to project_contributors';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'project_contributors' AND column_name = 'token_generated_at') THEN
            ALTER TABLE public.project_contributors ADD COLUMN token_generated_at TIMESTAMPTZ;
            RAISE NOTICE 'Added token_generated_at column to project_contributors';
        END IF;
    END IF;
END $$;

-- Add invitation token columns to friend_requests table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'friend_requests') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'friend_requests' AND column_name = 'invitation_token') THEN
            ALTER TABLE public.friend_requests ADD COLUMN invitation_token TEXT;
            RAISE NOTICE 'Added invitation_token column to friend_requests';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'token_generated_at' AND column_name = 'token_generated_at') THEN
            ALTER TABLE public.friend_requests ADD COLUMN token_generated_at TIMESTAMPTZ;
            RAISE NOTICE 'Added token_generated_at column to friend_requests';
        END IF;
    END IF;
END $$;

-- ============================================================================
-- CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Create indexes for faster token lookups
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_contributors') THEN
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_project_contributors_invitation_token') THEN
            CREATE INDEX idx_project_contributors_invitation_token 
            ON public.project_contributors(invitation_token) 
            WHERE invitation_token IS NOT NULL;
            RAISE NOTICE 'Created index on project_contributors.invitation_token';
        END IF;
    END IF;
END $$;

DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'friend_requests') THEN
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_friend_requests_invitation_token') THEN
            CREATE INDEX idx_friend_requests_invitation_token 
            ON public.friend_requests(invitation_token) 
            WHERE invitation_token IS NOT NULL;
            RAISE NOTICE 'Created index on friend_requests.invitation_token';
        END IF;
    END IF;
END $$;

-- ============================================================================
-- ADD CONSTRAINTS AND CHECKS
-- ============================================================================

-- Add connection_type check constraint if missing
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'connection_type') THEN
        -- Drop existing constraint if it exists
        IF EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'user_allies_connection_type_check') THEN
            ALTER TABLE public.user_allies DROP CONSTRAINT user_allies_connection_type_check;
        END IF;
        
        -- Add new constraint
        ALTER TABLE public.user_allies ADD CONSTRAINT user_allies_connection_type_check 
        CHECK (connection_type IN ('friend', 'colleague', 'collaborator', 'mentor', 'mentee'));
        RAISE NOTICE 'Added connection_type check constraint to user_allies';
    END IF;
END $$;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verify the invitation system tables and columns
SELECT 
    'Invitation System Setup Complete' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name IN ('user_allies', 'friend_requests', 'project_contributors')) as tables_ready,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'connection_type') as user_allies_ready,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'friend_requests' AND column_name = 'invitation_token') as friend_requests_ready,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'project_contributors' AND column_name = 'invitation_token') as project_contributors_ready;

RAISE NOTICE 'Invitation system database structure has been updated successfully!';
RAISE NOTICE 'You can now use the email invitation system for non-users.';
