-- Add escrow support to revenue and royalty tables

-- Add escrow fields to revenue_entries table
ALTER TABLE public.revenue_entries
ADD COLUMN IF NOT EXISTS in_escrow BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS escrow_reason TEXT,
ADD COLUMN IF NOT EXISTS escrow_release_condition TEXT,
ADD COLUMN IF NOT EXISTS escrow_release_date DATE,
ADD COLUMN IF NOT EXISTS escrow_notes TEXT;

-- Add escrow fields to royalty_distributions table
ALTER TABLE public.royalty_distributions
ADD COLUMN IF NOT EXISTS from_escrow BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS escrow_release_id UUID REFERENCES public.revenue_entries(id) ON DELETE SET NULL;

-- Create revenue_escrow table to track escrow history
CREATE TABLE IF NOT EXISTS public.revenue_escrow (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    revenue_id UUID NOT NULL REFERENCES public.revenue_entries(id) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    amount DECIMAL(12, 2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    escrow_date DATE NOT NULL,
    release_date DATE,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'released', 'cancelled')),
    reason TEXT NOT NULL,
    release_condition TEXT,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    released_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    notes TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_revenue_escrow_revenue_id ON public.revenue_escrow(revenue_id);
CREATE INDEX IF NOT EXISTS idx_revenue_escrow_project_id ON public.revenue_escrow(project_id);
CREATE INDEX IF NOT EXISTS idx_revenue_escrow_status ON public.revenue_escrow(status);

-- Set up Row Level Security (RLS)
ALTER TABLE public.revenue_escrow ENABLE ROW LEVEL SECURITY;

-- Project members can view escrow records
DROP POLICY IF EXISTS "Project members can view escrow records" ON public.revenue_escrow;
CREATE POLICY "Project members can view escrow records"
ON public.revenue_escrow FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.project_contributors
        WHERE project_contributors.project_id = revenue_escrow.project_id
        AND project_contributors.user_id = auth.uid()

    )
);

-- Project admins can create, update, or delete escrow records
DROP POLICY IF EXISTS "Project admins can manage escrow records" ON public.revenue_escrow;
CREATE POLICY "Project admins can manage escrow records"
ON public.revenue_escrow FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.project_contributors
        WHERE project_contributors.project_id = revenue_escrow.project_id
        AND project_contributors.user_id = auth.uid()


    )
);

-- Create a trigger to update the updated_at column
DROP TRIGGER IF EXISTS update_revenue_escrow_updated_at ON public.revenue_escrow;
CREATE TRIGGER update_revenue_escrow_updated_at
BEFORE UPDATE ON public.revenue_escrow
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to log escrow activities
CREATE OR REPLACE FUNCTION log_escrow_activity()
RETURNS TRIGGER AS $$
DECLARE
    activity_type_val TEXT;
    activity_data_val JSONB;
BEGIN
    IF TG_OP = 'INSERT' THEN
        activity_type_val := 'revenue_escrowed';
        activity_data_val := jsonb_build_object(
            'escrow_id', NEW.id,
            'revenue_id', NEW.revenue_id,
            'amount', NEW.amount,
            'currency', NEW.currency,
            'reason', NEW.reason
        );
    ELSIF TG_OP = 'UPDATE' AND NEW.status = 'released' AND OLD.status = 'active' THEN
        activity_type_val := 'escrow_released';
        activity_data_val := jsonb_build_object(
            'escrow_id', NEW.id,
            'revenue_id', NEW.revenue_id,
            'amount', NEW.amount,
            'currency', NEW.currency,
            'release_date', NEW.release_date
        );
    ELSIF TG_OP = 'UPDATE' AND NEW.status = 'cancelled' AND OLD.status = 'active' THEN
        activity_type_val := 'escrow_cancelled';
        activity_data_val := jsonb_build_object(
            'escrow_id', NEW.id,
            'revenue_id', NEW.revenue_id,
            'amount', NEW.amount,
            'currency', NEW.currency
        );
    END IF;

    -- Log the activity if we have a valid activity type
    IF activity_type_val IS NOT NULL THEN
        INSERT INTO public.project_activities (
            project_id,
            user_id,
            activity_type,
            activity_data
        ) VALUES (
            NEW.project_id,
            COALESCE(NEW.created_by, auth.uid()),
            activity_type_val,
            activity_data_val
        );
    END IF;

    RETURN NULL;
END;
$$ language 'plpgsql';

-- Create triggers to log escrow activities
DROP TRIGGER IF EXISTS log_revenue_escrow_activity ON public.revenue_escrow;
CREATE TRIGGER log_revenue_escrow_activity
AFTER INSERT OR UPDATE ON public.revenue_escrow
FOR EACH ROW
EXECUTE FUNCTION log_escrow_activity();

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.revenue_escrow TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
