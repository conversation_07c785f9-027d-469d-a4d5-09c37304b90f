-- ============================================================================
-- RECOGNITION & ANALYTICS SYSTEM MIGRATION
-- ============================================================================
-- Creates comprehensive recognition, analytics, and endorsement infrastructure
-- for user performance tracking, network analysis, and achievement systems.

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- USER RECOGNITION & ACHIEVEMENTS
-- ============================================================================

-- Create user_achievements table for tracking accomplishments
CREATE TABLE IF NOT EXISTS public.user_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_type VARCHAR(50) NOT NULL CHECK (
        achievement_type IN (
            'top_ally', 'collaboration_master', 'skill_expert', 'project_leader',
            'mentor', 'innovator', 'reliable_contributor', 'network_builder',
            'quality_champion', 'deadline_hero', 'team_player', 'problem_solver'
        )
    ),
    achievement_level INTEGER DEFAULT 1 CHECK (achievement_level BETWEEN 1 AND 5),
    achievement_title VARCHAR(255) NOT NULL,
    achievement_description TEXT,
    
    -- Achievement criteria and metrics
    criteria_met JSONB DEFAULT '{}', -- What criteria were met to earn this
    achievement_score DECIMAL(10,2) DEFAULT 0.0,
    evidence_data JSONB DEFAULT '{}', -- Supporting data for the achievement
    
    -- Recognition details
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    expires_at TIMESTAMP WITH TIME ZONE, -- For time-limited achievements
    is_featured BOOLEAN DEFAULT false, -- Featured on profile
    is_public BOOLEAN DEFAULT true, -- Visible to others
    
    -- Verification
    verified_by UUID REFERENCES auth.users(id),
    verified_at TIMESTAMP WITH TIME ZONE,
    verification_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure unique achievements per user per type/level
    CONSTRAINT user_achievements_unique UNIQUE (user_id, achievement_type, achievement_level)
);

-- Create recognition_rankings table for leaderboards and top performers
CREATE TABLE IF NOT EXISTS public.recognition_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    ranking_category VARCHAR(50) NOT NULL CHECK (
        ranking_category IN (
            'top_allies', 'collaboration_score', 'skill_diversity', 'project_success',
            'mentorship_impact', 'innovation_index', 'reliability_score', 'network_influence'
        )
    ),
    ranking_period VARCHAR(20) DEFAULT 'monthly' CHECK (
        ranking_period IN ('weekly', 'monthly', 'quarterly', 'yearly', 'all_time')
    ),
    
    -- Ranking metrics
    current_rank INTEGER NOT NULL,
    previous_rank INTEGER,
    rank_change INTEGER DEFAULT 0, -- Positive = moved up, negative = moved down
    score DECIMAL(10,4) NOT NULL,
    percentile DECIMAL(5,2), -- What percentile this user is in
    
    -- Period information
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_participants INTEGER DEFAULT 0,
    
    -- Additional context
    ranking_data JSONB DEFAULT '{}', -- Detailed breakdown of how score was calculated
    
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure unique rankings per user per category per period
    CONSTRAINT recognition_rankings_unique UNIQUE (user_id, ranking_category, ranking_period, period_start)
);

-- ============================================================================
-- COLLABORATION METRICS & ANALYTICS
-- ============================================================================

-- Create collaboration_metrics table for tracking collaboration performance
CREATE TABLE IF NOT EXISTS public.collaboration_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Time period
    metric_period VARCHAR(20) DEFAULT 'monthly' CHECK (
        metric_period IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')
    ),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    -- Collaboration metrics
    total_collaborations INTEGER DEFAULT 0,
    successful_collaborations INTEGER DEFAULT 0,
    collaboration_success_rate DECIMAL(5,2) DEFAULT 0.0,
    average_project_rating DECIMAL(3,2) DEFAULT 0.0,
    
    -- Communication metrics
    messages_sent INTEGER DEFAULT 0,
    messages_received INTEGER DEFAULT 0,
    response_time_avg INTEGER DEFAULT 0, -- Average response time in minutes
    files_shared INTEGER DEFAULT 0,
    
    -- Network metrics
    new_connections INTEGER DEFAULT 0,
    total_active_connections INTEGER DEFAULT 0,
    network_growth_rate DECIMAL(5,2) DEFAULT 0.0,
    mutual_connections_count INTEGER DEFAULT 0,
    
    -- Skill metrics
    skills_endorsed INTEGER DEFAULT 0,
    endorsements_received INTEGER DEFAULT 0,
    skill_diversity_score DECIMAL(5,2) DEFAULT 0.0,
    
    -- Project metrics
    projects_completed INTEGER DEFAULT 0,
    projects_on_time INTEGER DEFAULT 0,
    on_time_delivery_rate DECIMAL(5,2) DEFAULT 0.0,
    average_project_duration INTEGER DEFAULT 0, -- Days
    
    -- Quality metrics
    average_task_difficulty DECIMAL(3,2) DEFAULT 0.0,
    quality_score DECIMAL(5,2) DEFAULT 0.0,
    peer_rating_average DECIMAL(3,2) DEFAULT 0.0,
    
    -- Engagement metrics
    activity_score DECIMAL(10,2) DEFAULT 0.0,
    contribution_frequency DECIMAL(5,2) DEFAULT 0.0,
    platform_engagement_score DECIMAL(5,2) DEFAULT 0.0,
    
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure unique metrics per user per period
    CONSTRAINT collaboration_metrics_unique UNIQUE (user_id, metric_period, period_start)
);

-- Create network_analytics table for network analysis and insights
CREATE TABLE IF NOT EXISTS public.network_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Network structure metrics
    total_connections INTEGER DEFAULT 0,
    direct_connections INTEGER DEFAULT 0,
    second_degree_connections INTEGER DEFAULT 0,
    network_density DECIMAL(5,4) DEFAULT 0.0,
    clustering_coefficient DECIMAL(5,4) DEFAULT 0.0,
    
    -- Influence metrics
    network_influence_score DECIMAL(10,2) DEFAULT 0.0,
    betweenness_centrality DECIMAL(10,6) DEFAULT 0.0,
    closeness_centrality DECIMAL(10,6) DEFAULT 0.0,
    eigenvector_centrality DECIMAL(10,6) DEFAULT 0.0,
    
    -- Connection quality metrics
    strong_ties INTEGER DEFAULT 0, -- Frequent collaborators
    weak_ties INTEGER DEFAULT 0, -- Occasional connections
    bridge_connections INTEGER DEFAULT 0, -- Connections to different clusters
    skill_complementarity_score DECIMAL(5,2) DEFAULT 0.0,
    
    -- Network growth metrics
    connections_this_period INTEGER DEFAULT 0,
    connection_growth_rate DECIMAL(5,2) DEFAULT 0.0,
    network_reach INTEGER DEFAULT 0, -- Total reachable users within 3 degrees
    
    -- Collaboration network metrics
    collaboration_partners INTEGER DEFAULT 0,
    repeat_collaborators INTEGER DEFAULT 0,
    cross_skill_collaborations INTEGER DEFAULT 0,
    
    -- Network diversity metrics
    skill_diversity_in_network DECIMAL(5,2) DEFAULT 0.0,
    industry_diversity_score DECIMAL(5,2) DEFAULT 0.0,
    geographic_diversity_score DECIMAL(5,2) DEFAULT 0.0,
    
    -- Analysis period
    analysis_period VARCHAR(20) DEFAULT 'monthly',
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure unique analytics per user per period
    CONSTRAINT network_analytics_unique UNIQUE (user_id, analysis_period, period_start)
);

-- ============================================================================
-- ENDORSEMENT & RECOMMENDATION SYSTEM
-- ============================================================================

-- Create user_endorsements table (enhanced version of existing skill_endorsements)
CREATE TABLE IF NOT EXISTS public.user_endorsements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endorser_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    endorsed_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Endorsement details
    endorsement_type VARCHAR(30) DEFAULT 'skill' CHECK (
        endorsement_type IN ('skill', 'collaboration', 'leadership', 'reliability', 'innovation', 'mentorship')
    ),
    skill_id UUID REFERENCES public.skills(id) ON DELETE SET NULL,
    skill_name VARCHAR(100), -- For skills not in skills table
    
    -- Endorsement strength and details
    endorsement_level INTEGER DEFAULT 3 CHECK (endorsement_level BETWEEN 1 AND 5),
    endorsement_title VARCHAR(255),
    endorsement_message TEXT,
    
    -- Context and evidence
    project_context VARCHAR(255),
    collaboration_duration INTEGER, -- Days worked together
    evidence_urls TEXT[], -- Links to work examples
    
    -- Verification and credibility
    is_verified BOOLEAN DEFAULT false,
    verified_by UUID REFERENCES auth.users(id),
    verified_at TIMESTAMP WITH TIME ZONE,
    credibility_score DECIMAL(3,2) DEFAULT 0.0, -- Based on endorser's reputation
    
    -- Visibility and status
    is_public BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'disputed', 'withdrawn')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Prevent self-endorsements and duplicates
    CONSTRAINT user_endorsements_no_self CHECK (endorser_id != endorsed_user_id),
    CONSTRAINT user_endorsements_unique UNIQUE (endorser_id, endorsed_user_id, endorsement_type, skill_id, skill_name)
);

-- Create recommendation_algorithms table for tracking recommendation performance
CREATE TABLE IF NOT EXISTS public.recommendation_algorithms (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    algorithm_name VARCHAR(100) NOT NULL,
    algorithm_version VARCHAR(20) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL CHECK (
        algorithm_type IN ('ally_recommendation', 'skill_recommendation', 'project_recommendation', 'collaboration_recommendation')
    ),
    
    -- Algorithm configuration
    parameters JSONB DEFAULT '{}',
    weights JSONB DEFAULT '{}',
    thresholds JSONB DEFAULT '{}',
    
    -- Performance metrics
    accuracy_score DECIMAL(5,4) DEFAULT 0.0,
    precision_score DECIMAL(5,4) DEFAULT 0.0,
    recall_score DECIMAL(5,4) DEFAULT 0.0,
    f1_score DECIMAL(5,4) DEFAULT 0.0,
    
    -- Usage statistics
    total_recommendations INTEGER DEFAULT 0,
    accepted_recommendations INTEGER DEFAULT 0,
    acceptance_rate DECIMAL(5,4) DEFAULT 0.0,
    
    -- Status and deployment
    is_active BOOLEAN DEFAULT true,
    deployment_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure unique algorithm versions
    CONSTRAINT recommendation_algorithms_unique UNIQUE (algorithm_name, algorithm_version)
);

-- ============================================================================
-- ADD MISSING COLUMNS
-- ============================================================================

-- Add missing columns to existing tables
ALTER TABLE public.network_analytics ADD COLUMN IF NOT EXISTS analysis_period VARCHAR(20) DEFAULT 'monthly';
ALTER TABLE public.network_analytics ADD COLUMN IF NOT EXISTS period_start DATE;
ALTER TABLE public.network_analytics ADD COLUMN IF NOT EXISTS period_end DATE;
ALTER TABLE public.network_analytics ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.network_analytics ADD COLUMN IF NOT EXISTS network_influence_score DECIMAL(10,2) DEFAULT 0.0;
ALTER TABLE public.network_analytics ADD COLUMN IF NOT EXISTS total_connections INTEGER DEFAULT 0;
ALTER TABLE public.user_achievements ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.user_achievements ADD COLUMN IF NOT EXISTS achievement_type VARCHAR(50);
ALTER TABLE public.user_achievements ADD COLUMN IF NOT EXISTS earned_at TIMESTAMP WITH TIME ZONE DEFAULT now();
ALTER TABLE public.recognition_rankings ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.recognition_rankings ADD COLUMN IF NOT EXISTS category VARCHAR(50);
ALTER TABLE public.recognition_rankings ADD COLUMN IF NOT EXISTS rank_position INTEGER;
ALTER TABLE public.collaboration_metrics ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.collaboration_metrics ADD COLUMN IF NOT EXISTS metric_period VARCHAR(20) DEFAULT 'monthly';

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- User achievements indexes
CREATE INDEX IF NOT EXISTS idx_user_achievements_user ON public.user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_type ON public.user_achievements(achievement_type);
CREATE INDEX IF NOT EXISTS idx_user_achievements_level ON public.user_achievements(achievement_level);
CREATE INDEX IF NOT EXISTS idx_user_achievements_earned ON public.user_achievements(earned_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_achievements_featured ON public.user_achievements(is_featured) WHERE is_featured = true;

-- Recognition rankings indexes
CREATE INDEX IF NOT EXISTS idx_recognition_rankings_user ON public.recognition_rankings(user_id);
CREATE INDEX IF NOT EXISTS idx_recognition_rankings_category ON public.recognition_rankings(ranking_category);
CREATE INDEX IF NOT EXISTS idx_recognition_rankings_period ON public.recognition_rankings(ranking_period, period_start DESC);
CREATE INDEX IF NOT EXISTS idx_recognition_rankings_rank ON public.recognition_rankings(ranking_category, current_rank);
CREATE INDEX IF NOT EXISTS idx_recognition_rankings_score ON public.recognition_rankings(ranking_category, score DESC);

-- Collaboration metrics indexes
CREATE INDEX IF NOT EXISTS idx_collaboration_metrics_user ON public.collaboration_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_metrics_period ON public.collaboration_metrics(metric_period, period_start DESC);
CREATE INDEX IF NOT EXISTS idx_collaboration_metrics_success_rate ON public.collaboration_metrics(collaboration_success_rate DESC);
CREATE INDEX IF NOT EXISTS idx_collaboration_metrics_activity ON public.collaboration_metrics(activity_score DESC);

-- Network analytics indexes
CREATE INDEX IF NOT EXISTS idx_network_analytics_user ON public.network_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_network_analytics_period ON public.network_analytics(analysis_period, period_start DESC);
CREATE INDEX IF NOT EXISTS idx_network_analytics_influence ON public.network_analytics(network_influence_score DESC);
CREATE INDEX IF NOT EXISTS idx_network_analytics_connections ON public.network_analytics(total_connections DESC);

-- User endorsements indexes
CREATE INDEX IF NOT EXISTS idx_user_endorsements_endorser ON public.user_endorsements(endorser_id);
CREATE INDEX IF NOT EXISTS idx_user_endorsements_endorsed ON public.user_endorsements(endorsed_user_id);
CREATE INDEX IF NOT EXISTS idx_user_endorsements_type ON public.user_endorsements(endorsement_type);
CREATE INDEX IF NOT EXISTS idx_user_endorsements_skill ON public.user_endorsements(skill_id);
CREATE INDEX IF NOT EXISTS idx_user_endorsements_level ON public.user_endorsements(endorsement_level DESC);
CREATE INDEX IF NOT EXISTS idx_user_endorsements_verified ON public.user_endorsements(is_verified) WHERE is_verified = true;

-- Recommendation algorithms indexes
CREATE INDEX IF NOT EXISTS idx_recommendation_algorithms_type ON public.recommendation_algorithms(algorithm_type);
CREATE INDEX IF NOT EXISTS idx_recommendation_algorithms_active ON public.recommendation_algorithms(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_recommendation_algorithms_performance ON public.recommendation_algorithms(acceptance_rate DESC);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recognition_rankings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.network_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_endorsements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recommendation_algorithms ENABLE ROW LEVEL SECURITY;

-- User achievements policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_achievements' AND policyname = 'Users can view public achievements') THEN
        CREATE POLICY "Users can view public achievements" ON public.user_achievements
            FOR SELECT USING (is_public = true OR user_id = auth.uid());
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_achievements' AND policyname = 'Users can manage their own achievements') THEN
        CREATE POLICY "Users can manage their own achievements" ON public.user_achievements
            FOR ALL USING (user_id = auth.uid());
    END IF;
END $$;

-- Recognition rankings policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'recognition_rankings' AND policyname = 'Users can view all rankings') THEN
        CREATE POLICY "Users can view all rankings" ON public.recognition_rankings
            FOR SELECT USING (true);
    END IF;
END $$;

CREATE POLICY "System can manage rankings" ON public.recognition_rankings
    FOR ALL USING (true); -- System-managed table

-- Collaboration metrics policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'collaboration_metrics' AND policyname = 'Users can view their own metrics') THEN
        CREATE POLICY "Users can view their own metrics" ON public.collaboration_metrics
            FOR SELECT USING (user_id = auth.uid());
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'collaboration_metrics' AND policyname = 'System can manage metrics') THEN
        CREATE POLICY "System can manage metrics" ON public.collaboration_metrics
            FOR ALL USING (true); -- System-managed table
    END IF;
END $$;

-- Network analytics policies
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'network_analytics' AND policyname = 'Users can view their own network analytics') THEN
        CREATE POLICY "Users can view their own network analytics" ON public.network_analytics
            FOR SELECT USING (user_id = auth.uid());
    END IF;
END $$;

CREATE POLICY "System can manage network analytics" ON public.network_analytics
    FOR ALL USING (true); -- System-managed table

-- User endorsements policies
CREATE POLICY "Users can view endorsements involving them" ON public.user_endorsements
    FOR SELECT USING (
        (is_public = true) OR 
        (endorser_id = auth.uid()) OR 
        (endorsed_user_id = auth.uid())
    );

CREATE POLICY "Users can create endorsements" ON public.user_endorsements
    FOR INSERT WITH CHECK (endorser_id = auth.uid());

CREATE POLICY "Users can update their own endorsements" ON public.user_endorsements
    FOR UPDATE USING (endorser_id = auth.uid());

-- Recommendation algorithms policies
CREATE POLICY "Users can view active algorithms" ON public.recommendation_algorithms
    FOR SELECT USING (is_active = true);

CREATE POLICY "System can manage algorithms" ON public.recommendation_algorithms
    FOR ALL USING (true); -- System-managed table

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.user_achievements TO authenticated;
GRANT SELECT ON public.recognition_rankings TO authenticated;
GRANT SELECT ON public.collaboration_metrics TO authenticated;
GRANT SELECT ON public.network_analytics TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.user_endorsements TO authenticated;
GRANT SELECT ON public.recommendation_algorithms TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.user_achievements IS 'User achievements and recognition system with verification';
COMMENT ON TABLE public.recognition_rankings IS 'Leaderboards and ranking system for various performance categories';
COMMENT ON TABLE public.collaboration_metrics IS 'Comprehensive collaboration performance metrics and analytics';
COMMENT ON TABLE public.network_analytics IS 'Network analysis and social graph insights for users';
COMMENT ON TABLE public.user_endorsements IS 'Enhanced endorsement system for skills and collaboration';
COMMENT ON TABLE public.recommendation_algorithms IS 'Algorithm performance tracking for recommendation systems';
