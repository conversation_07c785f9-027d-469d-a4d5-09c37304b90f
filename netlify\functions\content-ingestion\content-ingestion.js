// External Content Ingestion API
// Handles importing content from external sources like blogs, Unreal Learning, etc.

const { createClient } = require('@supabase/supabase-js');
const cheerio = require('cheerio');
const fetch = require('node-fetch');
const TurndownService = require('turndown');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Initialize Turndown for HTML to Markdown conversion
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced'
});

// Content source configurations
const contentSources = {
  'dev.to': {
    name: 'Dev.to',
    apiEndpoint: 'https://dev.to/api/articles',
    parseMethod: 'api',
    selectors: {
      title: 'h1',
      content: '.crayons-article__main',
      author: '.crayons-story__secondary .crayons-link',
      publishDate: 'time[datetime]'
    }
  },
  'medium.com': {
    name: 'Medium',
    parseMethod: 'scrape',
    selectors: {
      title: 'h1[data-testid="storyTitle"]',
      content: 'article section',
      author: '[data-testid="authorName"]',
      publishDate: '[data-testid="storyPublishDate"]'
    }
  },
  'docs.github.com': {
    name: 'GitHub Docs',
    parseMethod: 'scrape',
    selectors: {
      title: 'h1.f00-light',
      content: '.markdown-body',
      author: null,
      publishDate: null
    }
  },
  'dev.epicgames.com': {
    name: 'Unreal Engine Learning',
    parseMethod: 'scrape',
    selectors: {
      title: 'h1.title',
      content: '.content-body',
      author: '.author-name',
      publishDate: '.publish-date'
    }
  }
};

exports.handler = async (event, context) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const { httpMethod, path, body } = event;
    const pathSegments = path.split('/').filter(Boolean);
    const action = pathSegments[pathSegments.length - 1];

    switch (httpMethod) {
      case 'POST':
        if (action === 'import') {
          return await handleContentImport(JSON.parse(body), headers);
        } else if (action === 'preview') {
          return await handleContentPreview(JSON.parse(body), headers);
        } else if (action === 'batch-import') {
          return await handleBatchImport(JSON.parse(body), headers);
        }
        break;

      case 'GET':
        if (action === 'sources') {
          return await handleGetSources(headers);
        } else if (action === 'jobs') {
          return await handleGetImportJobs(event.queryStringParameters, headers);
        }
        break;

      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'Endpoint not found' })
    };

  } catch (error) {
    console.error('Content ingestion error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      })
    };
  }
};

// Handle content import
async function handleContentImport(data, headers) {
  const { url, userId, importSettings = {} } = data;

  if (!url || !userId) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'URL and userId are required' })
    };
  }

  try {
    // Create import job
    const { data: job, error: jobError } = await supabase
      .from('content_import_jobs')
      .insert({
        external_url: url,
        requested_by: userId,
        status: 'pending',
        import_method: 'api',
        import_settings: importSettings
      })
      .select()
      .single();

    if (jobError) throw jobError;

    // Process import asynchronously
    processImportJob(job.id, url, importSettings);

    return {
      statusCode: 202,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Import job created successfully',
        jobId: job.id,
        status: 'pending'
      })
    };

  } catch (error) {
    console.error('Import error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to create import job' })
    };
  }
}

// Handle content preview (without saving)
async function handleContentPreview(data, headers) {
  const { url } = data;

  if (!url) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'URL is required' })
    };
  }

  try {
    const contentData = await extractContentFromUrl(url);
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        preview: contentData
      })
    };

  } catch (error) {
    console.error('Preview error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to preview content' })
    };
  }
}

// Handle batch import
async function handleBatchImport(data, headers) {
  const { urls, userId, importSettings = {} } = data;

  if (!urls || !Array.isArray(urls) || urls.length === 0 || !userId) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'URLs array and userId are required' })
    };
  }

  try {
    const jobs = [];

    for (const url of urls) {
      const { data: job, error: jobError } = await supabase
        .from('content_import_jobs')
        .insert({
          external_url: url,
          requested_by: userId,
          status: 'pending',
          import_method: 'batch',
          import_settings: importSettings
        })
        .select()
        .single();

      if (jobError) {
        console.error('Job creation error for URL:', url, jobError);
        continue;
      }

      jobs.push(job);
      
      // Process each job asynchronously
      processImportJob(job.id, url, importSettings);
    }

    return {
      statusCode: 202,
      headers,
      body: JSON.stringify({
        success: true,
        message: `Created ${jobs.length} import jobs`,
        jobs: jobs.map(job => ({ id: job.id, url: job.external_url, status: 'pending' }))
      })
    };

  } catch (error) {
    console.error('Batch import error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to create batch import jobs' })
    };
  }
}

// Get available content sources
async function handleGetSources(headers) {
  try {
    const { data: sources, error } = await supabase
      .from('external_content_sources')
      .select('*')
      .eq('is_active', true);

    if (error) throw error;

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        sources: sources || []
      })
    };

  } catch (error) {
    console.error('Get sources error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to get content sources' })
    };
  }
}

// Get import jobs
async function handleGetImportJobs(queryParams, headers) {
  try {
    let query = supabase
      .from('content_import_jobs')
      .select('*')
      .order('created_at', { ascending: false });

    if (queryParams?.userId) {
      query = query.eq('requested_by', queryParams.userId);
    }

    if (queryParams?.status) {
      query = query.eq('status', queryParams.status);
    }

    const limit = parseInt(queryParams?.limit) || 50;
    query = query.limit(limit);

    const { data: jobs, error } = await query;

    if (error) throw error;

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        jobs: jobs || []
      })
    };

  } catch (error) {
    console.error('Get jobs error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to get import jobs' })
    };
  }
}

// Process import job asynchronously
async function processImportJob(jobId, url, importSettings) {
  try {
    // Update job status to processing
    await supabase
      .from('content_import_jobs')
      .update({ 
        status: 'processing',
        started_at: new Date().toISOString()
      })
      .eq('id', jobId);

    const startTime = Date.now();

    // Extract content from URL
    const contentData = await extractContentFromUrl(url, importSettings);

    // Create learning content entry
    const { data: content, error: contentError } = await supabase
      .from('learning_content')
      .insert({
        title: contentData.title,
        slug: generateSlug(contentData.title),
        description: contentData.description || contentData.summary,
        content_type: 'external_import',
        content_body: contentData.content,
        content_summary: contentData.summary,
        external_source_url: url,
        external_source_name: contentData.sourceName,
        external_author_name: contentData.author,
        external_author_url: contentData.authorUrl,
        original_publish_date: contentData.publishDate,
        import_method: 'api',
        status: 'pending_review',
        estimated_read_time_minutes: estimateReadTime(contentData.content),
        tags: contentData.tags || [],
        skills_covered: contentData.skills || [],
        featured_image_url: contentData.featuredImage,
        thumbnail_url: contentData.thumbnail
      })
      .select()
      .single();

    if (contentError) throw contentError;

    const processingTime = Math.round((Date.now() - startTime) / 1000);

    // Update job as completed
    await supabase
      .from('content_import_jobs')
      .update({
        status: 'completed',
        content_id: content.id,
        processed_content: contentData,
        processing_time_seconds: processingTime,
        completed_at: new Date().toISOString()
      })
      .eq('id', jobId);

    console.log(`Import job ${jobId} completed successfully`);

  } catch (error) {
    console.error(`Import job ${jobId} failed:`, error);

    // Update job as failed
    await supabase
      .from('content_import_jobs')
      .update({
        status: 'failed',
        error_message: error.message,
        completed_at: new Date().toISOString()
      })
      .eq('id', jobId);
  }
}

// Extract content from URL
async function extractContentFromUrl(url, settings = {}) {
  const domain = new URL(url).hostname;
  const sourceConfig = Object.entries(contentSources).find(([key]) => 
    domain.includes(key)
  )?.[1];

  if (!sourceConfig) {
    // Generic scraping for unknown sources
    return await genericContentExtraction(url, settings);
  }

  if (sourceConfig.parseMethod === 'api') {
    return await apiContentExtraction(url, sourceConfig, settings);
  } else {
    return await scrapeContentExtraction(url, sourceConfig, settings);
  }
}

// Generic content extraction for unknown sources
async function genericContentExtraction(url, settings) {
  const response = await fetch(url, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (compatible; RoyalteaBot/1.0; +https://royalty.technology)'
    }
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch content: ${response.status}`);
  }

  const html = await response.text();
  const $ = cheerio.load(html);

  // Extract basic metadata
  const title = $('title').text().trim() || 
                $('h1').first().text().trim() || 
                'Imported Content';

  const description = $('meta[name="description"]').attr('content') ||
                     $('meta[property="og:description"]').attr('content') ||
                     $('p').first().text().trim().substring(0, 200);

  // Try to find main content area
  const contentSelectors = [
    'article',
    '.content',
    '.post-content',
    '.entry-content',
    '.article-content',
    'main',
    '.main-content'
  ];

  let content = '';
  for (const selector of contentSelectors) {
    const element = $(selector);
    if (element.length > 0 && element.text().trim().length > 100) {
      content = turndownService.turndown(element.html());
      break;
    }
  }

  // Fallback to body content if no specific content area found
  if (!content) {
    $('script, style, nav, header, footer, aside').remove();
    content = turndownService.turndown($('body').html());
  }

  return {
    title,
    description,
    content,
    summary: description,
    sourceName: new URL(url).hostname,
    author: $('meta[name="author"]').attr('content') || null,
    publishDate: $('meta[property="article:published_time"]').attr('content') || null,
    featuredImage: $('meta[property="og:image"]').attr('content') || null,
    thumbnail: $('meta[property="og:image"]').attr('content') || null,
    tags: [],
    skills: []
  };
}

// API-based content extraction
async function apiContentExtraction(url, sourceConfig, settings) {
  // Implementation for API-based sources like Dev.to
  if (sourceConfig.name === 'Dev.to') {
    const articleId = url.match(/\/([^\/]+)$/)?.[1];
    if (!articleId) throw new Error('Invalid Dev.to URL');

    const apiResponse = await fetch(`${sourceConfig.apiEndpoint}/${articleId}`);
    if (!apiResponse.ok) throw new Error('Failed to fetch from Dev.to API');

    const data = await apiResponse.json();
    
    return {
      title: data.title,
      description: data.description,
      content: turndownService.turndown(data.body_html),
      summary: data.description,
      sourceName: 'Dev.to',
      author: data.user?.name,
      authorUrl: data.user?.website_url,
      publishDate: data.published_at,
      featuredImage: data.cover_image,
      thumbnail: data.social_image,
      tags: data.tag_list || [],
      skills: data.tag_list || []
    };
  }

  throw new Error('API extraction not implemented for this source');
}

// Scraping-based content extraction
async function scrapeContentExtraction(url, sourceConfig, settings) {
  const response = await fetch(url, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (compatible; RoyalteaBot/1.0; +https://royalty.technology)'
    }
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch content: ${response.status}`);
  }

  const html = await response.text();
  const $ = cheerio.load(html);

  const title = $(sourceConfig.selectors.title).first().text().trim();
  const content = turndownService.turndown($(sourceConfig.selectors.content).html() || '');
  const author = sourceConfig.selectors.author ? $(sourceConfig.selectors.author).first().text().trim() : null;
  const publishDate = sourceConfig.selectors.publishDate ? $(sourceConfig.selectors.publishDate).attr('datetime') || $(sourceConfig.selectors.publishDate).text().trim() : null;

  return {
    title,
    description: $('meta[name="description"]').attr('content') || '',
    content,
    summary: $('meta[name="description"]').attr('content') || '',
    sourceName: sourceConfig.name,
    author,
    publishDate,
    featuredImage: $('meta[property="og:image"]').attr('content') || null,
    thumbnail: $('meta[property="og:image"]').attr('content') || null,
    tags: [],
    skills: []
  };
}

// Utility functions
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

function estimateReadTime(content) {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}
