-- Create revenue_tranches table for project wizard step 4
-- This table stores revenue tranche configurations for projects

CREATE TABLE IF NOT EXISTS public.revenue_tranches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    
    -- Tranche Details
    name TEXT NOT NULL,
    description TEXT,
    start_date DATE,
    end_date DATE,
    
    -- Revenue Sources (array of source names/types)
    revenue_sources JSONB DEFAULT '[]'::jsonb,
    
    -- Platform Fee Configuration
    platform_fee_config JSONB DEFAULT '{
        "apply_before": true,
        "percentage": 5
    }'::jsonb,
    
    -- Distribution Thresholds
    distribution_thresholds JSONB DEFAULT '{
        "minimum_revenue": 0,
        "maximum_payout": null,
        "per_contributor_minimum": 0,
        "per_contributor_maximum": null
    }'::jsonb,
    
    -- Rollover Configuration
    rollover_config TEXT DEFAULT 'none' CHECK (rollover_config IN ('none', 'next_period', 'accumulate')),
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_revenue_tranches_project_id ON public.revenue_tranches(project_id);
CREATE INDEX IF NOT EXISTS idx_revenue_tranches_dates ON public.revenue_tranches(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_revenue_tranches_created_at ON public.revenue_tranches(created_at DESC);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_revenue_tranches_updated_at
BEFORE UPDATE ON public.revenue_tranches
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE public.revenue_tranches ENABLE ROW LEVEL SECURITY;

-- Add missing columns to project_contributors table
ALTER TABLE public.project_contributors ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'removed'));
ALTER TABLE public.project_contributors ADD COLUMN IF NOT EXISTS permission_level TEXT DEFAULT 'Contributor';

-- RLS Policies
-- Project owners and contributors can view revenue tranches
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'revenue_tranches' AND policyname = 'Project members can view revenue tranches') THEN
        CREATE POLICY "Project members can view revenue tranches"
        ON public.revenue_tranches FOR SELECT
        USING (
            EXISTS (
                SELECT 1 FROM public.projects p
                WHERE p.id = revenue_tranches.project_id
                AND (
                    p.created_by = auth.uid() OR
                    EXISTS (
                        SELECT 1 FROM public.project_contributors pc
                        WHERE pc.project_id = p.id
                        AND pc.user_id = auth.uid()
                        AND pc.status = 'active'
                    )
                )
            )
        );
    END IF;
END $$;

-- Project owners can manage revenue tranches
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'revenue_tranches' AND policyname = 'Project owners can manage revenue tranches') THEN
        CREATE POLICY "Project owners can manage revenue tranches"
        ON public.revenue_tranches FOR ALL
        USING (
            EXISTS (
                SELECT 1 FROM public.projects p
                WHERE p.id = revenue_tranches.project_id
                AND p.created_by = auth.uid()
            )
        );
    END IF;
END $$;

-- Project admins can manage revenue tranches
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'revenue_tranches' AND policyname = 'Project admins can manage revenue tranches') THEN
        CREATE POLICY "Project admins can manage revenue tranches"
        ON public.revenue_tranches FOR ALL
        USING (
            EXISTS (
                SELECT 1 FROM public.project_contributors pc
                WHERE pc.project_id = revenue_tranches.project_id
                AND pc.user_id = auth.uid()
                AND pc.status = 'active'
                AND pc.permission_level IN ('Owner', 'Admin')
            )
        );
    END IF;
END $$;

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.revenue_tranches TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Add comment for documentation
COMMENT ON TABLE public.revenue_tranches IS 'Stores revenue tranche configurations for projects, defining how revenue will be distributed over different periods';
COMMENT ON COLUMN public.revenue_tranches.revenue_sources IS 'Array of revenue source names/types for this tranche';
COMMENT ON COLUMN public.revenue_tranches.platform_fee_config IS 'Configuration for platform fees including whether to apply before distribution and percentage';
COMMENT ON COLUMN public.revenue_tranches.distribution_thresholds IS 'Thresholds for revenue distribution including minimum amounts and maximums';
COMMENT ON COLUMN public.revenue_tranches.rollover_config IS 'How to handle unused revenue: none, next_period, or accumulate';
