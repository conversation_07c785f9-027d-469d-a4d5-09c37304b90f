import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Input, Textarea, Select, SelectItem,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure,
  Table, TableHeader, TableColumn, TableBody, TableRow, TableCell,
  Chip, Progress, Spinner, Divider, Checkbox
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  BookOpen, Plus, Edit, Trash2, Eye, Users, Clock, 
  ArrowUp, ArrowDown, GraduationCap, Target 
} from 'lucide-react';

/**
 * Learning Path Manager Component
 * 
 * Admin interface for creating and managing official learning paths
 * from approved videos and organizing content by skill/difficulty.
 */
const LearningPathManager = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [learningPaths, setLearningPaths] = useState([]);
  const [availableVideos, setAvailableVideos] = useState([]);
  const [selectedPath, setSelectedPath] = useState(null);
  const [pathForm, setPathForm] = useState({
    title: '',
    description: '',
    difficulty_level: 'beginner',
    estimated_duration_hours: '',
    skills: [],
    categories: [],
    is_featured: false,
    is_active: true,
    prerequisites: [],
    learning_objectives: [],
    target_audience: '',
    videos: []
  });

  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();

  const availableSkills = [
    'JavaScript', 'Python', 'React', 'Node.js', 'HTML', 'CSS', 'TypeScript',
    'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin',
    'SQL', 'MongoDB', 'PostgreSQL', 'MySQL', 'Redis',
    'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'DevOps',
    'Machine Learning', 'Data Science', 'AI', 'Deep Learning',
    'UI Design', 'UX Design', 'Figma', 'Adobe XD', 'Photoshop',
    'Project Management', 'Agile', 'Scrum', 'Leadership'
  ];

  const availableCategories = [
    'Programming', 'Web Development', 'Mobile Development', 'Data Science',
    'Machine Learning', 'DevOps', 'Cloud Computing', 'Database',
    'Design', 'Project Management', 'Business', 'Marketing'
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load learning paths
      const { data: paths, error: pathsError } = await supabase
        .from('learning_paths')
        .select(`
          *,
          learning_path_videos(
            id,
            order_index,
            course_catalog(*)
          )
        `)
        .order('created_at', { ascending: false });

      if (pathsError) throw pathsError;

      // Load available videos from course catalog
      const { data: videos, error: videosError } = await supabase
        .from('course_catalog')
        .select('*')
        .eq('is_active', true)
        .order('title');

      if (videosError) throw videosError;

      setLearningPaths(paths || []);
      setAvailableVideos(videos || []);

    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load learning paths');
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePath = () => {
    setPathForm({
      title: '',
      description: '',
      difficulty_level: 'beginner',
      estimated_duration_hours: '',
      skills: [],
      categories: [],
      is_featured: false,
      is_active: true,
      prerequisites: [],
      learning_objectives: [],
      target_audience: '',
      videos: []
    });
    setSelectedPath(null);
    onCreateOpen();
  };

  const handleEditPath = (path) => {
    setSelectedPath(path);
    setPathForm({
      title: path.title || '',
      description: path.description || '',
      difficulty_level: path.difficulty_level || 'beginner',
      estimated_duration_hours: path.estimated_duration_hours?.toString() || '',
      skills: path.skills || [],
      categories: path.categories || [],
      is_featured: path.is_featured || false,
      is_active: path.is_active !== false,
      prerequisites: path.prerequisites || [],
      learning_objectives: path.learning_objectives || [],
      target_audience: path.target_audience || '',
      videos: path.learning_path_videos?.map(lpv => ({
        id: lpv.course_catalog.id,
        order_index: lpv.order_index,
        ...lpv.course_catalog
      })).sort((a, b) => a.order_index - b.order_index) || []
    });
    onEditOpen();
  };

  const saveLearningPath = async () => {
    try {
      if (!pathForm.title.trim()) {
        toast.error('Please enter a title');
        return;
      }

      setLoading(true);

      const pathData = {
        title: pathForm.title.trim(),
        description: pathForm.description.trim(),
        difficulty_level: pathForm.difficulty_level,
        estimated_duration_hours: pathForm.estimated_duration_hours ? parseInt(pathForm.estimated_duration_hours) : null,
        skills: pathForm.skills,
        categories: pathForm.categories,
        is_featured: pathForm.is_featured,
        is_active: pathForm.is_active,
        prerequisites: pathForm.prerequisites.filter(p => p.trim()),
        learning_objectives: pathForm.learning_objectives.filter(o => o.trim()),
        target_audience: pathForm.target_audience.trim(),
        created_by: currentUser.id,
        updated_by: currentUser.id
      };

      let pathId;

      if (selectedPath) {
        // Update existing path
        const { error: updateError } = await supabase
          .from('learning_paths')
          .update(pathData)
          .eq('id', selectedPath.id);

        if (updateError) throw updateError;
        pathId = selectedPath.id;

        // Delete existing video associations
        const { error: deleteError } = await supabase
          .from('learning_path_videos')
          .delete()
          .eq('learning_path_id', selectedPath.id);

        if (deleteError) throw deleteError;

      } else {
        // Create new path
        const { data: newPath, error: createError } = await supabase
          .from('learning_paths')
          .insert([pathData])
          .select()
          .single();

        if (createError) throw createError;
        pathId = newPath.id;
      }

      // Add video associations
      if (pathForm.videos.length > 0) {
        const videoAssociations = pathForm.videos.map((video, index) => ({
          learning_path_id: pathId,
          course_id: video.id,
          order_index: index + 1
        }));

        const { error: videoError } = await supabase
          .from('learning_path_videos')
          .insert(videoAssociations);

        if (videoError) throw videoError;
      }

      toast.success(`Learning path ${selectedPath ? 'updated' : 'created'} successfully!`);
      onCreateClose();
      onEditClose();
      loadData();

    } catch (error) {
      console.error('Error saving learning path:', error);
      toast.error('Failed to save learning path');
    } finally {
      setLoading(false);
    }
  };

  const deleteLearningPath = async (pathId) => {
    if (!confirm('Are you sure you want to delete this learning path?')) {
      return;
    }

    try {
      setLoading(true);

      const { error } = await supabase
        .from('learning_paths')
        .delete()
        .eq('id', pathId);

      if (error) throw error;

      toast.success('Learning path deleted successfully!');
      loadData();

    } catch (error) {
      console.error('Error deleting learning path:', error);
      toast.error('Failed to delete learning path');
    } finally {
      setLoading(false);
    }
  };

  const addVideoToPath = (video) => {
    if (!pathForm.videos.find(v => v.id === video.id)) {
      setPathForm({
        ...pathForm,
        videos: [...pathForm.videos, { ...video, order_index: pathForm.videos.length + 1 }]
      });
    }
  };

  const removeVideoFromPath = (videoId) => {
    setPathForm({
      ...pathForm,
      videos: pathForm.videos.filter(v => v.id !== videoId)
    });
  };

  const moveVideo = (videoId, direction) => {
    const videos = [...pathForm.videos];
    const currentIndex = videos.findIndex(v => v.id === videoId);
    
    if (direction === 'up' && currentIndex > 0) {
      [videos[currentIndex], videos[currentIndex - 1]] = [videos[currentIndex - 1], videos[currentIndex]];
    } else if (direction === 'down' && currentIndex < videos.length - 1) {
      [videos[currentIndex], videos[currentIndex + 1]] = [videos[currentIndex + 1], videos[currentIndex]];
    }

    // Update order indices
    videos.forEach((video, index) => {
      video.order_index = index + 1;
    });

    setPathForm({ ...pathForm, videos });
  };

  const getDifficultyColor = (level) => {
    switch (level?.toLowerCase()) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'danger';
      default: return 'default';
    }
  };

  if (loading && learningPaths.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="learning-path-manager space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Learning Path Manager</h1>
          <p className="text-default-600 mt-1">
            Create and manage official learning paths from approved videos
          </p>
        </div>
        <Button
          color="primary"
          onPress={handleCreatePath}
          startContent={<Plus className="w-4 h-4" />}
        >
          Create Learning Path
        </Button>
      </div>

      {/* Learning Paths Table */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Learning Paths</h2>
        </CardHeader>
        <CardBody>
          {learningPaths.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 mx-auto mb-4 text-default-400" />
              <h3 className="text-xl font-semibold mb-2">No learning paths yet</h3>
              <p className="text-default-600 mb-4">
                Create your first learning path to organize educational content.
              </p>
              <Button color="primary" onPress={handleCreatePath}>
                Create Learning Path
              </Button>
            </div>
          ) : (
            <Table aria-label="Learning paths table">
              <TableHeader>
                <TableColumn>PATH</TableColumn>
                <TableColumn>DIFFICULTY</TableColumn>
                <TableColumn>VIDEOS</TableColumn>
                <TableColumn>DURATION</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {learningPaths.map((path) => (
                  <TableRow key={path.id}>
                    <TableCell>
                      <div>
                        <div className="font-semibold">{path.title}</div>
                        <div className="text-sm text-default-600 line-clamp-2">
                          {path.description}
                        </div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {path.skills?.slice(0, 3).map((skill, index) => (
                            <Chip key={index} size="sm" variant="flat" color="primary">
                              {skill}
                            </Chip>
                          ))}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        size="sm" 
                        color={getDifficultyColor(path.difficulty_level)}
                        variant="flat"
                      >
                        {path.difficulty_level}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <BookOpen className="w-4 h-4 text-default-600" />
                        <span>{path.learning_path_videos?.length || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4 text-default-600" />
                        <span>{path.estimated_duration_hours || 0}h</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <Chip 
                          size="sm" 
                          color={path.is_active ? 'success' : 'default'}
                          variant="flat"
                        >
                          {path.is_active ? 'Active' : 'Inactive'}
                        </Chip>
                        {path.is_featured && (
                          <Chip size="sm" color="warning" variant="flat">
                            Featured
                          </Chip>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          color="primary"
                          variant="flat"
                          onPress={() => handleEditPath(path)}
                          startContent={<Edit className="w-3 h-3" />}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          color="danger"
                          variant="flat"
                          onPress={() => deleteLearningPath(path.id)}
                          startContent={<Trash2 className="w-3 h-3" />}
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Create/Edit Learning Path Modal */}
      <Modal
        isOpen={isCreateOpen || isEditOpen}
        onClose={() => {
          onCreateClose();
          onEditClose();
        }}
        size="5xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">
              {selectedPath ? 'Edit Learning Path' : 'Create Learning Path'}
            </h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Title *"
                  placeholder="Enter learning path title"
                  value={pathForm.title}
                  onChange={(e) => setPathForm({ ...pathForm, title: e.target.value })}
                  isRequired
                />
                <Select
                  label="Difficulty Level"
                  selectedKeys={pathForm.difficulty_level ? [pathForm.difficulty_level] : []}
                  onSelectionChange={(keys) => setPathForm({
                    ...pathForm,
                    difficulty_level: Array.from(keys)[0] || 'beginner'
                  })}
                >
                  <SelectItem key="beginner">Beginner</SelectItem>
                  <SelectItem key="intermediate">Intermediate</SelectItem>
                  <SelectItem key="advanced">Advanced</SelectItem>
                </Select>
              </div>

              <Textarea
                label="Description"
                placeholder="Describe what learners will achieve in this path"
                value={pathForm.description}
                onChange={(e) => setPathForm({ ...pathForm, description: e.target.value })}
                minRows={3}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Estimated Duration (hours)"
                  type="number"
                  placeholder="0"
                  value={pathForm.estimated_duration_hours}
                  onChange={(e) => setPathForm({ ...pathForm, estimated_duration_hours: e.target.value })}
                />
                <Input
                  label="Target Audience"
                  placeholder="Who is this path designed for?"
                  value={pathForm.target_audience}
                  onChange={(e) => setPathForm({ ...pathForm, target_audience: e.target.value })}
                />
              </div>

              {/* Skills and Categories */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Skills Covered</label>
                  <div className="flex flex-wrap gap-1 p-3 border rounded-lg min-h-[100px]">
                    {availableSkills.map((skill) => (
                      <Chip
                        key={skill}
                        size="sm"
                        variant={pathForm.skills.includes(skill) ? "solid" : "flat"}
                        color={pathForm.skills.includes(skill) ? "primary" : "default"}
                        className="cursor-pointer"
                        onClick={() => {
                          const skills = pathForm.skills.includes(skill)
                            ? pathForm.skills.filter(s => s !== skill)
                            : [...pathForm.skills, skill];
                          setPathForm({ ...pathForm, skills });
                        }}
                      >
                        {skill}
                      </Chip>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Categories</label>
                  <div className="flex flex-wrap gap-1 p-3 border rounded-lg min-h-[100px]">
                    {availableCategories.map((category) => (
                      <Chip
                        key={category}
                        size="sm"
                        variant={pathForm.categories.includes(category) ? "solid" : "flat"}
                        color={pathForm.categories.includes(category) ? "secondary" : "default"}
                        className="cursor-pointer"
                        onClick={() => {
                          const categories = pathForm.categories.includes(category)
                            ? pathForm.categories.filter(c => c !== category)
                            : [...pathForm.categories, category];
                          setPathForm({ ...pathForm, categories });
                        }}
                      >
                        {category}
                      </Chip>
                    ))}
                  </div>
                </div>
              </div>

              {/* Settings */}
              <div className="flex gap-4">
                <Checkbox
                  isSelected={pathForm.is_active}
                  onValueChange={(checked) => setPathForm({ ...pathForm, is_active: checked })}
                >
                  Active (visible to users)
                </Checkbox>
                <Checkbox
                  isSelected={pathForm.is_featured}
                  onValueChange={(checked) => setPathForm({ ...pathForm, is_featured: checked })}
                >
                  Featured (highlighted on homepage)
                </Checkbox>
              </div>

              <Divider />

              {/* Video Selection */}
              <div>
                <h4 className="text-lg font-semibold mb-4">Learning Path Videos</h4>

                {/* Selected Videos */}
                {pathForm.videos.length > 0 && (
                  <div className="mb-4">
                    <h5 className="font-medium mb-2">Selected Videos ({pathForm.videos.length})</h5>
                    <div className="space-y-2">
                      {pathForm.videos.map((video, index) => (
                        <div key={video.id} className="flex items-center gap-3 p-3 border rounded-lg">
                          <div className="flex flex-col gap-1">
                            <Button
                              size="sm"
                              variant="light"
                              isIconOnly
                              onPress={() => moveVideo(video.id, 'up')}
                              isDisabled={index === 0}
                            >
                              <ArrowUp className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="light"
                              isIconOnly
                              onPress={() => moveVideo(video.id, 'down')}
                              isDisabled={index === pathForm.videos.length - 1}
                            >
                              <ArrowDown className="w-3 h-3" />
                            </Button>
                          </div>
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                            {index + 1}
                          </div>
                          <img
                            src={video.thumbnail_url || `https://img.youtube.com/vi/${video.external_id}/mqdefault.jpg`}
                            alt={video.title}
                            className="w-16 h-12 object-cover rounded"
                          />
                          <div className="flex-1">
                            <div className="font-medium line-clamp-1">{video.title}</div>
                            <div className="text-sm text-default-600">
                              {video.duration_minutes}m • {video.difficulty_level}
                            </div>
                          </div>
                          <Button
                            size="sm"
                            color="danger"
                            variant="light"
                            onPress={() => removeVideoFromPath(video.id)}
                            startContent={<Trash2 className="w-3 h-3" />}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Available Videos */}
                <div>
                  <h5 className="font-medium mb-2">Available Videos</h5>
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {availableVideos
                      .filter(video => !pathForm.videos.find(v => v.id === video.id))
                      .map((video) => (
                        <div key={video.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-default-50">
                          <img
                            src={video.thumbnail_url || `https://img.youtube.com/vi/${video.external_id}/mqdefault.jpg`}
                            alt={video.title}
                            className="w-16 h-12 object-cover rounded"
                          />
                          <div className="flex-1">
                            <div className="font-medium line-clamp-1">{video.title}</div>
                            <div className="text-sm text-default-600">
                              {video.duration_minutes}m • {video.difficulty_level}
                            </div>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {video.skills?.slice(0, 3).map((skill, index) => (
                                <Chip key={index} size="sm" variant="flat" color="primary">
                                  {skill}
                                </Chip>
                              ))}
                            </div>
                          </div>
                          <Button
                            size="sm"
                            color="primary"
                            variant="flat"
                            onPress={() => addVideoToPath(video)}
                            startContent={<Plus className="w-3 h-3" />}
                          >
                            Add
                          </Button>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="flat"
              onPress={() => {
                onCreateClose();
                onEditClose();
              }}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={saveLearningPath}
              isLoading={loading}
            >
              {selectedPath ? 'Update' : 'Create'} Learning Path
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default LearningPathManager;
