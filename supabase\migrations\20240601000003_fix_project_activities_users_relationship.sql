-- Fix the relationship between project_activities and users tables

-- Check if the project_activities table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'project_activities'
    ) THEN
        -- First drop the view if it exists to avoid column name conflicts
        DROP VIEW IF EXISTS public.project_activities_with_users;

        -- Create a view to expose the relationship between project_activities and users
        CREATE VIEW public.project_activities_with_users AS
        SELECT
            pa.*,
            COALESCE(p.id, au.id) as user_account_id,
            au.email as user_email,
            COALESCE(p.display_name, au.email) as user_display_name,
            p.avatar_url as user_avatar_url
        FROM
            public.project_activities pa
        LEFT JOIN
            public.profiles p ON pa.user_id = p.id
        LEFT JOIN
            auth.users au ON pa.user_id = au.id;

        -- Grant permissions on the view
        GRANT SELECT ON public.project_activities_with_users TO authenticated;
        GRANT SELECT ON public.project_activities_with_users TO anon;

        RAISE NOTICE 'Created project_activities_with_users view';
    ELSE
        RAISE NOTICE 'project_activities table does not exist, skipping view creation';
    END IF;
END $$;
