import { test, expect } from '@playwright/test';

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Learning Center Modal Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate directly to production site
    await page.goto('https://royalty.technology/login');
    
    // Login with test credentials
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    
    // Wait for successful login (content changes but URL stays the same)
    await page.waitForSelector('[data-testid="dashboard"], .dashboard', { timeout: 15000 });
    
    // Navigate to learning center
    await page.goto('https://royalty.technology/learn');
    await page.waitForLoadState('networkidle');
  });

  test('should display learning center and test modal functionality', async ({ page }) => {
    // Check main header
    await expect(page.locator('h1, h2').filter({ hasText: /Learning Center|Learn/ })).toBeVisible();
    
    // Test video player modal functionality
    console.log('Testing video player modals...');
    
    // Wait for video cards to load
    await page.waitForTimeout(3000);
    
    // Look for video cards with various selectors
    const videoCardSelectors = [
      '[data-testid="youtube-course-card"]',
      '.youtube-course-card',
      '[class*="course-card"]',
      '[class*="video-card"]'
    ];
    
    let videoCards = null;
    for (const selector of videoCardSelectors) {
      videoCards = page.locator(selector);
      if (await videoCards.count() > 0) {
        console.log(`Found ${await videoCards.count()} video cards with selector: ${selector}`);
        break;
      }
    }
    
    if (videoCards && await videoCards.count() > 0) {
      const firstCard = videoCards.first();
      
      // Try to click play/start/watch button
      const playButtonSelectors = [
        'button:has-text("Start")',
        'button:has-text("Continue")',
        'button:has-text("Watch")',
        'button:has-text("Play")',
        'button[aria-label*="play"]',
        'button[aria-label*="watch"]'
      ];
      
      for (const buttonSelector of playButtonSelectors) {
        const button = firstCard.locator(buttonSelector).first();
        if (await button.count() > 0) {
          console.log(`Clicking button: ${buttonSelector}`);
          await button.click();
          
          // Check if modal opened
          await page.waitForTimeout(1000);
          const modal = page.locator('[role="dialog"]');
          if (await modal.count() > 0) {
            console.log('✅ Video modal opened successfully');
            await expect(modal).toBeVisible();
            
            // Check for iframe or video content
            const iframe = page.locator('iframe[src*="youtube"]');
            if (await iframe.count() > 0) {
              console.log('✅ YouTube iframe found in modal');
              await expect(iframe).toBeVisible();
            }
            
            // Close modal
            await page.click('button:has-text("Close")');
            await page.waitForTimeout(500);
            break;
          }
        }
      }
    } else {
      console.log('No video cards found - checking for empty state or loading');
      
      // Check for loading state
      const loading = page.locator('text=Loading, .spinner, [data-testid="loading"]');
      if (await loading.count() > 0) {
        console.log('Page is still loading...');
        await page.waitForTimeout(5000);
      }
      
      // Check for empty state
      const emptyState = page.locator('text=No content found, text=No videos, text=Coming soon');
      if (await emptyState.count() > 0) {
        console.log('Empty state detected - this is expected if no content is available');
      }
    }
  });

  test('should test action buttons and modals', async ({ page }) => {
    await page.waitForTimeout(3000);
    
    // Test Submit Video button
    console.log('Testing Submit Video modal...');
    const submitButtons = [
      'button:has-text("Submit Video")',
      'button:has-text("Submit a Video")',
      'button:has-text("Add Video")'
    ];
    
    for (const buttonSelector of submitButtons) {
      const button = page.locator(buttonSelector).first();
      if (await button.count() > 0) {
        console.log(`Found submit button: ${buttonSelector}`);
        await button.click();
        
        await page.waitForTimeout(1000);
        const modal = page.locator('[role="dialog"]');
        if (await modal.count() > 0) {
          console.log('✅ Submit video modal opened');
          await expect(modal).toBeVisible();
          
          // Close modal
          await page.click('button:has-text("Cancel"), button:has-text("Close")');
          break;
        }
      }
    }
    
    // Test Learning Queue button
    console.log('Testing Learning Queue modal...');
    const queueButtons = [
      'button:has-text("My Queues")',
      'button:has-text("Learning Queue")',
      'button:has-text("Queue")'
    ];
    
    for (const buttonSelector of queueButtons) {
      const button = page.locator(buttonSelector).first();
      if (await button.count() > 0) {
        console.log(`Found queue button: ${buttonSelector}`);
        await button.click();
        
        await page.waitForTimeout(1000);
        const modal = page.locator('[role="dialog"]');
        if (await modal.count() > 0) {
          console.log('✅ Learning queue modal opened');
          await expect(modal).toBeVisible();
          
          // Close modal
          await page.click('button:has-text("Cancel"), button:has-text("Close")');
          break;
        }
      }
    }
  });

  test('should test tab navigation and content', async ({ page }) => {
    await page.waitForTimeout(3000);
    
    // Test tab navigation
    const tabs = [
      'Learning Paths',
      'My Progress',
      'Community',
      'Vetting'
    ];
    
    for (const tabName of tabs) {
      const tab = page.locator(`[role="tab"]:has-text("${tabName}")`);
      if (await tab.count() > 0) {
        console.log(`Testing ${tabName} tab...`);
        await tab.click();
        await page.waitForTimeout(2000);
        
        // Check if tab is active
        const activeTab = page.locator(`[aria-selected="true"]:has-text("${tabName}")`);
        if (await activeTab.count() > 0) {
          console.log(`✅ ${tabName} tab is active`);
        }
        
        // Test specific functionality for each tab
        if (tabName === 'Learning Paths') {
          // Look for learning path cards and start buttons
          const pathCards = page.locator('[data-testid="learning-path-card"], .learning-path-card');
          if (await pathCards.count() > 0) {
            const startButton = pathCards.first().locator('button:has-text("Start Learning Path")');
            if (await startButton.count() > 0) {
              console.log('✅ Learning path start button found');
            }
          }
        }
        
        if (tabName === 'Vetting') {
          // Look for vetting-specific buttons
          const suggestButton = page.locator('button:has-text("Suggest Content for Vetting")');
          if (await suggestButton.count() > 0) {
            console.log('✅ Vetting suggestion button found');
            await suggestButton.click();
            
            await page.waitForTimeout(1000);
            const modal = page.locator('[role="dialog"]');
            if (await modal.count() > 0) {
              console.log('✅ Vetting suggestion modal opened');
              await page.click('button:has-text("Cancel")');
            }
          }
        }
      }
    }
  });
});
