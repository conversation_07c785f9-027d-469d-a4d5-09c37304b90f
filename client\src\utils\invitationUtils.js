// Invitation Utilities
// Handles secure invitation token generation and validation

import { supabase } from './supabase/supabase.utils';

/**
 * Generate random bytes using Web Crypto API
 * @param {number} length - Number of bytes to generate
 * @returns {string} - Hex string
 */
const generateRandomBytes = (length) => {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Generate SHA-256 hash using Web Crypto API
 * @param {string} data - Data to hash
 * @returns {Promise<string>} - Hex hash
 */
const generateHash = async (data) => {
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
  const hashArray = new Uint8Array(hashBuffer);
  return Array.from(hashArray, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Generate a secure invitation token
 * @param {string} email - The email address being invited
 * @param {string} type - The type of invitation ('project', 'friend', 'team')
 * @param {string} entityId - The ID of the project/team being invited to
 * @returns {Promise<string>} - Secure token
 */
export const generateInvitationToken = async (email, type, entityId) => {
  const timestamp = Date.now();
  const randomBytes = generateRandomBytes(16);
  const payload = `${email}:${type}:${entityId}:${timestamp}`;

  // Create a hash of the payload for security
  const hash = await generateHash(payload + randomBytes);

  // Combine timestamp, random bytes, and hash for the token
  const tokenData = `${timestamp}:${randomBytes}:${hash}`;
  const token = btoa(tokenData).replace(/[+/]/g, c => c === '+' ? '-' : '_').replace(/=/g, '');

  return token;
};

/**
 * Generate invitation link with secure token
 * @param {string} invitationId - Database invitation ID
 * @param {string} email - Email address
 * @param {string} type - Invitation type
 * @param {string} entityId - Entity ID
 * @returns {Promise<string>} - Complete invitation URL
 */
export const generateInvitationLink = async (invitationId, email, type, entityId) => {
  const token = await generateInvitationToken(email, type, entityId);
  const baseUrl = window.location.origin;

  return `${baseUrl}/invitation/accept?id=${invitationId}&token=${token}&email=${encodeURIComponent(email)}`;
};

/**
 * Validate invitation token
 * @param {string} token - Token to validate
 * @param {string} email - Expected email
 * @param {string} type - Expected type
 * @param {string} entityId - Expected entity ID
 * @returns {Promise<boolean>} - Whether token is valid
 */
export const validateInvitationToken = async (token, email, type, entityId) => {
  try {
    // Decode base64url token
    const base64 = token.replace(/-/g, '+').replace(/_/g, '/');
    const decoded = atob(base64);
    const [timestamp, randomBytes, hash] = decoded.split(':');

    // Check if token is expired (7 days)
    const tokenAge = Date.now() - parseInt(timestamp);
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    if (tokenAge > maxAge) {
      return false;
    }

    // Recreate the expected hash
    const payload = `${email}:${type}:${entityId}:${timestamp}`;
    const expectedHash = await generateHash(payload + randomBytes);

    return hash === expectedHash;
  } catch (error) {
    console.error('Token validation error:', error);
    return false;
  }
};

/**
 * Update invitation record with secure token
 * @param {string} tableName - Database table name
 * @param {string} invitationId - Invitation ID
 * @param {string} token - Generated token
 */
export const updateInvitationWithToken = async (tableName, invitationId, token) => {
  try {
    // Try to update with token columns, but handle gracefully if they don't exist
    const { error } = await supabase
      .from(tableName)
      .update({
        invitation_token: token,
        token_generated_at: new Date().toISOString()
      })
      .eq('id', invitationId);

    if (error) {
      // If columns don't exist, log but don't fail
      if (error.code === '42703') { // Column does not exist
        console.log('Invitation token columns not yet available, skipping token storage');
        return;
      }
      console.error('Failed to update invitation with token:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error updating invitation token:', error);
    // Don't throw - invitation should work even without token storage
  }
};

/**
 * Get invitation by token
 * @param {string} tableName - Database table name
 * @param {string} invitationId - Invitation ID
 * @param {string} token - Token to verify
 * @returns {Object|null} - Invitation data or null if invalid
 */
export const getInvitationByToken = async (tableName, invitationId, token) => {
  try {
    // First try to get invitation by ID and token
    const { data: invitation, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('id', invitationId)
      .eq('status', 'pending')
      .single();

    if (error || !invitation) {
      return null;
    }

    // If token columns exist, verify token
    if (invitation.invitation_token) {
      if (invitation.invitation_token !== token) {
        return null;
      }

      // Check if token is expired
      const tokenAge = Date.now() - new Date(invitation.token_generated_at).getTime();
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

      if (tokenAge > maxAge) {
        return null;
      }
    } else {
      // Fallback: if no token columns, just verify the invitation exists and is pending
      console.log('Token columns not available, using basic invitation verification');
    }

    return invitation;
  } catch (error) {
    console.error('Error getting invitation by token:', error);
    return null;
  }
};

/**
 * Generate invitation data for email
 * @param {Object} invitation - Invitation record
 * @param {Object} project - Project data (if applicable)
 * @param {Object} inviter - Inviter user data
 * @returns {Promise<Object>} - Email template variables
 */
export const generateEmailVariables = async (invitation, project, inviter) => {
  const invitationUrl = await generateInvitationLink(
    invitation.id,
    invitation.email,
    'project',
    invitation.project_id
  );

  const baseVariables = {
    userName: invitation.display_name || invitation.email.split('@')[0],
    inviterName: inviter.display_name || inviter.email,
    invitationUrl
  };

  if (project) {
    return {
      ...baseVariables,
      projectName: project.name || 'Unknown Project',
      projectDescription: project.description || 'No description available',
      role: invitation.role || 'Contributor'
    };
  }

  return baseVariables;
};
