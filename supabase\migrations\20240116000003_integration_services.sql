-- Migration: Integration Services
-- Description: Database tables for email, push notifications, Discord, and file storage integrations
-- Created: 2024-01-16
-- Integration & Services Agent

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Email logs table
CREATE TABLE IF NOT EXISTS email_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  recipient TEXT NOT NULL,
  subject TEXT NOT NULL,
  template TEXT,
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'bounced'
  message_id TEXT,
  error_message TEXT,
  variables JSONB DEFAULT '{}'::jsonb,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Push subscriptions table
CREATE TABLE IF NOT EXISTS push_subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  endpoint TEXT NOT NULL,
  p256dh_key TEXT NOT NULL,
  auth_key TEXT NOT NULL,
  user_agent TEXT,
  is_active BOOLEAN DEFAULT true,
  error_message TEXT,
  unsubscribed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, endpoint)
);

-- Push notification logs table
CREATE TABLE IF NOT EXISTS push_notification_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES push_subscriptions(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  body TEXT,
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'failed'
  error_message TEXT,
  payload JSONB DEFAULT '{}'::jsonb,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Discord integrations table
CREATE TABLE IF NOT EXISTS discord_integrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  webhook_url TEXT NOT NULL,
  guild_id TEXT,
  channel_id TEXT,
  notification_types TEXT[] DEFAULT ARRAY['payment_received', 'project_invitation', 'escrow_release'],
  is_active BOOLEAN DEFAULT true,
  error_message TEXT,
  removed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Discord notification logs table
CREATE TABLE IF NOT EXISTS discord_notification_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  integration_id UUID REFERENCES discord_integrations(id) ON DELETE SET NULL,
  notification_type TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'failed'
  error_message TEXT,
  data JSONB DEFAULT '{}'::jsonb,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- File uploads table
CREATE TABLE IF NOT EXISTS file_uploads (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  uploaded_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  original_name TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size BIGINT,
  content_type TEXT,
  storage_provider TEXT NOT NULL, -- 's3', 'cloudinary', 'local'
  storage_key TEXT NOT NULL,
  storage_url TEXT,
  is_public BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}'::jsonb,
  download_count INTEGER DEFAULT 0,
  last_accessed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Slack integrations table (for future use)
CREATE TABLE IF NOT EXISTS slack_integrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  team_id TEXT NOT NULL,
  channel_id TEXT NOT NULL,
  webhook_url TEXT NOT NULL,
  access_token TEXT,
  notification_types TEXT[] DEFAULT ARRAY['payment_received', 'project_invitation'],
  is_active BOOLEAN DEFAULT true,
  error_message TEXT,
  removed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, team_id, channel_id)
);

-- Integration preferences table
CREATE TABLE IF NOT EXISTS integration_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Email preferences
  email_notifications BOOLEAN DEFAULT true,
  email_frequency TEXT DEFAULT 'immediate', -- 'immediate', 'daily', 'weekly', 'never'
  
  -- Push notification preferences
  push_notifications BOOLEAN DEFAULT true,
  push_quiet_hours_start TIME,
  push_quiet_hours_end TIME,
  
  -- Discord preferences
  discord_notifications BOOLEAN DEFAULT false,
  discord_notification_types TEXT[] DEFAULT ARRAY[]::TEXT[],
  
  -- Slack preferences
  slack_notifications BOOLEAN DEFAULT false,
  slack_notification_types TEXT[] DEFAULT ARRAY[]::TEXT[],
  
  -- File storage preferences
  default_storage_provider TEXT DEFAULT 's3', -- 's3', 'cloudinary'
  auto_delete_after_days INTEGER,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- API usage logs table (for monitoring and rate limiting)
CREATE TABLE IF NOT EXISTS api_usage_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  endpoint TEXT NOT NULL,
  method TEXT NOT NULL,
  status_code INTEGER,
  response_time_ms INTEGER,
  ip_address INET,
  user_agent TEXT,
  request_size BIGINT,
  response_size BIGINT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON email_logs(recipient);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);
CREATE INDEX IF NOT EXISTS idx_email_logs_created_at ON email_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON push_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_is_active ON push_subscriptions(is_active);

CREATE INDEX IF NOT EXISTS idx_push_notification_logs_user_id ON push_notification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_push_notification_logs_status ON push_notification_logs(status);

CREATE INDEX IF NOT EXISTS idx_discord_integrations_user_id ON discord_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_discord_integrations_is_active ON discord_integrations(is_active);

CREATE INDEX IF NOT EXISTS idx_discord_notification_logs_user_id ON discord_notification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_discord_notification_logs_status ON discord_notification_logs(status);

CREATE INDEX IF NOT EXISTS idx_file_uploads_uploaded_by ON file_uploads(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_file_uploads_content_type ON file_uploads(content_type);
CREATE INDEX IF NOT EXISTS idx_file_uploads_storage_provider ON file_uploads(storage_provider);
CREATE INDEX IF NOT EXISTS idx_file_uploads_is_public ON file_uploads(is_public);

CREATE INDEX IF NOT EXISTS idx_slack_integrations_user_id ON slack_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_slack_integrations_is_active ON slack_integrations(is_active);

CREATE INDEX IF NOT EXISTS idx_integration_preferences_user_id ON integration_preferences(user_id);

CREATE INDEX IF NOT EXISTS idx_api_usage_logs_user_id ON api_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_endpoint ON api_usage_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_created_at ON api_usage_logs(created_at);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_push_subscriptions_updated_at ON push_subscriptions;
CREATE TRIGGER update_push_subscriptions_updated_at BEFORE UPDATE ON push_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_discord_integrations_updated_at ON discord_integrations;
CREATE TRIGGER update_discord_integrations_updated_at BEFORE UPDATE ON discord_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_file_uploads_updated_at ON file_uploads;
CREATE TRIGGER update_file_uploads_updated_at BEFORE UPDATE ON file_uploads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_slack_integrations_updated_at ON slack_integrations;
CREATE TRIGGER update_slack_integrations_updated_at BEFORE UPDATE ON slack_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_integration_preferences_updated_at ON integration_preferences;
CREATE TRIGGER update_integration_preferences_updated_at BEFORE UPDATE ON integration_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_notification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE discord_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE discord_notification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE slack_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_preferences ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can manage their own push subscriptions" ON push_subscriptions FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their own push notification logs" ON push_notification_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their own Discord integrations" ON discord_integrations FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their own Discord notification logs" ON discord_notification_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their own files" ON file_uploads FOR ALL USING (auth.uid() = uploaded_by);
CREATE POLICY "Public files are viewable by all" ON file_uploads FOR SELECT USING (is_public = true);
CREATE POLICY "Users can manage their own Slack integrations" ON slack_integrations FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their own integration preferences" ON integration_preferences FOR ALL USING (auth.uid() = user_id);

-- Create functions for common operations
CREATE OR REPLACE FUNCTION get_user_storage_usage(user_uuid UUID)
RETURNS TABLE(
  total_files BIGINT,
  total_size BIGINT,
  by_type JSONB,
  by_provider JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::BIGINT as total_files,
    COALESCE(SUM(file_size), 0)::BIGINT as total_size,
    COALESCE(
      jsonb_object_agg(
        split_part(content_type, '/', 1),
        type_size
      ) FILTER (WHERE split_part(content_type, '/', 1) IS NOT NULL),
      '{}'::jsonb
    ) as by_type,
    COALESCE(
      jsonb_object_agg(
        storage_provider,
        provider_size
      ) FILTER (WHERE storage_provider IS NOT NULL),
      '{}'::jsonb
    ) as by_provider
  FROM (
    SELECT 
      content_type,
      storage_provider,
      SUM(file_size) as type_size,
      SUM(file_size) as provider_size
    FROM file_uploads 
    WHERE uploaded_by = user_uuid
    GROUP BY content_type, storage_provider
  ) stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean up old logs
CREATE OR REPLACE FUNCTION cleanup_old_logs(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
  temp_count INTEGER;
BEGIN
  -- Clean up old email logs
  DELETE FROM email_logs
  WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;

  -- Clean up old push notification logs
  DELETE FROM push_notification_logs
  WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;

  -- Clean up old Discord notification logs
  DELETE FROM discord_notification_logs
  WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;

  -- Clean up old API usage logs
  DELETE FROM api_usage_logs
  WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;

  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Table comments
COMMENT ON TABLE email_logs IS 'Logs of all email notifications sent through the platform';
COMMENT ON TABLE push_subscriptions IS 'User push notification subscriptions with VAPID keys';
COMMENT ON TABLE push_notification_logs IS 'Logs of all push notifications sent';
COMMENT ON TABLE discord_integrations IS 'User Discord webhook integrations for notifications';
COMMENT ON TABLE discord_notification_logs IS 'Logs of all Discord notifications sent';
COMMENT ON TABLE file_uploads IS 'Metadata for all files uploaded to the platform';
COMMENT ON TABLE slack_integrations IS 'User Slack integrations for team notifications';
COMMENT ON TABLE integration_preferences IS 'User preferences for all integration services';
COMMENT ON TABLE api_usage_logs IS 'API usage tracking for monitoring and rate limiting';
