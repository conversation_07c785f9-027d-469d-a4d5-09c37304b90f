-- Clean Learning Content Migration
-- Creates learning_content table with proper error handling

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create learning_content table
CREATE TABLE IF NOT EXISTS public.learning_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content_type VARCHAR(50) NOT NULL DEFAULT 'tutorial',
    content_body TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create updated_at function if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
        CREATE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $func$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $func$ language 'plpgsql';
    END IF;
END $$;

-- Create trigger for updated_at (drop first to avoid conflicts)
DO $$
BEGIN
    DROP TRIGGER IF EXISTS update_learning_content_updated_at ON learning_content;
    CREATE TRIGGER update_learning_content_updated_at 
        BEFORE UPDATE ON learning_content 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
END $$;

-- Enable RLS
ALTER TABLE learning_content ENABLE ROW LEVEL SECURITY;

-- Create policies (drop first to avoid conflicts)
DO $$
BEGIN
    DROP POLICY IF EXISTS "Anyone can read learning content" ON learning_content;
    CREATE POLICY "Anyone can read learning content" ON learning_content FOR SELECT USING (true);
    
    DROP POLICY IF EXISTS "Service role can manage content" ON learning_content;
    CREATE POLICY "Service role can manage content" ON learning_content FOR ALL USING (auth.role() = 'service_role');
END $$;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_learning_content_content_type ON learning_content(content_type);
CREATE INDEX IF NOT EXISTS idx_learning_content_created_at ON learning_content(created_at);

-- Insert real Royaltea content
INSERT INTO learning_content (title, description, content_type, content_body, difficulty_level) VALUES
(
    'Understanding Revenue Sharing in Royaltea',
    'Learn the fundamentals of how revenue sharing works in the Royaltea platform, including royalty models and payment distribution.',
    'tutorial',
    '# Understanding Revenue Sharing in Royaltea

## What is Revenue Sharing?

Revenue sharing in Royaltea is a transparent system that automatically distributes project earnings among contributors based on their contributions and agreed-upon terms. Unlike traditional employment models, revenue sharing creates true partnership opportunities where everyone benefits from project success.

## Core Concepts

### 1. Royalty Models
Royaltea supports multiple royalty distribution models:

- **Equal Split**: All contributors receive equal shares
- **Contribution-Based**: Shares based on time, effort, or value contributed  
- **Role-Based**: Different percentages for different roles (developer, designer, manager)
- **Hybrid Models**: Combination of the above approaches

### 2. Tranche System
Projects can be divided into tranches (phases) with different revenue sharing rules:

- **Development Tranche**: Revenue from initial development work
- **Maintenance Tranche**: Ongoing revenue from updates and support
- **Growth Tranche**: Revenue from scaling and new features
- **Legacy Tranche**: Long-term passive revenue

### 3. Contribution Points
The platform tracks contribution through various metrics:

- **Time Tracking**: Hours worked on specific tasks
- **Deliverable Completion**: Finished features, designs, or content
- **Quality Metrics**: Code reviews, testing, documentation
- **Leadership Activities**: Project management, team coordination

## How It Works

### Step 1: Project Setup
When creating a project, the project owner defines:
- Revenue sharing model
- Contributor roles and percentages
- Tranche structure
- Payment thresholds

### Step 2: Contributor Onboarding
New contributors:
- Review and sign revenue sharing agreements
- Understand their role and expected contribution
- Set up payment preferences
- Begin tracking their work

### Step 3: Revenue Tracking
The platform automatically:
- Monitors project revenue from connected accounts
- Tracks individual contributions
- Calculates distribution amounts
- Handles tax documentation

### Step 4: Distribution
Revenue is distributed:
- Automatically when thresholds are met
- Manually by project administrators
- According to predefined schedules
- With full transparency and audit trails

## Benefits

### For Project Owners
- **Attract Top Talent**: Offer equity-like participation without giving up ownership
- **Align Incentives**: Contributors are motivated by project success
- **Reduce Upfront Costs**: Pay contributors from revenue rather than upfront
- **Scale Efficiently**: Add contributors without fixed salary commitments

### For Contributors
- **Unlimited Earning Potential**: No salary cap - earn based on project success
- **Portfolio Diversification**: Work on multiple revenue-generating projects
- **Skill Development**: Learn while earning from real projects
- **Network Building**: Connect with other professionals and entrepreneurs

## Getting Started

Ready to implement revenue sharing in your project? Here''s how to begin:

1. **Define Your Model**: Choose the revenue sharing approach that fits your project
2. **Set Up Your Project**: Create your project in Royaltea with revenue sharing enabled
3. **Invite Contributors**: Send invitations with clear revenue sharing terms
4. **Connect Revenue Sources**: Link your payment processors and revenue streams
5. **Start Tracking**: Begin monitoring contributions and revenue
6. **Distribute Earnings**: Set up automatic or manual distribution schedules

This comprehensive system ensures fair compensation while maintaining transparency and trust among all project contributors.',
    'beginner'
),
(
    'Project Management Best Practices in Royaltea',
    'Master project management within the Royaltea ecosystem, from team coordination to milestone tracking and revenue optimization.',
    'tutorial',
    '# Project Management Best Practices in Royaltea

## Introduction

Effective project management is crucial for success in the Royaltea ecosystem. This guide covers best practices for managing projects, teams, and revenue-sharing arrangements to maximize both productivity and profitability.

## Project Lifecycle Management

### 1. Project Initiation
**Define Clear Objectives**
- Set specific, measurable goals
- Identify target audience and market
- Establish success metrics
- Create project charter

**Team Assembly**
- Identify required skills and roles
- Recruit contributors through Royaltea network
- Define revenue sharing agreements
- Establish communication protocols

### 2. Planning Phase
**Scope Definition**
- Break down project into manageable tasks
- Create work breakdown structure (WBS)
- Estimate time and resource requirements
- Identify dependencies and risks

**Timeline Creation**
- Set realistic milestones and deadlines
- Build in buffer time for unexpected challenges
- Align timeline with revenue projections
- Communicate schedule to all team members

### 3. Execution and Monitoring
**Task Management**
- Use Royaltea''s built-in project tracking tools
- Assign tasks with clear deliverables
- Monitor progress against milestones
- Conduct regular team check-ins

**Quality Assurance**
- Implement code review processes
- Test deliverables before milestone completion
- Gather feedback from stakeholders
- Maintain documentation standards

### 4. Project Closure
**Final Deliverables**
- Complete all outstanding tasks
- Conduct final quality review
- Deploy or launch the project
- Document lessons learned

**Revenue Transition**
- Transition from development to revenue generation
- Set up ongoing maintenance and support
- Implement revenue tracking and distribution
- Plan for future enhancements

## Team Coordination Strategies

### Communication Best Practices
**Regular Meetings**
- Weekly team standups (15-30 minutes)
- Monthly progress reviews
- Quarterly strategic planning sessions
- Ad-hoc problem-solving meetings

**Documentation Standards**
- Maintain project wiki or knowledge base
- Document decisions and rationale
- Keep meeting notes and action items
- Create user guides and technical documentation

### Conflict Resolution
**Common Issues**
- Disagreements over revenue distribution
- Scope creep and changing requirements
- Performance and contribution concerns
- Communication breakdowns

**Resolution Strategies**
- Address issues early and directly
- Use data to support discussions
- Involve neutral mediators when needed
- Document agreements and changes

## Revenue-Focused Management

### Milestone-Based Revenue Planning
**Revenue Milestones**
- Link project phases to revenue generation
- Set up early revenue streams when possible
- Plan for multiple revenue sources
- Track revenue against projections

**Contributor Motivation**
- Tie milestone completion to revenue sharing
- Provide regular updates on project revenue
- Celebrate achievements and successes
- Maintain transparency in financial matters

## Success Metrics

### Project Success Indicators
**Financial Metrics**
- Revenue generation vs. projections
- Profit margins and cost efficiency
- Return on investment (ROI)
- Revenue growth rate

**Operational Metrics**
- On-time delivery rate
- Quality metrics and defect rates
- Team productivity and efficiency
- Customer satisfaction scores

## Getting Started Checklist

### Project Setup
- Define project vision and objectives
- Identify required skills and team size
- Create revenue sharing agreement template
- Set up project workspace in Royaltea
- Establish communication protocols

### Team Building
- Post project opportunity on Royaltea
- Interview and select contributors
- Onboard team members with agreements
- Conduct team kickoff meeting
- Set up collaboration tools and access

### Project Execution
- Create detailed project plan and timeline
- Set up milestone tracking and reporting
- Implement regular team meetings
- Establish quality assurance processes
- Monitor progress and adjust as needed

Ready to apply these project management practices? Start with assessing your current approach, implement key practices, build your team through Royaltea, set up tracking systems, and continuously iterate and improve based on results.',
    'intermediate'
)
ON CONFLICT (title) DO NOTHING;

-- Add a comment
COMMENT ON TABLE learning_content IS 'Learning content for the Royaltea platform including tutorials, guides, and educational materials';
