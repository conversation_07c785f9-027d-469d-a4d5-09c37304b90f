/**
 * Simple Project Detail Debug Test
 * 
 * A simplified test to debug the project detail page issue
 */

import { test, expect } from '@playwright/test';

test.describe('Simple Project Detail Debug', () => {
  test('should check project detail page rendering', async ({ page }) => {
    console.log('🔍 Starting simple project detail debug...');

    // Navigate to the site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    // Check if we're logged in
    const isLoggedIn = await page.locator('[data-testid="user-profile"], .user-profile, [aria-label*="profile"], [aria-label*="Profile"]').isVisible();
    
    if (!isLoggedIn) {
      console.log('🔐 Need to log in...');
      
      // Fill login form
      await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
      await page.fill('input[type="password"], input[name="password"]', 'TestPassword123!');
      
      // Submit login
      await page.click('button[type="submit"], button:has-text("Sign In"), button:has-text("Log In")');
      await page.waitForLoadState('networkidle');
      
      console.log('✅ Logged in successfully');
    }

    // Try to navigate directly to a project that should exist
    // First, let's try to create a project to ensure we have one
    console.log('🚀 Trying to create a project first...');
    
    await page.goto('https://royalty.technology/start');
    await page.waitForLoadState('networkidle');
    
    // Look for create project button
    const createProjectButton = await page.locator('button:has-text("Create Project"), a:has-text("Create Project"), [data-testid="create-project"]').first();
    
    if (await createProjectButton.isVisible()) {
      console.log('📝 Found create project button, clicking...');
      await createProjectButton.click();
      await page.waitForLoadState('networkidle');
      
      // Check if we're in the project wizard
      const isInWizard = await page.locator('.project-wizard, .studio-selection, h1:has-text("Studio Selection")').isVisible();
      console.log(`📋 In project wizard: ${isInWizard}`);
      
      if (isInWizard) {
        // Try to complete a minimal project creation
        console.log('🏗️ Attempting to complete project creation...');
        
        // Take a screenshot of the current state
        await page.screenshot({ path: 'test-results/simple-debug-01-wizard-state.png', fullPage: true });
        
        // Check what's on the page
        const pageContent = await page.locator('body').textContent();
        console.log('📄 Page content preview:', pageContent.substring(0, 500));
      }
    }

    // Now try to navigate to projects list to see if any projects exist
    console.log('📋 Checking projects list...');
    await page.goto('https://royalty.technology/projects');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/simple-debug-02-projects-list.png', fullPage: true });
    
    // Check for project links
    const projectLinks = await page.locator('a[href*="/project/"]').all();
    console.log(`Found ${projectLinks.length} project links`);
    
    if (projectLinks.length > 0) {
      // Get the first project URL
      const firstProjectUrl = await projectLinks[0].getAttribute('href');
      console.log(`🔗 First project URL: ${firstProjectUrl}`);
      
      // Navigate to the project detail page
      console.log('🎯 Navigating to project detail page...');
      await page.goto(`https://royalty.technology${firstProjectUrl}`);
      await page.waitForLoadState('networkidle');
      
      // Wait a bit for any dynamic content
      await page.waitForTimeout(3000);
      
      await page.screenshot({ path: 'test-results/simple-debug-03-project-detail.png', fullPage: true });
      
      // Check what's actually rendered
      const bodyHTML = await page.locator('body').innerHTML();
      console.log('📄 Body HTML length:', bodyHTML.length);
      
      // Check for specific elements
      const hasProjectContainer = await page.locator('.project-detail-container').isVisible();
      const hasProjectHeader = await page.locator('.project-header').isVisible();
      const hasProjectTitle = await page.locator('.project-title, h1').isVisible();
      
      console.log(`✅ Project container: ${hasProjectContainer}`);
      console.log(`✅ Project header: ${hasProjectHeader}`);
      console.log(`✅ Project title: ${hasProjectTitle}`);
      
      // Check for any error messages
      const hasError = await page.locator('.error, .alert-danger, [role="alert"]').isVisible();
      console.log(`❌ Error visible: ${hasError}`);
      
      // Check the actual text content
      const textContent = await page.locator('body').textContent();
      console.log('📝 Text content preview:', textContent.substring(0, 1000));
      
      // Check if it looks like the raw data display from the screenshot
      const hasRawDataPattern = textContent.includes('Project Details') && 
                               textContent.includes('Start Date:') && 
                               textContent.includes('Launch Date:') &&
                               textContent.includes('Estimated Duration:');
      
      console.log(`🔍 Looks like raw data display: ${hasRawDataPattern}`);
      
      if (hasRawDataPattern) {
        console.log('🚨 FOUND THE ISSUE: Page is showing raw data instead of styled component');
        
        // Check for CSS loading issues
        const stylesheets = await page.locator('link[rel="stylesheet"]').all();
        console.log(`📄 Found ${stylesheets.length} stylesheets`);
        
        // Check for JavaScript errors
        const jsErrors = [];
        page.on('pageerror', error => {
          jsErrors.push(error.message);
        });
        
        console.log(`🐛 JavaScript errors: ${jsErrors.length > 0 ? jsErrors.join(', ') : 'None'}`);
      }
      
    } else {
      console.log('❌ No projects found in the list');
    }

    console.log('🎉 Simple project detail debug completed');
  });
});
