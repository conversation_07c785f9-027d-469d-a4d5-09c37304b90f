// Fix Database Restore - Skip problematic migrations and continue with core ones
// This script will help us get the database back to a working state

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createCoreTables() {
  console.log('🔧 Creating core tables that are missing...\n');

  const coreTablesSQL = `
-- Create core tables if they don't exist
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled', 'archived')),
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    project_type TEXT DEFAULT 'software',
    team_id UUID,
    studio_id UUID,
    is_public BOOLEAN DEFAULT false,
    budget DECIMAL(12,2),
    start_date DATE,
    end_date DATE
);

CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'review', 'done', 'cancelled')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    assignee_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    due_date TIMESTAMP WITH TIME ZONE,
    estimated_hours INTEGER,
    logged_hours INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS public.collaboration_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID REFERENCES auth.users(id) NOT NULL,
    target_user_id UUID REFERENCES auth.users(id),
    project_title VARCHAR(255) NOT NULL,
    project_description TEXT NOT NULL,
    required_skills TEXT[],
    budget_range_min INTEGER,
    budget_range_max INTEGER,
    timeline_weeks INTEGER,
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.collaboration_request_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    collaboration_request_id UUID REFERENCES public.collaboration_requests(id) ON DELETE CASCADE,
    applicant_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    application_message TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(collaboration_request_id, applicant_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON public.projects(created_by);
CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_assignee_id ON public.tasks(assignee_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_requester_id ON public.collaboration_requests(requester_id);

-- Enable RLS
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_request_applications ENABLE ROW LEVEL SECURITY;

-- Basic policies
DO $$ BEGIN
  -- Projects policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'projects' AND policyname = 'Users can view public projects') THEN
    CREATE POLICY "Users can view public projects" ON public.projects FOR SELECT USING (is_public = true);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'projects' AND policyname = 'Users can manage their own projects') THEN
    CREATE POLICY "Users can manage their own projects" ON public.projects FOR ALL USING (created_by = auth.uid());
  END IF;

  -- Tasks policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'tasks' AND policyname = 'Users can view tasks in accessible projects') THEN
    CREATE POLICY "Users can view tasks in accessible projects" ON public.tasks FOR SELECT USING (
      project_id IN (
        SELECT id FROM public.projects 
        WHERE created_by = auth.uid() OR is_public = true
      )
    );
  END IF;

  -- Collaboration requests policies
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'collaboration_requests' AND policyname = 'Users can manage their own requests') THEN
    CREATE POLICY "Users can manage their own requests" ON public.collaboration_requests FOR ALL USING (requester_id = auth.uid());
  END IF;

EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE 'Some policies may already exist, continuing...';
END $$;
`;

  try {
    const { error } = await supabase.rpc('exec_sql', { sql: coreTablesSQL });
    if (error) {
      console.error('❌ Error creating core tables:', error);
      return false;
    }
    console.log('✅ Core tables created successfully');
    return true;
  } catch (error) {
    console.error('❌ Error:', error);
    return false;
  }
}

async function checkDatabaseStatus() {
  console.log('🔍 Checking database status...\n');
  
  try {
    // Check what tables exist
    const { data: tables, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['projects', 'tasks', 'collaboration_requests', 'learning_content']);

    if (error) {
      console.error('❌ Error checking tables:', error);
      return;
    }

    console.log('📋 Existing tables:');
    tables.forEach(table => {
      console.log(`  ✓ ${table.table_name}`);
    });

    // Check learning content
    const { data: content, error: contentError } = await supabase
      .from('learning_content')
      .select('id, title')
      .limit(5);

    if (!contentError && content) {
      console.log(`\n📚 Learning content: ${content.length} items found`);
      content.forEach(item => {
        console.log(`  • ${item.title}`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking database:', error);
  }
}

async function main() {
  console.log('🚀 Starting Database Recovery Process...\n');
  
  // First create core tables
  const coreTablesCreated = await createCoreTables();
  
  if (coreTablesCreated) {
    console.log('\n✅ Core tables are ready');
    console.log('📝 You can now continue with the migration push');
    console.log('💡 Run: npx supabase db push --linked');
  } else {
    console.log('\n❌ Failed to create core tables');
    console.log('🔧 You may need to manually fix the database');
  }

  // Check final status
  await checkDatabaseStatus();
}

main().catch(console.error);
