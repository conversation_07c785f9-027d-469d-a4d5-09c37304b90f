-- Add screenshots column to bug_reports table
-- This will store an array of screenshot objects with url, name, and path

-- Add screenshots column to store screenshot metadata
ALTER TABLE public.bug_reports
ADD COLUMN IF NOT EXISTS screenshots <PERSON><PERSON><PERSON><PERSON> DEFAULT '[]'::jsonb;

-- Add comment for the new column
COMMENT ON COLUMN public.bug_reports.screenshots IS 'Array of screenshot objects with url, name, and path properties';

-- Create storage bucket for bug attachments if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('bug-attachments', 'bug-attachments', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for bug attachments bucket
-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload bug attachments"
ON storage.objects
FOR INSERT
WITH CHECK (
    bucket_id = 'bug-attachments' 
    AND auth.role() = 'authenticated'
);

-- Allow public read access to bug attachments
CREATE POLICY "Public read access to bug attachments"
ON storage.objects
FOR SELECT
USING (bucket_id = 'bug-attachments');

-- Allow users to delete their own uploaded attachments
CREATE POLICY "Users can delete their own bug attachments"
ON storage.objects
FOR DELETE
USING (
    bucket_id = 'bug-attachments' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow admins to delete any bug attachments
CREATE POLICY "Admins can delete any bug attachments"
ON storage.objects
FOR DELETE
USING (
    bucket_id = 'bug-attachments'
    AND auth.uid() IS NOT NULL
);
