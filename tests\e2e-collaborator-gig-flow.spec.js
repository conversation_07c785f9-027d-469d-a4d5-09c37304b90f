import { test, expect } from '@playwright/test';

// Test credentials
const PROJECT_OWNER = {
  email: '<EMAIL>',
  password: 'ProjectOwner123!'
};

const FREELANCER = {
  email: '<EMAIL>',
  password: 'Freelancer123!'
};

test.describe('Find Collaborators → Accept Gig Applications Flow', () => {
  test('Complete Collaboration and Gig Management Flow', async ({ browser }) => {
    // Create two browser contexts for different users
    const ownerContext = await browser.newContext();
    const freelancerContext = await browser.newContext();
    
    const ownerPage = await ownerContext.newPage();
    const freelancerPage = await freelancerContext.newPage();

    try {
      // PART 1: PROJECT OWNER - Find Collaborators
      console.log('🔍 Step 1: Project owner searches for collaborators');
      
      // Login as project owner
      await ownerPage.goto('/login');
      await ownerPage.fill('input[type="email"]', PROJECT_OWNER.email);
      await ownerPage.fill('input[type="password"]', PROJECT_OWNER.password);
      await ownerPage.click('button[type="submit"]');
      await ownerPage.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });

      // Navigate to Social/Collaborators page
      await ownerPage.goto('/social');
      await ownerPage.waitForLoadState('networkidle');
      
      // Verify social page loads
      await expect(ownerPage.locator('h1:has-text("Social")')).toBeVisible();

      // Search for collaborators
      const searchInput = ownerPage.locator('input[placeholder*="Search"]');
      if (await searchInput.isVisible()) {
        await searchInput.fill('React developer');
        await ownerPage.waitForTimeout(2000);
        
        console.log('✅ Collaborator search initiated');
      }

      // Filter by skills
      const skillFilter = ownerPage.locator('select[label*="Skills"]');
      if (await skillFilter.isVisible()) {
        await skillFilter.selectOption('React');
        await ownerPage.waitForTimeout(1000);
      }

      // Filter by experience level
      const experienceFilter = ownerPage.locator('select[label*="Experience"]');
      if (await experienceFilter.isVisible()) {
        await experienceFilter.selectOption('intermediate');
        await ownerPage.waitForTimeout(1000);
      }

      // Browse developer profiles
      const profileCards = ownerPage.locator('[data-testid="profile-card"]');
      const profileCount = await profileCards.count();
      
      if (profileCount > 0) {
        console.log(`✅ Found ${profileCount} developer profiles`);
        
        // View detailed profile
        const viewProfileButton = profileCards.first().locator('button:has-text("View Profile")');
        if (await viewProfileButton.isVisible()) {
          await viewProfileButton.click();
          await ownerPage.waitForTimeout(1000);
          
          // Check profile modal/page
          const profileModal = ownerPage.locator('[role="dialog"]:has-text("Profile")');
          const profilePage = ownerPage.locator('h1:has-text("Profile")');
          
          if (await profileModal.isVisible()) {
            // Verify profile information
            await expect(ownerPage.locator('text=Skills')).toBeVisible();
            await expect(ownerPage.locator('text=Experience')).toBeVisible();
            await expect(ownerPage.locator('text=Verification')).toBeVisible();
            
            // Check verification badges
            const verificationBadges = ownerPage.locator('[data-testid="verification-badge"]');
            const badgeCount = await verificationBadges.count();
            if (badgeCount > 0) {
              console.log(`✅ Found ${badgeCount} verification badges`);
            }
            
            await ownerPage.click('button:has-text("Close")');
          } else if (await profilePage.isVisible()) {
            // Navigate back if on profile page
            await ownerPage.goBack();
          }
        }

        // Send collaboration request
        const collaborateButton = profileCards.first().locator('button:has-text("Collaborate")');
        if (await collaborateButton.isVisible()) {
          await collaborateButton.click();
          await ownerPage.waitForTimeout(1000);
          
          // Fill collaboration request form
          const requestModal = ownerPage.locator('[role="dialog"]:has-text("Collaboration Request")');
          if (await requestModal.isVisible()) {
            await ownerPage.fill('input[label*="Project Title"]', 'E2E Test Collaboration Project');
            await ownerPage.fill('textarea[label*="Description"]', 'Looking for a React developer to help with frontend development');
            await ownerPage.selectOption('select[label*="Project Type"]', 'web_development');
            await ownerPage.fill('input[label*="Budget"]', '2000');
            await ownerPage.selectOption('select[label*="Duration"]', '1-3 months');
            
            // Select required skills
            const skillChips = ownerPage.locator('[data-testid="skill-chip"]');
            const skillCount = await skillChips.count();
            if (skillCount > 0) {
              await skillChips.first().click();
              if (skillCount > 1) await skillChips.nth(1).click();
            }
            
            await ownerPage.click('button:has-text("Send Request")');
            await ownerPage.waitForTimeout(2000);
            
            // Verify request sent
            const requestSent = ownerPage.locator('text=Request sent').or(ownerPage.locator('text=sent successfully'));
            if (await requestSent.isVisible()) {
              console.log('✅ Collaboration request sent');
            }
            
            // Close modal if still open
            const closeButton = ownerPage.locator('button:has-text("Close")');
            if (await closeButton.isVisible()) {
              await closeButton.click();
            }
          }
        }
      }

      // PART 2: CREATE GIG POSTING
      console.log('📝 Step 2: Project owner creates gig posting');
      
      // Navigate to marketplace or gig creation
      await ownerPage.goto('/marketplace');
      await ownerPage.waitForLoadState('networkidle');

      // Create new gig posting
      const createGigButton = ownerPage.locator('button:has-text("Post Gig")').or(ownerPage.locator('button:has-text("Create Gig")'));
      if (await createGigButton.isVisible()) {
        await createGigButton.click();
        await ownerPage.waitForTimeout(1000);
        
        // Fill gig creation form
        await ownerPage.fill('input[label*="Gig Title"]', 'Frontend Developer Needed - React Project');
        await ownerPage.fill('textarea[label*="Description"]', 'Looking for an experienced React developer to build responsive web components');
        await ownerPage.selectOption('select[label*="Category"]', 'web_development');
        await ownerPage.fill('input[label*="Budget"]', '1500');
        await ownerPage.selectOption('select[label*="Experience Level"]', 'intermediate');
        
        // Set project duration
        await ownerPage.fill('input[label*="Duration"]', '4');
        await ownerPage.selectOption('select[label*="Duration Unit"]', 'weeks');
        
        // Add required skills
        const gigSkillChips = ownerPage.locator('[data-testid="skill-chip"]');
        const gigSkillCount = await gigSkillChips.count();
        if (gigSkillCount > 0) {
          await gigSkillChips.first().click(); // React
          if (gigSkillCount > 1) await gigSkillChips.nth(1).click(); // JavaScript
        }
        
        // Set application deadline
        const deadlineInput = ownerPage.locator('input[type="date"]');
        if (await deadlineInput.isVisible()) {
          const futureDate = new Date();
          futureDate.setDate(futureDate.getDate() + 14);
          await deadlineInput.fill(futureDate.toISOString().split('T')[0]);
        }
        
        await ownerPage.click('button:has-text("Post Gig")');
        await ownerPage.waitForTimeout(3000);
        
        // Verify gig posted
        const gigPosted = ownerPage.locator('text=Gig posted').or(ownerPage.locator('text=posted successfully'));
        if (await gigPosted.isVisible()) {
          console.log('✅ Gig posted successfully');
        }
      }

      // PART 3: FREELANCER - Browse and Apply to Gigs
      console.log('💼 Step 3: Freelancer browses and applies to gigs');
      
      // Login as freelancer
      await freelancerPage.goto('/login');
      await freelancerPage.fill('input[type="email"]', FREELANCER.email);
      await freelancerPage.fill('input[type="password"]', FREELANCER.password);
      await freelancerPage.click('button[type="submit"]');
      await freelancerPage.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });

      // Navigate to marketplace
      await freelancerPage.goto('/marketplace');
      await freelancerPage.waitForLoadState('networkidle');
      
      // Verify marketplace loads
      await expect(freelancerPage.locator('h1:has-text("Marketplace")')).toBeVisible();

      // Browse available gigs
      const gigCards = freelancerPage.locator('[data-testid="gig-card"]');
      const gigCount = await gigCards.count();
      
      if (gigCount > 0) {
        console.log(`✅ Found ${gigCount} available gigs`);
        
        // Filter gigs by category
        const categoryFilter = freelancerPage.locator('select[label*="Category"]');
        if (await categoryFilter.isVisible()) {
          await categoryFilter.selectOption('web_development');
          await freelancerPage.waitForTimeout(1000);
        }
        
        // Filter by budget range
        const budgetFilter = freelancerPage.locator('select[label*="Budget"]');
        if (await budgetFilter.isVisible()) {
          await budgetFilter.selectOption('1000-5000');
          await freelancerPage.waitForTimeout(1000);
        }
        
        // View gig details
        const viewGigButton = gigCards.first().locator('button:has-text("View Details")');
        if (await viewGigButton.isVisible()) {
          await viewGigButton.click();
          await freelancerPage.waitForTimeout(1000);
          
          // Check gig details modal
          const gigModal = freelancerPage.locator('[role="dialog"]:has-text("Gig Details")');
          if (await gigModal.isVisible()) {
            // Verify gig information
            await expect(freelancerPage.locator('text=Description')).toBeVisible();
            await expect(freelancerPage.locator('text=Budget')).toBeVisible();
            await expect(freelancerPage.locator('text=Required Skills')).toBeVisible();
            
            // Check skill match indicator
            const skillMatch = freelancerPage.locator('[data-testid="skill-match"]');
            if (await skillMatch.isVisible()) {
              console.log('✅ Skill match indicator displayed');
            }
            
            await freelancerPage.click('button:has-text("Close")');
          }
        }
        
        // Apply to gig
        const applyButton = gigCards.first().locator('button:has-text("Apply")');
        if (await applyButton.isVisible()) {
          await applyButton.click();
          await freelancerPage.waitForTimeout(1000);
          
          // Fill application form
          const applicationModal = freelancerPage.locator('[role="dialog"]:has-text("Apply")');
          if (await applicationModal.isVisible()) {
            // Step 1: Basic application info
            await freelancerPage.fill('textarea[label*="Cover Letter"]', 'I am an experienced React developer with 3+ years of experience building responsive web applications. I would love to work on this project.');
            await freelancerPage.fill('input[label*="Proposed Rate"]', '75');
            await freelancerPage.fill('input[label*="Estimated Hours"]', '20');
            
            await freelancerPage.click('button:has-text("Next")');
            await freelancerPage.waitForTimeout(1000);
            
            // Step 2: Portfolio and experience
            const portfolioUrl = freelancerPage.locator('input[label*="Portfolio"]');
            if (await portfolioUrl.isVisible()) {
              await portfolioUrl.fill('https://github.com/freelancer/portfolio');
            }
            
            const experienceTextarea = freelancerPage.locator('textarea[label*="Relevant Experience"]');
            if (await experienceTextarea.isVisible()) {
              await experienceTextarea.fill('Built multiple React applications including e-commerce platforms and dashboard interfaces');
            }
            
            await freelancerPage.click('button:has-text("Next")');
            await freelancerPage.waitForTimeout(1000);
            
            // Step 3: Final submission
            const availabilitySelect = freelancerPage.locator('select[label*="Availability"]');
            if (await availabilitySelect.isVisible()) {
              await availabilitySelect.selectOption('immediately');
            }
            
            const questionsTextarea = freelancerPage.locator('textarea[label*="Questions"]');
            if (await questionsTextarea.isVisible()) {
              await questionsTextarea.fill('What is the expected timeline for the project milestones?');
            }
            
            await freelancerPage.click('button:has-text("Submit Application")');
            await freelancerPage.waitForTimeout(3000);
            
            // Verify application submitted
            const applicationSubmitted = freelancerPage.locator('text=Application submitted').or(freelancerPage.locator('text=submitted successfully'));
            if (await applicationSubmitted.isVisible()) {
              console.log('✅ Gig application submitted successfully');
            }
          }
        }
      }

      // PART 4: PROJECT OWNER - Review and Accept Applications
      console.log('✅ Step 4: Project owner reviews and accepts applications');
      
      // Switch back to owner page
      await ownerPage.goto('/marketplace');
      await ownerPage.waitForLoadState('networkidle');
      
      // Navigate to manage gigs or applications
      const manageGigsButton = ownerPage.locator('button:has-text("Manage Gigs")').or(ownerPage.locator('button:has-text("My Gigs")'));
      if (await manageGigsButton.isVisible()) {
        await manageGigsButton.click();
        await ownerPage.waitForTimeout(1000);
      }
      
      // View applications for posted gig
      const viewApplicationsButton = ownerPage.locator('button:has-text("View Applications")');
      if (await viewApplicationsButton.isVisible()) {
        await viewApplicationsButton.click();
        await ownerPage.waitForTimeout(1000);
        
        // Check applications list
        const applicationCards = ownerPage.locator('[data-testid="application-card"]');
        const applicationCount = await applicationCards.count();
        
        if (applicationCount > 0) {
          console.log(`✅ Found ${applicationCount} applications`);
          
          // Review first application
          const reviewButton = applicationCards.first().locator('button:has-text("Review")');
          if (await reviewButton.isVisible()) {
            await reviewButton.click();
            await ownerPage.waitForTimeout(1000);
            
            // Check application review modal
            const reviewModal = ownerPage.locator('[role="dialog"]:has-text("Review Application")');
            if (await reviewModal.isVisible()) {
              // Verify application details
              await expect(ownerPage.locator('text=Cover Letter')).toBeVisible();
              await expect(ownerPage.locator('text=Proposed Rate')).toBeVisible();
              await expect(ownerPage.locator('text=Portfolio')).toBeVisible();
              
              // Check freelancer profile integration
              const viewProfileButton = ownerPage.locator('button:has-text("View Profile")');
              if (await viewProfileButton.isVisible()) {
                await viewProfileButton.click();
                await ownerPage.waitForTimeout(1000);
                
                // Verify profile information loads
                const profileSection = ownerPage.locator('[data-testid="freelancer-profile"]');
                if (await profileSection.isVisible()) {
                  console.log('✅ Freelancer profile loaded in review');
                }
              }
              
              // Accept the application
              const acceptButton = ownerPage.locator('button:has-text("Accept")');
              if (await acceptButton.isVisible()) {
                await acceptButton.click();
                await ownerPage.waitForTimeout(1000);
                
                // Fill acceptance details
                const acceptanceModal = ownerPage.locator('[role="dialog"]:has-text("Accept Application")');
                if (await acceptanceModal.isVisible()) {
                  await ownerPage.fill('textarea[label*="Message"]', 'Congratulations! Your application has been accepted. Looking forward to working with you.');
                  
                  // Set project start date
                  const startDateInput = ownerPage.locator('input[type="date"]');
                  if (await startDateInput.isVisible()) {
                    const startDate = new Date();
                    startDate.setDate(startDate.getDate() + 3);
                    await startDateInput.fill(startDate.toISOString().split('T')[0]);
                  }
                  
                  await ownerPage.click('button:has-text("Confirm Accept")');
                  await ownerPage.waitForTimeout(2000);
                  
                  // Verify acceptance
                  const acceptanceConfirmed = ownerPage.locator('text=Application accepted').or(ownerPage.locator('text=accepted successfully'));
                  if (await acceptanceConfirmed.isVisible()) {
                    console.log('✅ Application accepted successfully');
                  }
                }
              }
            }
          }
        }
      }

      // PART 5: FREELANCER - Check Application Status
      console.log('📬 Step 5: Freelancer checks application status');
      
      // Switch to freelancer page
      await freelancerPage.goto('/marketplace');
      await freelancerPage.waitForLoadState('networkidle');
      
      // Navigate to my applications
      const myApplicationsButton = freelancerPage.locator('button:has-text("My Applications")');
      if (await myApplicationsButton.isVisible()) {
        await myApplicationsButton.click();
        await freelancerPage.waitForTimeout(1000);
        
        // Check application status
        const applicationStatusCards = freelancerPage.locator('[data-testid="application-status-card"]');
        const statusCount = await applicationStatusCards.count();
        
        if (statusCount > 0) {
          console.log(`✅ Found ${statusCount} application status updates`);
          
          // Check for accepted status
          const acceptedStatus = freelancerPage.locator('text=Accepted').or(freelancerPage.locator('[data-testid="status-accepted"]'));
          if (await acceptedStatus.isVisible()) {
            console.log('✅ Application acceptance status confirmed');
          }
          
          // View project details for accepted application
          const viewProjectButton = applicationStatusCards.first().locator('button:has-text("View Project")');
          if (await viewProjectButton.isVisible()) {
            await viewProjectButton.click();
            await freelancerPage.waitForTimeout(1000);
            
            // Verify project workspace access
            const projectWorkspace = freelancerPage.locator('[data-testid="project-workspace"]');
            if (await projectWorkspace.isVisible()) {
              console.log('✅ Project workspace access granted');
            }
          }
        }
      }

      console.log('🎉 Complete Collaborator → Gig Application → Acceptance flow test PASSED');

    } finally {
      // Cleanup
      await ownerContext.close();
      await freelancerContext.close();
    }
  });

  test('Collaboration Request Management', async ({ page }) => {
    // Login as project owner
    await page.goto('/login');
    await page.fill('input[type="email"]', PROJECT_OWNER.email);
    await page.fill('input[type="password"]', PROJECT_OWNER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });

    await page.goto('/social');
    await page.waitForLoadState('networkidle');

    // Test collaboration request management
    const requestsTab = page.locator('[role="tab"]:has-text("Requests")');
    if (await requestsTab.isVisible()) {
      await requestsTab.click();
      await page.waitForTimeout(1000);
      
      // Check sent requests
      const sentRequestsTab = page.locator('[role="tab"]:has-text("Sent")');
      if (await sentRequestsTab.isVisible()) {
        await sentRequestsTab.click();
        await page.waitForTimeout(1000);
        
        const sentRequests = page.locator('[data-testid="sent-request"]');
        const sentCount = await sentRequests.count();
        console.log(`Found ${sentCount} sent collaboration requests`);
      }
      
      // Check received requests
      const receivedRequestsTab = page.locator('[role="tab"]:has-text("Received")');
      if (await receivedRequestsTab.isVisible()) {
        await receivedRequestsTab.click();
        await page.waitForTimeout(1000);
        
        const receivedRequests = page.locator('[data-testid="received-request"]');
        const receivedCount = await receivedRequests.count();
        console.log(`Found ${receivedCount} received collaboration requests`);
        
        if (receivedCount > 0) {
          // Test accepting a collaboration request
          const acceptButton = receivedRequests.first().locator('button:has-text("Accept")');
          if (await acceptButton.isVisible()) {
            await acceptButton.click();
            await page.waitForTimeout(1000);
            
            const acceptModal = page.locator('[role="dialog"]:has-text("Accept Collaboration")');
            if (await acceptModal.isVisible()) {
              await page.fill('textarea[label*="Message"]', 'I would be happy to collaborate on this project.');
              await page.click('button:has-text("Confirm")');
              await page.waitForTimeout(2000);
            }
          }
        }
      }
    }

    console.log('✅ Collaboration request management test passed');
  });
});
