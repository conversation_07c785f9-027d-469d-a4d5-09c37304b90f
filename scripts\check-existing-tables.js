// Check Existing Tables - See what's already in the database
import { createClient } from '@supabase/supabase-js';

// Database configuration
const SUPABASE_URL = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function checkExistingTables() {
  console.log('🔍 Checking Existing Database Tables...\n');

  try {
    // Try to query some common tables to see what exists
    const tablesToCheck = [
      'learning_content',
      'vetting_applications', 
      'vetting_criteria',
      'email_logs',
      'push_subscriptions',
      'projects',
      'users',
      'profiles',
      'studios',
      'tasks',
      'missions'
    ];

    console.log('📋 Table Existence Check:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    for (const tableName of tablesToCheck) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);

        if (error) {
          if (error.code === '42P01') {
            console.log(`❌ ${tableName}: Does not exist`);
          } else {
            console.log(`⚠️ ${tableName}: Error - ${error.message}`);
          }
        } else {
          console.log(`✅ ${tableName}: Exists (${data.length} sample records)`);
        }
      } catch (err) {
        console.log(`❌ ${tableName}: Error - ${err.message}`);
      }
    }

    // Try to get schema information
    console.log('\n🗂️ Attempting to get schema information...');
    
    try {
      // This might work if we have the right permissions
      const { data: schemaData, error: schemaError } = await supabase
        .rpc('get_schema_info');
        
      if (schemaError) {
        console.log('⚠️ Cannot access schema info:', schemaError.message);
      } else {
        console.log('✅ Schema info retrieved:', schemaData);
      }
    } catch (err) {
      console.log('⚠️ Schema info not available');
    }

    // Check if we can create a simple test table
    console.log('\n🧪 Testing table creation permissions...');
    
    try {
      // Try to create a simple test table
      const testSQL = `
        CREATE TABLE IF NOT EXISTS test_table_creation (
          id SERIAL PRIMARY KEY,
          test_field TEXT,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        INSERT INTO test_table_creation (test_field) VALUES ('test');
        
        SELECT * FROM test_table_creation LIMIT 1;
        
        DROP TABLE test_table_creation;
      `;
      
      // This probably won't work via the API, but let's try
      const { data: testData, error: testError } = await supabase.rpc('exec_sql', { sql: testSQL });
      
      if (testError) {
        console.log('❌ Cannot create tables via API:', testError.message);
        console.log('   Tables must be created via migrations or SQL editor');
      } else {
        console.log('✅ Table creation via API works!');
      }
    } catch (err) {
      console.log('❌ Table creation test failed:', err.message);
    }

    console.log('\n📊 Summary:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('• Database connection: Working');
    console.log('• Some tables exist from previous migrations');
    console.log('• learning_content table needs to be created');
    console.log('• Table creation requires migration or SQL editor');
    console.log('• Next step: Use Supabase dashboard SQL editor');

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  }
}

// Run the check
checkExistingTables();
