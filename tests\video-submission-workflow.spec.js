import { test, expect } from '@playwright/test';

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Sample YouTube video URLs for testing
const TEST_VIDEOS = {
  valid: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
  invalid: 'https://invalid-url.com/video',
  malformed: 'not-a-url'
};

test.describe('Video Submission Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
    
    // Navigate to learning center
    await page.goto('/learn');
    await page.waitForLoadState('networkidle');
  });

  test('should complete full video submission workflow', async ({ page }) => {
    // Open video submission form
    await page.click('button:has-text("Submit Video")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('h3:has-text("Submit Video")')).toBeVisible();

    // Step 1: Video URL and Basic Info
    await page.fill('input[label*="YouTube Video URL"]', TEST_VIDEOS.valid);
    await page.waitForTimeout(2000); // Wait for video data to load
    
    // Check if video preview loaded
    const hasVideoPreview = await page.locator('text=Video Found').isVisible();
    if (hasVideoPreview) {
      console.log('Video preview loaded successfully');
    }
    
    // Fill basic information
    await page.fill('input[label*="Title"]', 'Test Educational Video');
    await page.fill('textarea[label*="Description"]', 'This is a test video submission for educational purposes.');
    await page.fill('input[label*="Channel Name"]', 'Test Channel');
    await page.fill('input[label*="Duration"]', '30');
    
    // Proceed to next step
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);

    // Step 2: Skills and Categories
    // Select skills
    const skillChips = page.locator('[data-testid="skill-chip"]');
    const skillCount = await skillChips.count();
    if (skillCount > 0) {
      await skillChips.first().click();
      await skillChips.nth(1).click(); // Select second skill if available
    }
    
    // Select categories
    const categoryChips = page.locator('[data-testid="category-chip"]');
    const categoryCount = await categoryChips.count();
    if (categoryCount > 0) {
      await categoryChips.first().click();
    }
    
    // Select difficulty level
    await page.selectOption('select[label*="Difficulty"]', 'beginner');
    
    // Proceed to next step
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);

    // Step 3: Additional Details
    await page.fill('textarea[label*="Submission Reason"]', 'This video provides excellent educational content for beginners.');
    await page.fill('input[label*="Target Audience"]', 'Beginner developers');
    await page.fill('textarea[label*="Quality Notes"]', 'High quality video with clear explanations.');
    
    // Check community settings
    const publicCheckbox = page.locator('input[type="checkbox"]:near(text="Make submission public")');
    if (await publicCheckbox.isVisible()) {
      await publicCheckbox.check();
    }
    
    const votingCheckbox = page.locator('input[type="checkbox"]:near(text="Enable community voting")');
    if (await votingCheckbox.isVisible()) {
      await votingCheckbox.check();
    }
    
    // Submit the form
    await page.click('button:has-text("Submit")');
    await page.waitForTimeout(2000);
    
    // Check for success message
    const successMessage = page.locator('text=submitted successfully');
    await expect(successMessage).toBeVisible({ timeout: 5000 });
    
    // Modal should close
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('should validate video URL input', async ({ page }) => {
    await page.click('button:has-text("Submit Video")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();

    // Test invalid URL
    await page.fill('input[label*="YouTube Video URL"]', TEST_VIDEOS.invalid);
    await page.waitForTimeout(1000);
    
    // Should show error message
    const errorMessage = page.locator('text=Invalid YouTube URL');
    if (await errorMessage.isVisible()) {
      console.log('URL validation working correctly');
    }
    
    // Test malformed URL
    await page.fill('input[label*="YouTube Video URL"]', TEST_VIDEOS.malformed);
    await page.waitForTimeout(1000);
    
    // Try to proceed without valid URL
    await page.click('button:has-text("Next")');
    
    // Should not proceed or show validation error
    const stillOnStep1 = await page.locator('input[label*="YouTube Video URL"]').isVisible();
    expect(stillOnStep1).toBeTruthy();
    
    // Close modal
    await page.click('button:has-text("Cancel")');
  });

  test('should handle form validation errors', async ({ page }) => {
    await page.click('button:has-text("Submit Video")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();

    // Try to proceed without filling required fields
    await page.click('button:has-text("Next")');
    
    // Should show validation errors or stay on same step
    const urlField = page.locator('input[label*="YouTube Video URL"]');
    await expect(urlField).toBeVisible();
    
    // Fill URL but leave other required fields empty
    await page.fill('input[label*="YouTube Video URL"]', TEST_VIDEOS.valid);
    await page.waitForTimeout(1000);
    
    // Clear title field and try to proceed
    await page.fill('input[label*="Title"]', '');
    await page.click('button:has-text("Next")');
    
    // Should show validation error for title
    const titleError = page.locator('text=required').or(page.locator('text=Please'));
    if (await titleError.isVisible()) {
      console.log('Form validation working correctly');
    }
    
    await page.click('button:has-text("Cancel")');
  });

  test('should save draft and restore form data', async ({ page }) => {
    await page.click('button:has-text("Submit Video")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();

    // Fill some form data
    await page.fill('input[label*="YouTube Video URL"]', TEST_VIDEOS.valid);
    await page.fill('input[label*="Title"]', 'Draft Video Title');
    await page.fill('textarea[label*="Description"]', 'Draft description');
    
    // Close modal without submitting
    await page.click('button:has-text("Cancel")');
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
    
    // Reopen modal
    await page.click('button:has-text("Submit Video")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Check if form data is restored (depending on implementation)
    const titleValue = await page.locator('input[label*="Title"]').inputValue();
    // This test depends on whether draft saving is implemented
    
    await page.click('button:has-text("Cancel")');
  });
});

test.describe('Video Vetting Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
    
    // Navigate to admin dashboard
    await page.goto('/admin');
    await page.waitForLoadState('networkidle');
  });

  test('should display video vetting dashboard', async ({ page }) => {
    // Click on Video Vetting tab
    const vettingTab = page.locator('[role="tab"]:has-text("Video Vetting")');
    if (await vettingTab.isVisible()) {
      await vettingTab.click();
      await page.waitForTimeout(1000);
      
      // Check dashboard elements
      await expect(page.locator('h1:has-text("Video Vetting Dashboard")')).toBeVisible();
      
      // Check stats cards
      await expect(page.locator('text=Pending Review')).toBeVisible();
      await expect(page.locator('text=Approved')).toBeVisible();
      await expect(page.locator('text=Rejected')).toBeVisible();
      await expect(page.locator('text=Auto-Approve Ready')).toBeVisible();
      
      // Check tabs
      await expect(page.locator('[role="tab"]:has-text("Pending Review")')).toBeVisible();
      await expect(page.locator('[role="tab"]:has-text("High Priority")')).toBeVisible();
      await expect(page.locator('[role="tab"]:has-text("Auto-Approve Ready")')).toBeVisible();
    }
  });

  test('should handle video review workflow', async ({ page }) => {
    const vettingTab = page.locator('[role="tab"]:has-text("Video Vetting")');
    if (await vettingTab.isVisible()) {
      await vettingTab.click();
      await page.waitForTimeout(1000);
      
      // Look for review buttons
      const reviewButtons = page.locator('button:has-text("Review")');
      const reviewCount = await reviewButtons.count();
      
      if (reviewCount > 0) {
        // Click first review button
        await reviewButtons.first().click();
        await page.waitForTimeout(1000);
        
        // Check if review modal opens
        const reviewModal = page.locator('[role="dialog"]:has-text("Review Video Submission")');
        if (await reviewModal.isVisible()) {
          // Check review form elements
          await expect(page.locator('select[label*="Action"]')).toBeVisible();
          await expect(page.locator('textarea[label*="Review Notes"]')).toBeVisible();
          
          // Test approval workflow
          await page.selectOption('select[label*="Action"]', 'approved');
          await page.fill('textarea[label*="Review Notes"]', 'This video meets our quality standards.');
          
          // Check approval options
          const addToCatalog = page.locator('input[type="checkbox"]:near(text="Add to course catalog")');
          if (await addToCatalog.isVisible()) {
            await addToCatalog.check();
          }
          
          // Don't actually submit in test
          await page.click('button:has-text("Cancel")');
        }
      } else {
        console.log('No videos pending review');
      }
    }
  });

  test('should filter submissions by status', async ({ page }) => {
    const vettingTab = page.locator('[role="tab"]:has-text("Video Vetting")');
    if (await vettingTab.isVisible()) {
      await vettingTab.click();
      await page.waitForTimeout(1000);
      
      // Test different status tabs
      const statusTabs = [
        'Pending Review',
        'High Priority', 
        'Auto-Approve Ready',
        'Approved',
        'Rejected'
      ];
      
      for (const status of statusTabs) {
        const statusTab = page.locator(`[role="tab"]:has-text("${status}")`);
        if (await statusTab.isVisible()) {
          await statusTab.click();
          await page.waitForTimeout(1000);
          
          // Check if content loads for this status
          const hasContent = await page.locator('table').isVisible();
          const hasEmptyState = await page.locator('text=No submissions found').isVisible();
          
          expect(hasContent || hasEmptyState).toBeTruthy();
        }
      }
    }
  });
});

test.describe('Community Voting System', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
    
    await page.goto('/learn');
    await page.waitForLoadState('networkidle');
  });

  test('should display community recommendations', async ({ page }) => {
    // Navigate to Community tab
    await page.click('[role="tab"]:has-text("Community")');
    await page.waitForTimeout(1000);
    
    await expect(page.locator('h2:has-text("Community Recommendations")')).toBeVisible();
    
    // Check for recommendation cards or empty state
    const hasRecommendations = await page.locator('[data-testid="recommendation-card"]').count() > 0;
    const hasEmptyState = await page.locator('text=No community recommendations yet').isVisible();
    
    expect(hasRecommendations || hasEmptyState).toBeTruthy();
  });

  test('should handle voting on recommendations', async ({ page }) => {
    await page.click('[role="tab"]:has-text("Community")');
    await page.waitForTimeout(1000);
    
    // Look for voting buttons
    const upvoteButtons = page.locator('button:has-text("👍")');
    const downvoteButtons = page.locator('button:has-text("👎")');
    
    const upvoteCount = await upvoteButtons.count();
    const downvoteCount = await downvoteButtons.count();
    
    if (upvoteCount > 0) {
      // Test upvoting
      const initialUpvotes = await upvoteButtons.first().textContent();
      await upvoteButtons.first().click();
      await page.waitForTimeout(1000);
      
      // Check if vote count changed (depending on implementation)
      console.log('Voting functionality tested');
    }
    
    if (downvoteCount > 0) {
      // Test downvoting
      await downvoteButtons.first().click();
      await page.waitForTimeout(1000);
      console.log('Downvote functionality tested');
    }
  });
});
