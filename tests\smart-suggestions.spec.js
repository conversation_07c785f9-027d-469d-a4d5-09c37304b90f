import { test, expect } from '@playwright/test';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Smart Suggestion Engine', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
    
    // Navigate to learning center
    await page.goto('/learn');
    await page.waitForLoadState('networkidle');
  });

  test('should display smart suggestions section', async ({ page }) => {
    // Check if smart suggestions section is visible
    await expect(page.locator('h2:has-text("Smart Suggestions")')).toBeVisible();
    
    // Check description
    await expect(page.locator('text=Personalized recommendations')).toBeVisible();
    
    // Check refresh button
    await expect(page.locator('button:has-text("Refresh")')).toBeVisible();
  });

  test('should display suggestion categories', async ({ page }) => {
    // Check if suggestion tabs are visible
    const suggestionTabs = [
      'For You',
      'Skill Gaps', 
      'Project Based',
      'Trending',
      'Career Path'
    ];
    
    for (const tabName of suggestionTabs) {
      const tab = page.locator(`[role="tab"]:has-text("${tabName}")`);
      if (await tab.isVisible()) {
        await expect(tab).toBeVisible();
        
        // Check for badge indicators
        const badge = tab.locator('[data-testid="badge"]');
        if (await badge.isVisible()) {
          console.log(`${tabName} tab has badge indicator`);
        }
      }
    }
  });

  test('should switch between suggestion categories', async ({ page }) => {
    const suggestionTabs = [
      'For You',
      'Skill Gaps',
      'Project Based', 
      'Trending',
      'Career Path'
    ];
    
    for (const tabName of suggestionTabs) {
      const tab = page.locator(`[role="tab"]:has-text("${tabName}")`);
      if (await tab.isVisible()) {
        await tab.click();
        await page.waitForTimeout(1000);
        
        // Check if content loads for this category
        const hasContent = await page.locator('[data-testid="suggestion-card"]').count() > 0;
        const hasEmptyState = await page.locator('text=No suggestions available').isVisible();
        const hasLoading = await page.locator('[data-testid="spinner"]').isVisible();
        
        // At least one should be true
        expect(hasContent || hasEmptyState || hasLoading).toBeTruthy();
        
        console.log(`${tabName} tab content loaded`);
      }
    }
  });

  test('should display suggestion cards with proper information', async ({ page }) => {
    // Look for suggestion cards
    const suggestionCards = page.locator('[data-testid="suggestion-card"]');
    const cardCount = await suggestionCards.count();
    
    if (cardCount > 0) {
      const firstCard = suggestionCards.first();
      
      // Check card elements
      await expect(firstCard.locator('h3, h4')).toBeVisible(); // Title
      
      // Check for video thumbnail
      const thumbnail = firstCard.locator('img');
      if (await thumbnail.isVisible()) {
        console.log('Suggestion card has thumbnail');
      }
      
      // Check for suggestion reason
      const reason = firstCard.locator('text=Based on').or(firstCard.locator('text=Fill skill gaps')).or(firstCard.locator('text=Relevant to'));
      if (await reason.isVisible()) {
        console.log('Suggestion card shows reasoning');
      }
      
      // Check for confidence indicator
      const confidence = firstCard.locator('[role="progressbar"]');
      if (await confidence.isVisible()) {
        console.log('Suggestion card shows confidence score');
      }
      
      // Check for action buttons
      const enrollButton = firstCard.locator('button:has-text("Enroll")');
      const startButton = firstCard.locator('button:has-text("Start")');
      
      if (await enrollButton.isVisible() || await startButton.isVisible()) {
        console.log('Suggestion card has action buttons');
      }
    } else {
      // Check for empty state
      const emptyState = page.locator('text=No suggestions available');
      if (await emptyState.isVisible()) {
        console.log('Empty state displayed for suggestions');
      }
    }
  });

  test('should handle suggestion interactions', async ({ page }) => {
    const suggestionCards = page.locator('[data-testid="suggestion-card"]');
    const cardCount = await suggestionCards.count();
    
    if (cardCount > 0) {
      const firstCard = suggestionCards.first();
      
      // Test enrollment
      const enrollButton = firstCard.locator('button:has-text("Enroll")');
      if (await enrollButton.isVisible()) {
        await enrollButton.click();
        await page.waitForTimeout(1000);
        
        // Check for success message or enrollment confirmation
        const successMessage = page.locator('text=Enrolled').or(page.locator('text=Added'));
        if (await successMessage.isVisible()) {
          console.log('Enrollment successful');
        }
      }
      
      // Test sharing
      const shareButton = firstCard.locator('button[aria-label*="Share"]');
      if (await shareButton.isVisible()) {
        await shareButton.click();
        await page.waitForTimeout(1000);
        
        // Check if share modal opens
        const shareModal = page.locator('[role="dialog"]:has-text("Share")');
        if (await shareModal.isVisible()) {
          await page.click('button:has-text("Cancel")');
        }
      }
      
      // Test external link
      const externalButton = firstCard.locator('button[aria-label*="External"]');
      if (await externalButton.isVisible()) {
        // Don't actually click external links in tests
        await expect(externalButton).toBeVisible();
      }
    }
  });

  test('should refresh suggestions', async ({ page }) => {
    // Click refresh button
    const refreshButton = page.locator('button:has-text("Refresh")');
    await refreshButton.click();
    
    // Check for loading state
    const loadingSpinner = page.locator('[data-testid="spinner"]');
    if (await loadingSpinner.isVisible()) {
      console.log('Refresh loading state shown');
      
      // Wait for loading to complete
      await page.waitForTimeout(3000);
    }
    
    // Check for success message
    const successMessage = page.locator('text=Suggestions refreshed');
    if (await successMessage.isVisible()) {
      console.log('Refresh success message shown');
    }
    
    // Verify content is still displayed
    const hasContent = await page.locator('[data-testid="suggestion-card"]').count() > 0;
    const hasEmptyState = await page.locator('text=No suggestions available').isVisible();
    
    expect(hasContent || hasEmptyState).toBeTruthy();
  });

  test('should show skill gap suggestions based on projects', async ({ page }) => {
    // Navigate to Skill Gaps tab
    const skillGapsTab = page.locator('[role="tab"]:has-text("Skill Gaps")');
    if (await skillGapsTab.isVisible()) {
      await skillGapsTab.click();
      await page.waitForTimeout(1000);
      
      // Check for skill gap content
      const hasSkillGaps = await page.locator('[data-testid="suggestion-card"]').count() > 0;
      const hasEmptyState = await page.locator('text=No suggestions available').isVisible();
      
      if (hasSkillGaps) {
        // Check for skill gap specific messaging
        const skillGapReason = page.locator('text=Fill skill gaps');
        if (await skillGapReason.isVisible()) {
          console.log('Skill gap suggestions show proper reasoning');
        }
        
        // Check for urgency indicators
        const urgencyIndicator = page.locator('text=high priority').or(page.locator('[data-testid="urgency-badge"]'));
        if (await urgencyIndicator.isVisible()) {
          console.log('Skill gap suggestions show urgency');
        }
      } else if (hasEmptyState) {
        console.log('No skill gaps identified - empty state shown');
      }
    }
  });

  test('should show project-based suggestions', async ({ page }) => {
    // Navigate to Project Based tab
    const projectTab = page.locator('[role="tab"]:has-text("Project Based")');
    if (await projectTab.isVisible()) {
      await projectTab.click();
      await page.waitForTimeout(1000);
      
      // Check for project-based content
      const hasProjectSuggestions = await page.locator('[data-testid="suggestion-card"]').count() > 0;
      const hasEmptyState = await page.locator('text=No suggestions available').isVisible();
      
      if (hasProjectSuggestions) {
        // Check for project-specific messaging
        const projectReason = page.locator('text=Relevant to your current projects');
        if (await projectReason.isVisible()) {
          console.log('Project-based suggestions show proper reasoning');
        }
      } else if (hasEmptyState) {
        console.log('No active projects - empty state shown');
      }
    }
  });

  test('should show trending suggestions', async ({ page }) => {
    // Navigate to Trending tab
    const trendingTab = page.locator('[role="tab"]:has-text("Trending")');
    if (await trendingTab.isVisible()) {
      await trendingTab.click();
      await page.waitForTimeout(1000);
      
      // Check for trending content
      const hasTrending = await page.locator('[data-testid="suggestion-card"]').count() > 0;
      const hasEmptyState = await page.locator('text=No suggestions available').isVisible();
      
      if (hasTrending) {
        // Check for trending indicators
        const trendingReason = page.locator('text=Trending in the community');
        if (await trendingReason.isVisible()) {
          console.log('Trending suggestions show proper reasoning');
        }
        
        // Check for popularity indicators
        const popularityIndicator = page.locator('text=enrollments').or(page.locator('[data-testid="trending-badge"]'));
        if (await popularityIndicator.isVisible()) {
          console.log('Trending suggestions show popularity metrics');
        }
      } else if (hasEmptyState) {
        console.log('No trending content available');
      }
    }
  });

  test('should show career path suggestions', async ({ page }) => {
    // Navigate to Career Path tab
    const careerTab = page.locator('[role="tab"]:has-text("Career Path")');
    if (await careerTab.isVisible()) {
      await careerTab.click();
      await page.waitForTimeout(1000);
      
      // Check for career path content
      const hasCareerSuggestions = await page.locator('[data-testid="suggestion-card"]').count() > 0;
      const hasEmptyState = await page.locator('text=No suggestions available').isVisible();
      
      if (hasCareerSuggestions) {
        // Check for career-specific messaging
        const careerReason = page.locator('text=Advance your career goals');
        if (await careerReason.isVisible()) {
          console.log('Career path suggestions show proper reasoning');
        }
        
        // Check for career relevance indicators
        const careerIndicator = page.locator('[data-testid="career-badge"]');
        if (await careerIndicator.isVisible()) {
          console.log('Career path suggestions show relevance indicators');
        }
      } else if (hasEmptyState) {
        console.log('No career goals set - empty state shown');
      }
    }
  });

  test('should handle suggestion analytics and confidence scores', async ({ page }) => {
    const suggestionCards = page.locator('[data-testid="suggestion-card"]');
    const cardCount = await suggestionCards.count();
    
    if (cardCount > 0) {
      for (let i = 0; i < Math.min(cardCount, 3); i++) {
        const card = suggestionCards.nth(i);
        
        // Check for confidence score
        const confidenceBar = card.locator('[role="progressbar"]');
        if (await confidenceBar.isVisible()) {
          const confidenceValue = await confidenceBar.getAttribute('aria-valuenow');
          console.log(`Suggestion ${i + 1} confidence: ${confidenceValue}%`);
          
          // Confidence should be between 0 and 100
          const confidence = parseInt(confidenceValue);
          expect(confidence).toBeGreaterThanOrEqual(0);
          expect(confidence).toBeLessThanOrEqual(100);
        }
        
        // Check for reasoning text
        const reasoningText = card.locator('[data-testid="suggestion-reason"]');
        if (await reasoningText.isVisible()) {
          const reasoning = await reasoningText.textContent();
          console.log(`Suggestion ${i + 1} reasoning: ${reasoning}`);
          expect(reasoning.length).toBeGreaterThan(0);
        }
      }
    }
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if smart suggestions section is visible on mobile
    await expect(page.locator('h2:has-text("Smart Suggestions")')).toBeVisible();
    
    // Check if tabs are accessible on mobile (might be scrollable)
    const tabs = page.locator('[role="tab"]');
    const tabCount = await tabs.count();
    expect(tabCount).toBeGreaterThan(0);
    
    // Test tab switching on mobile
    const forYouTab = page.locator('[role="tab"]:has-text("For You")');
    if (await forYouTab.isVisible()) {
      await forYouTab.click();
      await page.waitForTimeout(1000);
    }
    
    // Check if suggestion cards are properly sized for mobile
    const suggestionCards = page.locator('[data-testid="suggestion-card"]');
    const cardCount = await suggestionCards.count();
    
    if (cardCount > 0) {
      const firstCard = suggestionCards.first();
      const cardBox = await firstCard.boundingBox();
      
      // Card should fit within mobile viewport width
      expect(cardBox.width).toBeLessThanOrEqual(375);
    }
    
    // Test refresh button on mobile
    const refreshButton = page.locator('button:has-text("Refresh")');
    if (await refreshButton.isVisible()) {
      await refreshButton.click();
      await page.waitForTimeout(1000);
    }
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Test network error handling
    await page.route('**/learning_progress*', route => {
      route.abort('failed');
    });
    
    // Refresh suggestions to trigger error
    const refreshButton = page.locator('button:has-text("Refresh")');
    await refreshButton.click();
    await page.waitForTimeout(2000);
    
    // Should handle error gracefully
    const hasErrorMessage = await page.locator('text=Failed to load').isVisible();
    const hasEmptyState = await page.locator('text=No suggestions available').isVisible();
    const hasContent = await page.locator('[data-testid="suggestion-card"]').count() > 0;
    
    // Should show either error message, empty state, or fallback content
    expect(hasErrorMessage || hasEmptyState || hasContent).toBeTruthy();
  });
});
