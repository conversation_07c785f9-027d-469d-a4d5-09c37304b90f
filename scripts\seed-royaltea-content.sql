-- Seed Royaltea-Specific Learning Content
-- Comprehensive learning materials about revenue sharing, project management, and platform features

-- Insert comprehensive Royaltea learning content
INSERT INTO public.learning_content (
    title, slug, description, content_type, status, content_body, content_summary,
    difficulty_level, estimated_read_time_minutes, estimated_completion_time_minutes,
    prerequisites, learning_objectives, skills_covered, tags, vetting_level,
    vetting_approved, category_id, featured, meta_title, meta_description,
    published_at, created_at
) VALUES 

-- 1. Revenue Sharing Fundamentals
(
    'Understanding Revenue Sharing in Royaltea',
    'understanding-revenue-sharing-royaltea',
    'Learn the fundamentals of how revenue sharing works in the Royaltea platform, including royalty models and payment distribution.',
    'royaltea_guide',
    'published',
    '# Understanding Revenue Sharing in Royaltea

## What is Revenue Sharing?

Revenue sharing in Royaltea is a transparent system that automatically distributes project earnings among contributors based on their contributions and agreed-upon terms. Unlike traditional employment models, revenue sharing creates true partnership opportunities where everyone benefits from project success.

## Core Concepts

### 1. Royalty Models
Royaltea supports multiple royalty distribution models:

- **Equal Split**: All contributors receive equal shares
- **Contribution-Based**: Shares based on time, effort, or value contributed
- **Role-Based**: Different percentages for different roles (developer, designer, manager)
- **Hybrid Models**: Combination of the above approaches

### 2. Tranche System
Projects can be divided into tranches (phases) with different revenue sharing rules:

- **Development Tranche**: Revenue from initial development work
- **Maintenance Tranche**: Ongoing revenue from updates and support  
- **Growth Tranche**: Revenue from scaling and new features
- **Legacy Tranche**: Long-term passive revenue

### 3. Contribution Points
The platform tracks contribution through various metrics:

- **Time Tracking**: Hours worked on specific tasks
- **Deliverable Completion**: Finished features, designs, or content
- **Quality Metrics**: Code reviews, testing, documentation
- **Leadership Activities**: Project management, team coordination

## How It Works

### Step 1: Project Setup
When creating a project, the project owner defines:
- Revenue sharing model
- Contributor roles and percentages
- Tranche structure
- Payment thresholds

### Step 2: Contributor Onboarding
New contributors:
- Review and sign revenue sharing agreements
- Understand their role and expected contribution
- Set up payment preferences
- Begin tracking their work

### Step 3: Revenue Tracking
The platform automatically:
- Monitors project revenue from connected accounts
- Tracks individual contributions
- Calculates distribution amounts
- Handles tax documentation

### Step 4: Distribution
Revenue is distributed:
- Automatically when thresholds are met
- Manually by project administrators
- According to predefined schedules
- With full transparency and audit trails

## Benefits

### For Project Owners
- **Attract Top Talent**: Offer equity-like participation without giving up ownership
- **Align Incentives**: Contributors are motivated by project success
- **Reduce Upfront Costs**: Pay contributors from revenue rather than upfront
- **Scale Efficiently**: Add contributors without fixed salary commitments

### For Contributors
- **Unlimited Earning Potential**: No salary cap - earn based on project success
- **Portfolio Diversification**: Work on multiple revenue-generating projects
- **Skill Development**: Learn while earning from real projects
- **Network Building**: Connect with other professionals and entrepreneurs

## Best Practices

### Setting Up Revenue Sharing
1. **Be Transparent**: Clearly communicate the model and expectations
2. **Start Simple**: Use basic models initially, evolve as needed
3. **Document Everything**: Keep detailed records of agreements and changes
4. **Regular Reviews**: Assess and adjust the model as the project grows

### Managing Contributors
1. **Clear Expectations**: Define roles, responsibilities, and success metrics
2. **Regular Communication**: Hold team meetings and progress reviews
3. **Fair Recognition**: Ensure contributions are accurately tracked and valued
4. **Conflict Resolution**: Have processes for handling disputes

## Common Scenarios

### Scenario 1: SaaS Application
A team builds a subscription-based web application:
- **Developer (40%)**: Builds and maintains the core platform
- **Designer (25%)**: Creates UI/UX and marketing materials  
- **Marketer (25%)**: Drives user acquisition and growth
- **Project Manager (10%)**: Coordinates team and manages operations

### Scenario 2: E-commerce Store
An online store with multiple contributors:
- **Store Owner (50%)**: Provides initial capital and business direction
- **Developer (20%)**: Builds and maintains the website
- **Content Creator (15%)**: Creates product descriptions and marketing content
- **Customer Support (15%)**: Handles customer inquiries and support

### Scenario 3: Digital Course
An educational course with revenue sharing:
- **Subject Expert (60%)**: Creates course content and curriculum
- **Video Producer (25%)**: Handles filming, editing, and production
- **Marketing Specialist (15%)**: Promotes course and manages sales

## Getting Started

Ready to implement revenue sharing in your project? Here''s how to begin:

1. **Define Your Model**: Choose the revenue sharing approach that fits your project
2. **Set Up Your Project**: Create your project in Royaltea with revenue sharing enabled
3. **Invite Contributors**: Send invitations with clear revenue sharing terms
4. **Connect Revenue Sources**: Link your payment processors and revenue streams
5. **Start Tracking**: Begin monitoring contributions and revenue
6. **Distribute Earnings**: Set up automatic or manual distribution schedules

## Next Steps

- [Setting Up Your First Revenue Sharing Project](setting-up-revenue-sharing-project)
- [Managing Contributors and Agreements](managing-contributors-agreements)
- [Revenue Tracking and Analytics](revenue-tracking-analytics)
- [Tax Considerations for Revenue Sharing](tax-considerations-revenue-sharing)',
    'Learn how revenue sharing works in Royaltea, including royalty models, tranche systems, and contribution tracking.',
    'beginner',
    12,
    25,
    ARRAY['Basic understanding of project collaboration'],
    ARRAY['Understand revenue sharing concepts', 'Learn about royalty models', 'Understand contribution tracking', 'Know how to set up revenue sharing'],
    ARRAY['revenue-sharing', 'project-management', 'collaboration', 'royalty-models'],
    ARRAY['royaltea', 'revenue-sharing', 'collaboration', 'project-management', 'royalty'],
    1,
    true,
    (SELECT id FROM public.content_categories WHERE slug = 'revenue-sharing' LIMIT 1),
    true,
    'Understanding Revenue Sharing in Royaltea - Complete Guide',
    'Learn how revenue sharing works in the Royaltea platform, including royalty models, tranche systems, and contribution tracking.',
    NOW(),
    NOW()
),

-- 2. Project Management Guide
(
    'Project Management Best Practices in Royaltea',
    'project-management-best-practices-royaltea',
    'Master project management within the Royaltea ecosystem, from team coordination to milestone tracking.',
    'royaltea_guide',
    'published',
    '# Project Management Best Practices in Royaltea

## Introduction

Effective project management is crucial for success in the Royaltea ecosystem. This guide covers best practices for managing projects, teams, and revenue-sharing arrangements to maximize both productivity and profitability.

## Project Lifecycle Management

### 1. Project Initiation
**Define Clear Objectives**
- Set specific, measurable goals
- Identify target audience and market
- Establish success metrics
- Create project charter

**Team Assembly**
- Identify required skills and roles
- Recruit contributors through Royaltea network
- Define revenue sharing agreements
- Establish communication protocols

### 2. Planning Phase
**Scope Definition**
- Break down project into manageable tasks
- Create work breakdown structure (WBS)
- Estimate time and resource requirements
- Identify dependencies and risks

**Timeline Creation**
- Set realistic milestones and deadlines
- Build in buffer time for unexpected challenges
- Align timeline with revenue projections
- Communicate schedule to all team members

### 3. Execution and Monitoring
**Task Management**
- Use Royaltea''s built-in project tracking tools
- Assign tasks with clear deliverables
- Monitor progress against milestones
- Conduct regular team check-ins

**Quality Assurance**
- Implement code review processes
- Test deliverables before milestone completion
- Gather feedback from stakeholders
- Maintain documentation standards

### 4. Project Closure
**Final Deliverables**
- Complete all outstanding tasks
- Conduct final quality review
- Deploy or launch the project
- Document lessons learned

**Revenue Transition**
- Transition from development to revenue generation
- Set up ongoing maintenance and support
- Implement revenue tracking and distribution
- Plan for future enhancements

## Team Coordination Strategies

### Communication Best Practices
**Regular Meetings**
- Weekly team standups (15-30 minutes)
- Monthly progress reviews
- Quarterly strategic planning sessions
- Ad-hoc problem-solving meetings

**Documentation Standards**
- Maintain project wiki or knowledge base
- Document decisions and rationale
- Keep meeting notes and action items
- Create user guides and technical documentation

**Collaboration Tools**
- Use Royaltea''s integrated communication features
- Leverage shared workspaces and file storage
- Implement version control for all assets
- Maintain transparent progress tracking

### Conflict Resolution
**Common Issues**
- Disagreements over revenue distribution
- Scope creep and changing requirements
- Performance and contribution concerns
- Communication breakdowns

**Resolution Strategies**
- Address issues early and directly
- Use data to support discussions
- Involve neutral mediators when needed
- Document agreements and changes

## Revenue-Focused Management

### Milestone-Based Revenue Planning
**Revenue Milestones**
- Link project phases to revenue generation
- Set up early revenue streams when possible
- Plan for multiple revenue sources
- Track revenue against projections

**Contributor Motivation**
- Tie milestone completion to revenue sharing
- Provide regular updates on project revenue
- Celebrate achievements and successes
- Maintain transparency in financial matters

### Performance Tracking
**Contribution Metrics**
- Track time spent on project tasks
- Monitor deliverable completion rates
- Assess quality of contributions
- Measure impact on project success

**Revenue Attribution**
- Connect individual contributions to revenue outcomes
- Track which features drive the most revenue
- Analyze contributor ROI
- Adjust revenue sharing based on performance

## Risk Management

### Common Project Risks
**Technical Risks**
- Technology stack limitations
- Integration challenges
- Scalability concerns
- Security vulnerabilities

**Business Risks**
- Market changes and competition
- Revenue model validation
- Customer acquisition challenges
- Regulatory compliance issues

**Team Risks**
- Contributor availability and commitment
- Skill gaps and knowledge transfer
- Communication and coordination issues
- Revenue sharing disputes

### Risk Mitigation Strategies
**Proactive Planning**
- Identify risks early in project lifecycle
- Develop contingency plans
- Maintain emergency funds or resources
- Regular risk assessment and review

**Team Resilience**
- Cross-train team members on critical skills
- Maintain documentation and knowledge sharing
- Build strong team relationships and trust
- Have backup contributors identified

## Tools and Resources

### Royaltea Platform Features
**Project Dashboard**
- Real-time project status and metrics
- Revenue tracking and distribution
- Team performance analytics
- Milestone and deadline tracking

**Collaboration Tools**
- Integrated messaging and video calls
- Shared workspaces and file storage
- Task assignment and tracking
- Code review and approval workflows

**Revenue Management**
- Automated revenue distribution
- Contribution tracking and analytics
- Payment processing and tax handling
- Financial reporting and transparency

### External Integrations
**Development Tools**
- GitHub, GitLab, or Bitbucket integration
- CI/CD pipeline connections
- Project management tool sync
- Time tracking application links

**Business Tools**
- Accounting software integration
- Customer relationship management (CRM)
- Marketing automation platforms
- Analytics and reporting tools

## Success Metrics

### Project Success Indicators
**Financial Metrics**
- Revenue generation vs. projections
- Profit margins and cost efficiency
- Return on investment (ROI)
- Revenue growth rate

**Operational Metrics**
- On-time delivery rate
- Quality metrics and defect rates
- Team productivity and efficiency
- Customer satisfaction scores

**Team Metrics**
- Contributor retention rate
- Skill development and growth
- Team satisfaction and engagement
- Knowledge sharing and collaboration

## Case Studies

### Case Study 1: SaaS Platform Development
A team of 5 contributors built a project management SaaS platform:

**Challenge**: Coordinating remote team across different time zones
**Solution**: Implemented asynchronous communication with daily updates
**Result**: Launched on schedule, achieved 150% of revenue projections

**Key Learnings**:
- Clear documentation reduced miscommunication
- Regular milestone celebrations maintained team morale
- Transparent revenue sharing motivated high performance

### Case Study 2: E-commerce Marketplace
A marketplace connecting local artisans with customers:

**Challenge**: Balancing feature development with revenue generation
**Solution**: Implemented MVP approach with rapid iteration cycles
**Result**: Generated revenue within 3 months, scaled to 1000+ users

**Key Learnings**:
- Early revenue validation guided feature priorities
- Customer feedback loops improved product-market fit
- Flexible revenue sharing adapted to changing contributions

## Getting Started Checklist

### Project Setup
- [ ] Define project vision and objectives
- [ ] Identify required skills and team size
- [ ] Create revenue sharing agreement template
- [ ] Set up project workspace in Royaltea
- [ ] Establish communication protocols

### Team Building
- [ ] Post project opportunity on Royaltea
- [ ] Interview and select contributors
- [ ] Onboard team members with agreements
- [ ] Conduct team kickoff meeting
- [ ] Set up collaboration tools and access

### Project Execution
- [ ] Create detailed project plan and timeline
- [ ] Set up milestone tracking and reporting
- [ ] Implement regular team meetings
- [ ] Establish quality assurance processes
- [ ] Monitor progress and adjust as needed

## Next Steps

Ready to apply these project management practices? Here are your next steps:

1. **Assess Your Current Project**: Evaluate your existing project management approach
2. **Implement Key Practices**: Start with the most impactful practices for your situation
3. **Build Your Team**: Use Royaltea to find and onboard the right contributors
4. **Set Up Tracking**: Implement systems to monitor progress and revenue
5. **Iterate and Improve**: Continuously refine your approach based on results

## Additional Resources

- [Setting Up Your Project Workspace](setting-up-project-workspace)
- [Building High-Performance Teams](building-high-performance-teams)
- [Revenue Optimization Strategies](revenue-optimization-strategies)
- [Conflict Resolution in Revenue Sharing](conflict-resolution-revenue-sharing)',
    'Master project management in Royaltea with best practices for team coordination, milestone tracking, and revenue-focused management.',
    'intermediate',
    15,
    35,
    ARRAY['Basic project management knowledge', 'Understanding of revenue sharing'],
    ARRAY['Master project lifecycle management', 'Learn team coordination strategies', 'Understand revenue-focused management', 'Implement risk management practices'],
    ARRAY['project-management', 'team-coordination', 'milestone-tracking', 'risk-management'],
    ARRAY['royaltea', 'project-management', 'team-coordination', 'leadership', 'collaboration'],
    2,
    true,
    (SELECT id FROM public.content_categories WHERE slug = 'project-management' LIMIT 1),
    true,
    'Project Management Best Practices in Royaltea - Complete Guide',
    'Master project management within the Royaltea ecosystem, from team coordination to milestone tracking and revenue optimization.',
    NOW(),
    NOW()
),

-- 3. Platform Features Overview
(
    'Complete Guide to Royaltea Platform Features',
    'complete-guide-royaltea-platform-features',
    'Comprehensive overview of all Royaltea platform features, from project creation to revenue distribution.',
    'royaltea_guide',
    'published',
    '# Complete Guide to Royaltea Platform Features

## Platform Overview

Royaltea is a comprehensive platform designed to facilitate collaborative project development with built-in revenue sharing capabilities. This guide covers all major features and how to use them effectively.

## Core Features

### 1. Project Management Hub
**Project Dashboard**
- Real-time project status and metrics
- Task assignment and progress tracking
- Team member activity feeds
- Revenue and financial overview

**Kanban Workflow**
- Visual task management with drag-and-drop
- Customizable workflow stages
- Task dependencies and blocking relationships
- Automated progress updates

**Mission Board**
- Gamified task completion system
- Skill-based task recommendations
- Achievement tracking and rewards
- Performance analytics

### 2. Team Collaboration
**Communication Tools**
- Integrated messaging system
- Video conferencing capabilities
- Screen sharing and collaboration
- Threaded discussions by topic

**File Management**
- Centralized file storage and versioning
- Real-time collaborative editing
- Asset library and organization
- Integration with external storage services

**Code Collaboration**
- Git repository integration
- Code review and approval workflows
- Automated testing and deployment
- Documentation generation

### 3. Revenue Sharing System
**Agreement Management**
- Digital contract creation and signing
- Revenue model configuration
- Contributor role definitions
- Automated compliance tracking

**Revenue Tracking**
- Real-time revenue monitoring
- Multiple revenue source integration
- Automated distribution calculations
- Tax documentation and reporting

**Payment Processing**
- Secure payment gateway integration
- Multiple payment method support
- International currency handling
- Automated escrow and distribution

### 4. Vetting and Skill Verification
**6-Level Vetting System**
- Progressive skill verification
- Peer and expert review processes
- Portfolio and project assessment
- Continuous skill development tracking

**Learning Integration**
- Curated learning content by skill level
- Progress tracking and certification
- Skill gap analysis and recommendations
- Community-driven content suggestions

### 5. User Profiles and Networking
**Professional Profiles**
- Comprehensive skill and experience showcase
- Portfolio and project history
- Testimonials and recommendations
- Availability and collaboration preferences

**Networking Features**
- Skill-based contributor discovery
- Project opportunity matching
- Professional relationship management
- Community forums and discussions

## Advanced Features

### 1. Analytics and Reporting
**Project Analytics**
- Performance metrics and KPIs
- Revenue forecasting and trends
- Team productivity analysis
- Risk assessment and alerts

**Financial Reporting**
- Detailed revenue and expense tracking
- Profit and loss statements
- Tax reporting and documentation
- Audit trails and compliance

**Team Performance**
- Individual contributor metrics
- Team collaboration effectiveness
- Skill development progress
- Goal achievement tracking

### 2. Automation and Integrations
**Workflow Automation**
- Task assignment based on skills and availability
- Automated milestone and deadline reminders
- Revenue distribution triggers
- Quality assurance checkpoints

**Third-Party Integrations**
- Development tools (GitHub, GitLab, Bitbucket)
- Design tools (Figma, Adobe Creative Suite)
- Communication platforms (Slack, Discord)
- Business tools (QuickBooks, Stripe, PayPal)

### 3. Mobile and Cross-Platform
**Mobile Applications**
- iOS and Android native apps
- Real-time notifications and updates
- Offline capability for essential features
- Mobile-optimized user interface

**Cross-Platform Sync**
- Seamless data synchronization
- Multi-device access and continuity
- Cloud-based storage and backup
- Progressive web app capabilities

## Getting Started Guide

### 1. Account Setup
**Registration Process**
- Create account with email or social login
- Complete profile with skills and experience
- Verify identity and payment information
- Set up notification preferences

**Profile Optimization**
- Add professional photo and bio
- List skills, experience, and certifications
- Upload portfolio and work samples
- Set availability and collaboration preferences

### 2. Creating Your First Project
**Project Configuration**
- Define project scope and objectives
- Set up revenue sharing model
- Configure team roles and permissions
- Establish timeline and milestones

**Team Building**
- Post project opportunities
- Review and interview candidates
- Send collaboration invitations
- Onboard team members with agreements

### 3. Project Execution
**Daily Operations**
- Use kanban board for task management
- Conduct regular team meetings
- Track progress and update stakeholders
- Monitor revenue and financial metrics

**Quality Assurance**
- Implement code review processes
- Test deliverables before milestone completion
- Gather feedback from stakeholders
- Maintain documentation standards

## Best Practices

### 1. Project Setup
**Clear Communication**
- Define roles and responsibilities clearly
- Set expectations for communication frequency
- Establish decision-making processes
- Create conflict resolution procedures

**Revenue Planning**
- Start with simple revenue sharing models
- Be transparent about financial projections
- Plan for multiple revenue streams
- Regular review and adjustment of terms

### 2. Team Management
**Contributor Onboarding**
- Provide comprehensive project overview
- Explain revenue sharing terms clearly
- Set up necessary tools and access
- Assign initial tasks and mentorship

**Performance Management**
- Set clear performance expectations
- Provide regular feedback and coaching
- Recognize and reward achievements
- Address performance issues promptly

### 3. Revenue Optimization
**Revenue Diversification**
- Explore multiple monetization strategies
- Test different pricing models
- Develop recurring revenue streams
- Plan for scalability and growth

**Cost Management**
- Track project expenses carefully
- Optimize resource allocation
- Negotiate better rates with vendors
- Implement cost-saving measures

## Troubleshooting Common Issues

### Technical Issues
**Platform Access Problems**
- Clear browser cache and cookies
- Try different browser or device
- Check internet connection stability
- Contact support for account issues

**Integration Failures**
- Verify API keys and permissions
- Check third-party service status
- Review integration configuration
- Test with minimal setup first

### Team Issues
**Communication Breakdowns**
- Establish regular check-in schedules
- Use multiple communication channels
- Document important decisions
- Involve mediators when necessary

**Performance Concerns**
- Address issues early and directly
- Provide additional training or resources
- Adjust workload or responsibilities
- Consider team restructuring if needed

### Revenue Issues
**Payment Processing Problems**
- Verify payment method information
- Check for sufficient account balances
- Review transaction limits and restrictions
- Contact payment processor support

**Distribution Disputes**
- Review contribution tracking data
- Discuss concerns openly with team
- Use platform mediation services
- Adjust agreements if necessary

## Support and Resources

### Help and Documentation
**Knowledge Base**
- Comprehensive feature documentation
- Step-by-step tutorials and guides
- Frequently asked questions
- Video tutorials and webinars

**Community Support**
- User forums and discussions
- Peer-to-peer help and advice
- Feature requests and feedback
- Beta testing opportunities

### Professional Support
**Customer Success**
- Dedicated account management
- Onboarding assistance and training
- Best practice consultation
- Performance optimization advice

**Technical Support**
- 24/7 technical assistance
- Bug reporting and resolution
- Integration support and troubleshooting
- Custom development consultation

## Future Roadmap

### Upcoming Features
**Enhanced AI Integration**
- Intelligent task assignment
- Automated project risk assessment
- Predictive revenue modeling
- Smart contributor matching

**Advanced Analytics**
- Machine learning insights
- Predictive performance analytics
- Market trend analysis
- Competitive intelligence

**Expanded Integrations**
- Additional development tools
- Enhanced business software connections
- Marketplace and e-commerce platforms
- Advanced financial services

## Conclusion

Royaltea provides a comprehensive platform for collaborative project development with built-in revenue sharing. By leveraging these features effectively, teams can build successful projects while ensuring fair compensation for all contributors.

The key to success is starting with clear communication, setting up proper processes, and continuously optimizing based on results and feedback. The platform provides the tools and infrastructure - your team provides the vision and execution.

## Next Steps

Ready to get started with Royaltea? Here''s what to do next:

1. **Complete Your Profile**: Set up a comprehensive professional profile
2. **Explore the Platform**: Familiarize yourself with key features and tools
3. **Join or Create a Project**: Start collaborating and earning revenue
4. **Connect with the Community**: Network with other professionals and learn from their experiences
5. **Provide Feedback**: Help improve the platform by sharing your suggestions and experiences

## Additional Resources

- [Quick Start Guide for New Users](quick-start-guide-new-users)
- [Advanced Project Management Techniques](advanced-project-management-techniques)
- [Revenue Optimization Strategies](revenue-optimization-strategies)
- [Building Successful Remote Teams](building-successful-remote-teams)',
    'Comprehensive overview of all Royaltea platform features, from project creation to revenue distribution and team collaboration.',
    'beginner',
    20,
    45,
    ARRAY['Basic computer skills', 'Understanding of collaborative work'],
    ARRAY['Master all platform features', 'Understand project management tools', 'Learn revenue sharing system', 'Navigate team collaboration features'],
    ARRAY['platform-features', 'project-management', 'revenue-sharing', 'team-collaboration'],
    ARRAY['royaltea', 'platform-guide', 'features', 'tutorial', 'getting-started'],
    1,
    true,
    (SELECT id FROM public.content_categories WHERE slug = 'royaltea-platform' LIMIT 1),
    true,
    'Complete Guide to Royaltea Platform Features - Master the Platform',
    'Comprehensive overview of all Royaltea platform features, from project creation to revenue distribution and team collaboration tools.',
    NOW(),
    NOW()
);

-- Update content categories to add foreign key references
UPDATE public.learning_content 
SET category_id = (SELECT id FROM public.content_categories WHERE slug = 'revenue-sharing' LIMIT 1)
WHERE slug = 'understanding-revenue-sharing-royaltea';

UPDATE public.learning_content 
SET category_id = (SELECT id FROM public.content_categories WHERE slug = 'project-management' LIMIT 1)
WHERE slug = 'project-management-best-practices-royaltea';

UPDATE public.learning_content 
SET category_id = (SELECT id FROM public.content_categories WHERE slug = 'royaltea-platform' LIMIT 1)
WHERE slug = 'complete-guide-royaltea-platform-features';

-- Insert some sample external content sources for demonstration
INSERT INTO public.external_content_sources (name, base_url, supported_content_types, import_method, is_active) VALUES
('Royaltea Blog', 'https://blog.royalty.technology', ARRAY['article', 'tutorial'], 'api', true),
('Team Documentation', 'https://docs.royalty.technology', ARRAY['guide', 'tutorial'], 'manual', true),
('Community Wiki', 'https://wiki.royalty.technology', ARRAY['article', 'guide'], 'scrape', true)
ON CONFLICT (name) DO NOTHING;

RAISE NOTICE 'Royaltea-specific learning content has been created successfully!';
RAISE NOTICE 'Created 3 comprehensive guides covering revenue sharing, project management, and platform features';
RAISE NOTICE 'All content is published and ready for users to access';
