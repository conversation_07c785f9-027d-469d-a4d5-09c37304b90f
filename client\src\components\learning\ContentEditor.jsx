import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Input, Textarea, Select, SelectItem,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure,
  Tabs, Tab, Chip, Switch, Divider, Progress
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  Save, Eye, Upload, Image, Video, Link, Bold, Italic, 
  List, Hash, Quote, Code, Plus, Trash2, Settings, 
  BookOpen, Target, Award, Clock, Users
} from 'lucide-react';

/**
 * Content Editor Component
 * 
 * Rich text editor for creating and editing learning content with:
 * - Markdown support with live preview
 * - Media upload and embedding
 * - Metadata management
 * - Vetting level assignment
 * - SEO optimization
 * - Content organization
 */
const ContentEditor = ({ contentId = null, onSave, onCancel, initialData = null }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('content');
  const [categories, setCategories] = useState([]);
  const [series, setSeries] = useState([]);

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: '',
    content_type: 'article',
    content_body: '',
    content_summary: '',
    
    // Media
    featured_image_url: '',
    thumbnail_url: '',
    media_assets: [],
    
    // Learning metadata
    difficulty_level: 'beginner',
    estimated_read_time_minutes: '',
    estimated_completion_time_minutes: '',
    prerequisites: [''],
    learning_objectives: [''],
    skills_covered: [''],
    tags: [''],
    
    // Vetting
    vetting_level: 1,
    vetting_approved: false,
    vetting_skills: [''],
    
    // Organization
    category_id: '',
    series_id: '',
    
    // SEO
    meta_title: '',
    meta_description: '',
    meta_keywords: [''],
    
    // Publishing
    status: 'draft',
    featured: false,
    
    // External content
    external_source_url: '',
    external_source_name: '',
    external_author_name: '',
    external_author_url: ''
  });

  const { isOpen: isPreviewOpen, onOpen: onPreviewOpen, onClose: onPreviewClose } = useDisclosure();

  const contentTypes = [
    { key: 'article', label: 'Article', description: 'Written content with rich text' },
    { key: 'tutorial', label: 'Tutorial', description: 'Step-by-step instructional content' },
    { key: 'guide', label: 'Guide', description: 'Comprehensive reference material' },
    { key: 'royaltea_guide', label: 'Royaltea Guide', description: 'Platform-specific documentation' },
    { key: 'embedded', label: 'Embedded Content', description: 'External content embedded in our site' },
    { key: 'external_import', label: 'External Import', description: 'Content imported from external sources' }
  ];

  const difficultyLevels = [
    { key: 'beginner', label: 'Beginner', color: 'success' },
    { key: 'intermediate', label: 'Intermediate', color: 'warning' },
    { key: 'advanced', label: 'Advanced', color: 'danger' },
    { key: 'expert', label: 'Expert', color: 'secondary' }
  ];

  const vettingLevels = [
    { level: 1, name: 'Learning', color: 'warning', icon: '🟡' },
    { level: 2, name: 'Peer Verified', color: 'secondary', icon: '🟠' },
    { level: 3, name: 'Project Verified', color: 'success', icon: '🟢' },
    { level: 4, name: 'Expert Verified', color: 'primary', icon: '🔵' },
    { level: 5, name: 'Master Verified', color: 'danger', icon: '🟣' }
  ];

  useEffect(() => {
    loadCategories();
    loadSeries();
    
    if (contentId) {
      loadContent();
    } else if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [contentId, initialData]);

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('content_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadSeries = async () => {
    try {
      const { data, error } = await supabase
        .from('content_series')
        .select('*')
        .eq('is_active', true)
        .order('title', { ascending: true });

      if (error) throw error;
      setSeries(data || []);
    } catch (error) {
      console.error('Error loading series:', error);
    }
  };

  const loadContent = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('learning_content')
        .select('*')
        .eq('id', contentId)
        .single();

      if (error) throw error;

      setFormData(prev => ({ ...prev, ...data }));
    } catch (error) {
      console.error('Error loading content:', error);
      toast.error('Failed to load content');
    } finally {
      setLoading(false);
    }
  };

  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const estimateReadTime = (content) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Auto-generate slug from title
      if (field === 'title' && !prev.slug) {
        updated.slug = generateSlug(value);
      }
      
      // Auto-estimate read time from content
      if (field === 'content_body') {
        updated.estimated_read_time_minutes = estimateReadTime(value);
      }
      
      // Auto-generate meta title from title
      if (field === 'title' && !prev.meta_title) {
        updated.meta_title = value;
      }
      
      // Auto-generate meta description from description
      if (field === 'description' && !prev.meta_description) {
        updated.meta_description = value;
      }
      
      return updated;
    });
  };

  const handleArrayInputChange = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleSave = async (publishStatus = 'draft') => {
    if (!formData.title.trim()) {
      toast.error('Title is required');
      return;
    }

    try {
      setSaving(true);

      const contentData = {
        ...formData,
        status: publishStatus,
        author_id: currentUser.id,
        author_name: currentUser.user_metadata?.full_name || currentUser.email,
        
        // Clean up array fields
        prerequisites: formData.prerequisites.filter(p => p.trim()),
        learning_objectives: formData.learning_objectives.filter(o => o.trim()),
        skills_covered: formData.skills_covered.filter(s => s.trim()),
        tags: formData.tags.filter(t => t.trim()),
        vetting_skills: formData.vetting_skills.filter(s => s.trim()),
        meta_keywords: formData.meta_keywords.filter(k => k.trim()),
        
        // Convert string numbers to integers
        estimated_read_time_minutes: parseInt(formData.estimated_read_time_minutes) || null,
        estimated_completion_time_minutes: parseInt(formData.estimated_completion_time_minutes) || null,
        vetting_level: parseInt(formData.vetting_level) || null,
        
        updated_at: new Date().toISOString()
      };

      if (publishStatus === 'published') {
        contentData.published_at = new Date().toISOString();
      }

      let result;
      if (contentId) {
        // Update existing content
        const { data, error } = await supabase
          .from('learning_content')
          .update(contentData)
          .eq('id', contentId)
          .select()
          .single();

        if (error) throw error;
        result = data;
      } else {
        // Create new content
        const { data, error } = await supabase
          .from('learning_content')
          .insert([contentData])
          .select()
          .single();

        if (error) throw error;
        result = data;
      }

      toast.success(`Content ${publishStatus === 'published' ? 'published' : 'saved'} successfully!`);
      
      if (onSave) {
        onSave(result);
      }

    } catch (error) {
      console.error('Error saving content:', error);
      toast.error('Failed to save content');
    } finally {
      setSaving(false);
    }
  };

  const renderContentTab = () => (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Basic Information</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <Input
            label="Title"
            placeholder="Enter content title..."
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            isRequired
          />

          <Input
            label="Slug"
            placeholder="url-friendly-slug"
            value={formData.slug}
            onChange={(e) => handleInputChange('slug', e.target.value)}
            description="URL-friendly version of the title"
          />

          <Textarea
            label="Description"
            placeholder="Brief description of the content..."
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            minRows={3}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              label="Content Type"
              placeholder="Select content type"
              selectedKeys={[formData.content_type]}
              onSelectionChange={(keys) => handleInputChange('content_type', Array.from(keys)[0])}
            >
              {contentTypes.map((type) => (
                <SelectItem key={type.key} value={type.key}>
                  <div>
                    <div className="font-medium">{type.label}</div>
                    <div className="text-sm text-default-500">{type.description}</div>
                  </div>
                </SelectItem>
              ))}
            </Select>

            <Select
              label="Difficulty Level"
              placeholder="Select difficulty"
              selectedKeys={[formData.difficulty_level]}
              onSelectionChange={(keys) => handleInputChange('difficulty_level', Array.from(keys)[0])}
            >
              {difficultyLevels.map((level) => (
                <SelectItem key={level.key} value={level.key}>
                  <Chip color={level.color} variant="flat" size="sm">
                    {level.label}
                  </Chip>
                </SelectItem>
              ))}
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Content Body */}
      <Card>
        <CardHeader className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Content Body</h3>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="flat"
              startContent={<Eye className="w-4 h-4" />}
              onClick={onPreviewOpen}
            >
              Preview
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          <Textarea
            placeholder="Write your content here... (Markdown supported)"
            value={formData.content_body}
            onChange={(e) => handleInputChange('content_body', e.target.value)}
            minRows={20}
            className="font-mono"
          />
          
          <div className="mt-4 text-sm text-default-500">
            <p>Supports Markdown formatting. Estimated read time: {formData.estimated_read_time_minutes || 0} minutes</p>
          </div>
        </CardBody>
      </Card>

      {/* Summary */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Content Summary</h3>
        </CardHeader>
        <CardBody>
          <Textarea
            placeholder="Brief summary or excerpt..."
            value={formData.content_summary}
            onChange={(e) => handleInputChange('content_summary', e.target.value)}
            minRows={3}
            description="This will be shown in content listings and previews"
          />
        </CardBody>
      </Card>
    </div>
  );

  const renderLearningTab = () => (
    <div className="space-y-6">
      {/* Learning Metadata */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Target className="w-5 h-5" />
            Learning Metadata
          </h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Estimated Read Time (minutes)"
              type="number"
              placeholder="15"
              value={formData.estimated_read_time_minutes}
              onChange={(e) => handleInputChange('estimated_read_time_minutes', e.target.value)}
            />

            <Input
              label="Estimated Completion Time (minutes)"
              type="number"
              placeholder="30"
              value={formData.estimated_completion_time_minutes}
              onChange={(e) => handleInputChange('estimated_completion_time_minutes', e.target.value)}
            />
          </div>

          {/* Prerequisites */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium">Prerequisites</label>
              <Button
                size="sm"
                variant="flat"
                startContent={<Plus className="w-4 h-4" />}
                onClick={() => addArrayItem('prerequisites')}
              >
                Add
              </Button>
            </div>
            <div className="space-y-2">
              {formData.prerequisites.map((prereq, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    placeholder="Enter prerequisite..."
                    value={prereq}
                    onChange={(e) => handleArrayInputChange('prerequisites', index, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    size="sm"
                    variant="flat"
                    color="danger"
                    isIconOnly
                    onClick={() => removeArrayItem('prerequisites', index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Learning Objectives */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium">Learning Objectives</label>
              <Button
                size="sm"
                variant="flat"
                startContent={<Plus className="w-4 h-4" />}
                onClick={() => addArrayItem('learning_objectives')}
              >
                Add
              </Button>
            </div>
            <div className="space-y-2">
              {formData.learning_objectives.map((objective, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    placeholder="What will learners achieve..."
                    value={objective}
                    onChange={(e) => handleArrayInputChange('learning_objectives', index, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    size="sm"
                    variant="flat"
                    color="danger"
                    isIconOnly
                    onClick={() => removeArrayItem('learning_objectives', index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Skills Covered */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium">Skills Covered</label>
              <Button
                size="sm"
                variant="flat"
                startContent={<Plus className="w-4 h-4" />}
                onClick={() => addArrayItem('skills_covered')}
              >
                Add
              </Button>
            </div>
            <div className="space-y-2">
              {formData.skills_covered.map((skill, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    placeholder="Skill name..."
                    value={skill}
                    onChange={(e) => handleArrayInputChange('skills_covered', index, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    size="sm"
                    variant="flat"
                    color="danger"
                    isIconOnly
                    onClick={() => removeArrayItem('skills_covered', index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Vetting Integration */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Award className="w-5 h-5" />
            Vetting Integration
          </h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <Select
            label="Vetting Level"
            placeholder="Select vetting level"
            selectedKeys={[formData.vetting_level.toString()]}
            onSelectionChange={(keys) => handleInputChange('vetting_level', parseInt(Array.from(keys)[0]))}
          >
            {vettingLevels.map((level) => (
              <SelectItem key={level.level.toString()} value={level.level}>
                <div className="flex items-center gap-2">
                  <span>{level.icon}</span>
                  <span>Level {level.level} - {level.name}</span>
                </div>
              </SelectItem>
            ))}
          </Select>

          <Switch
            isSelected={formData.vetting_approved}
            onValueChange={(value) => handleInputChange('vetting_approved', value)}
          >
            Vetting Approved
          </Switch>

          {/* Vetting Skills */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium">Vetting Skills</label>
              <Button
                size="sm"
                variant="flat"
                startContent={<Plus className="w-4 h-4" />}
                onClick={() => addArrayItem('vetting_skills')}
              >
                Add
              </Button>
            </div>
            <div className="space-y-2">
              {formData.vetting_skills.map((skill, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    placeholder="Vetting skill..."
                    value={skill}
                    onChange={(e) => handleArrayInputChange('vetting_skills', index, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    size="sm"
                    variant="flat"
                    color="danger"
                    isIconOnly
                    onClick={() => removeArrayItem('vetting_skills', index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading content editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="content-editor max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">
            {contentId ? 'Edit Content' : 'Create New Content'}
          </h1>
          <p className="text-default-600">
            Create comprehensive learning materials with rich content and metadata
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="flat"
            onClick={onCancel}
          >
            Cancel
          </Button>
          
          <Button
            color="secondary"
            variant="flat"
            startContent={<Save className="w-4 h-4" />}
            onClick={() => handleSave('draft')}
            isLoading={saving}
          >
            Save Draft
          </Button>
          
          <Button
            color="primary"
            startContent={<BookOpen className="w-4 h-4" />}
            onClick={() => handleSave('published')}
            isLoading={saving}
          >
            Publish
          </Button>
        </div>
      </div>

      {/* Main Editor */}
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={setActiveTab}
        className="w-full"
      >
        <Tab key="content" title="Content">
          {renderContentTab()}
        </Tab>
        
        <Tab key="learning" title="Learning">
          {renderLearningTab()}
        </Tab>
        
        <Tab key="organization" title="Organization">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Organization</h3>
              </CardHeader>
              <CardBody className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Select
                    label="Category"
                    placeholder="Select category"
                    selectedKeys={formData.category_id ? [formData.category_id] : []}
                    onSelectionChange={(keys) => handleInputChange('category_id', Array.from(keys)[0] || '')}
                  >
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        <div className="flex items-center gap-2">
                          <span>{category.icon}</span>
                          <span>{category.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </Select>

                  <Select
                    label="Series"
                    placeholder="Select series (optional)"
                    selectedKeys={formData.series_id ? [formData.series_id] : []}
                    onSelectionChange={(keys) => handleInputChange('series_id', Array.from(keys)[0] || '')}
                  >
                    {series.map((s) => (
                      <SelectItem key={s.id} value={s.id}>
                        {s.title}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                {/* Tags */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium">Tags</label>
                    <Button
                      size="sm"
                      variant="flat"
                      startContent={<Plus className="w-4 h-4" />}
                      onClick={() => addArrayItem('tags')}
                    >
                      Add
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {formData.tags.map((tag, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Input
                          placeholder="Tag name..."
                          value={tag}
                          onChange={(e) => handleArrayInputChange('tags', index, e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          size="sm"
                          variant="flat"
                          color="danger"
                          isIconOnly
                          onClick={() => removeArrayItem('tags', index)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <Switch
                    isSelected={formData.featured}
                    onValueChange={(value) => handleInputChange('featured', value)}
                  >
                    Featured Content
                  </Switch>
                </div>
              </CardBody>
            </Card>
          </div>
        </Tab>
      </Tabs>

      {/* Preview Modal */}
      <Modal isOpen={isPreviewOpen} onClose={onPreviewClose} size="5xl">
        <ModalContent>
          <ModalHeader>
            <h2 className="text-xl font-bold">Content Preview</h2>
          </ModalHeader>
          <ModalBody>
            <div className="prose prose-lg max-w-none dark:prose-invert">
              <h1>{formData.title}</h1>
              {formData.description && <p className="lead">{formData.description}</p>}
              <div dangerouslySetInnerHTML={{ __html: formData.content_body.replace(/\n/g, '<br>') }} />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onPreviewClose}>
              Close Preview
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ContentEditor;
