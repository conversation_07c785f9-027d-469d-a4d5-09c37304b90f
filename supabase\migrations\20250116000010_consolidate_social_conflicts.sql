-- COMPREHENSIVE SOCIAL SYSTEM CONSOLIDATION MIGRATION
-- Resolves ALL conflicts between multiple social system migrations
-- Preserves the working social system and adds missing features from all migrations

-- ============================================================================
-- CONFLICT RESOLUTION: Drop ALL duplicate/conflicting tables
-- ============================================================================

-- From 20250116000005_social_features_system.sql
DROP TABLE IF EXISTS public.call_participants CASCADE;
DROP TABLE IF EXISTS public.calls CASCADE;
DROP TABLE IF EXISTS public.activity_engagement CASCADE;
DROP TABLE IF EXISTS public.activity_feed CASCADE;

-- From 20250609000002_ally_friend_system.sql
DROP TABLE IF EXISTS public.friend_requests CASCADE;
DROP TABLE IF EXISTS public.social_interactions CASCADE;
DROP TABLE IF EXISTS public.ally_recommendations CASCADE;

-- From 20250609000003_collaboration_system.sql
DROP TABLE IF EXISTS public.conversations CASCADE;
DROP TABLE IF EXISTS public.message_reactions CASCADE;
DROP TABLE IF EXISTS public.shared_files CASCADE;
DROP TABLE IF EXISTS public.file_permissions CASCADE;
DROP TABLE IF EXISTS public.activity_feeds CASCADE;
DROP TABLE IF EXISTS public.activity_reactions CASCADE;
DROP TABLE IF EXISTS public.notification_preferences CASCADE;

-- From 20250609000004_recognition_analytics_system.sql
DROP TABLE IF EXISTS public.user_achievements CASCADE;
DROP TABLE IF EXISTS public.recognition_rankings CASCADE;
DROP TABLE IF EXISTS public.collaboration_metrics CASCADE;
DROP TABLE IF EXISTS public.user_endorsements CASCADE;
DROP TABLE IF EXISTS public.recommendation_algorithms CASCADE;

-- Drop conflicting functions from all migrations
DROP FUNCTION IF EXISTS public.send_friend_request(UUID, TEXT);
DROP FUNCTION IF EXISTS public.respond_to_friend_request(UUID, TEXT);
DROP FUNCTION IF EXISTS public.send_message(UUID, UUID, TEXT, message_type, UUID, JSONB);
DROP FUNCTION IF EXISTS public.create_activity(activity_type, TEXT, TEXT, JSONB, VARCHAR, VARCHAR, UUID);
DROP FUNCTION IF EXISTS public.initiate_call(UUID[], VARCHAR, UUID);
DROP FUNCTION IF EXISTS public.get_social_feed(INTEGER, INTEGER);
DROP FUNCTION IF EXISTS get_mutual_allies(UUID, UUID);
DROP FUNCTION IF EXISTS are_users_allies(UUID, UUID);
DROP FUNCTION IF EXISTS create_direct_conversation(UUID, UUID);
DROP FUNCTION IF EXISTS get_unread_message_count(UUID);

-- Drop conflicting types
DROP TYPE IF EXISTS connection_status CASCADE;
DROP TYPE IF EXISTS message_type CASCADE;
DROP TYPE IF EXISTS activity_type CASCADE;
DROP TYPE IF EXISTS call_status CASCADE;

-- ============================================================================
-- ENHANCE EXISTING SOCIAL SYSTEM: Add missing valuable features
-- ============================================================================

-- Add privacy settings table (valuable feature from ally_friend_system)
CREATE TABLE IF NOT EXISTS public.user_privacy_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    
    -- Discovery settings
    discoverable_by_email BOOLEAN DEFAULT true,
    discoverable_by_name BOOLEAN DEFAULT true,
    discoverable_by_skills BOOLEAN DEFAULT true,
    show_in_recommendations BOOLEAN DEFAULT true,
    
    -- Connection settings
    allow_ally_requests BOOLEAN DEFAULT true,
    require_mutual_connections BOOLEAN DEFAULT false,
    auto_accept_from_allies BOOLEAN DEFAULT false,
    
    -- Profile visibility
    profile_visibility VARCHAR(20) DEFAULT 'public' CHECK (profile_visibility IN ('public', 'allies_only', 'private')),
    show_ally_count BOOLEAN DEFAULT true,
    show_project_count BOOLEAN DEFAULT true,
    show_skills BOOLEAN DEFAULT true,
    show_activity_status BOOLEAN DEFAULT true,
    
    -- Communication preferences
    email_on_ally_request BOOLEAN DEFAULT true,
    email_on_request_accepted BOOLEAN DEFAULT true,
    push_notifications BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Add conversation groups for team/project messaging
CREATE TABLE IF NOT EXISTS public.conversation_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    group_type VARCHAR(50) DEFAULT 'team' CHECK (group_type IN ('team', 'project', 'alliance', 'direct')),
    created_by UUID NOT NULL REFERENCES auth.users(id),
    team_id UUID REFERENCES public.teams(id),
    project_id UUID REFERENCES public.projects(id),
    
    -- Group settings
    is_private BOOLEAN DEFAULT true,
    allow_file_sharing BOOLEAN DEFAULT true,
    max_members INTEGER DEFAULT 50,
    
    -- Group metadata
    avatar_url TEXT,
    settings JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Add group membership
CREATE TABLE IF NOT EXISTS public.conversation_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.conversation_groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member')),
    joined_at TIMESTAMP DEFAULT NOW(),
    last_read_at TIMESTAMP DEFAULT NOW(),
    is_muted BOOLEAN DEFAULT false,
    notification_settings JSONB DEFAULT '{}',
    
    UNIQUE(group_id, user_id)
);

-- Enhance messages table for group support
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES public.conversation_groups(id),
ADD COLUMN IF NOT EXISTS mentions JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS reactions JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS is_pinned BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS edited_at TIMESTAMP;

-- ============================================================================
-- INDEXES FOR NEW TABLES
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_privacy_settings_user ON public.user_privacy_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_settings_discoverable ON public.user_privacy_settings(discoverable_by_email, discoverable_by_name);

CREATE INDEX IF NOT EXISTS idx_conversation_groups_type ON public.conversation_groups(group_type);
CREATE INDEX IF NOT EXISTS idx_conversation_groups_team ON public.conversation_groups(team_id);
CREATE INDEX IF NOT EXISTS idx_conversation_groups_project ON public.conversation_groups(project_id);

CREATE INDEX IF NOT EXISTS idx_conversation_participants_group ON public.conversation_participants(group_id);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_user ON public.conversation_participants(user_id);

CREATE INDEX IF NOT EXISTS idx_messages_group_id ON public.messages(group_id);

-- ============================================================================
-- ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.user_privacy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_participants ENABLE ROW LEVEL SECURITY;

-- Drop existing policies first (only if tables exist)
DO $$
BEGIN
    -- Drop policies for existing tables only
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_privacy_settings') THEN
        DROP POLICY IF EXISTS "Users can manage their own privacy settings" ON public.user_privacy_settings;
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'conversation_groups') THEN
        DROP POLICY IF EXISTS "Users can view groups they participate in" ON public.conversation_groups;
        DROP POLICY IF EXISTS "Users can create conversation groups" ON public.conversation_groups;
        DROP POLICY IF EXISTS "Group creators can update groups" ON public.conversation_groups;
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'conversation_participants') THEN
        DROP POLICY IF EXISTS "Users can view group participants" ON public.conversation_participants;
        DROP POLICY IF EXISTS "Users can join groups" ON public.conversation_participants;
        DROP POLICY IF EXISTS "Users can update their own participation" ON public.conversation_participants;
    END IF;
END $$;

-- Privacy settings policies
CREATE POLICY "Users can manage their own privacy settings" ON public.user_privacy_settings
    FOR ALL USING (user_id = auth.uid());

-- Conversation groups policies
CREATE POLICY "Users can view groups they participate in" ON public.conversation_groups
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants
            WHERE group_id = conversation_groups.id AND user_id = auth.uid()
        ) OR created_by = auth.uid()
    );

CREATE POLICY "Users can create conversation groups" ON public.conversation_groups
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Group creators can update groups" ON public.conversation_groups
    FOR UPDATE USING (auth.uid() = created_by);

-- Conversation participants policies
CREATE POLICY "Users can view group participants" ON public.conversation_participants
    FOR SELECT USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp2
            WHERE cp2.group_id = conversation_participants.group_id AND cp2.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can join groups" ON public.conversation_participants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own participation" ON public.conversation_participants
    FOR UPDATE USING (auth.uid() = user_id);

-- ============================================================================
-- ENHANCED FUNCTIONS
-- ============================================================================

-- Function to check if two users are allies (from ally_friend_system)
CREATE OR REPLACE FUNCTION public.are_users_allies(user1_id UUID, user2_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_allies 
        WHERE ((user_id = user1_id AND ally_id = user2_id) OR 
               (user_id = user2_id AND ally_id = user1_id))
        AND status = 'accepted'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get mutual allies
CREATE OR REPLACE FUNCTION public.get_mutual_allies(user1_id UUID, user2_id UUID)
RETURNS TABLE(mutual_ally_id UUID, ally_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT 
        CASE 
            WHEN ua1.ally_id = ua2.ally_id THEN ua1.ally_id
            WHEN ua1.ally_id = ua2.user_id THEN ua1.ally_id
            WHEN ua1.user_id = ua2.ally_id THEN ua1.user_id
            ELSE ua1.user_id
        END as mutual_ally_id,
        COALESCE(u.display_name, u.email) as ally_name
    FROM public.user_allies ua1
    JOIN public.user_allies ua2 ON (
        (ua1.ally_id = ua2.ally_id AND ua1.user_id = user1_id AND ua2.user_id = user2_id) OR
        (ua1.ally_id = ua2.user_id AND ua1.user_id = user1_id AND ua2.ally_id = user2_id) OR
        (ua1.user_id = ua2.ally_id AND ua1.ally_id = user1_id AND ua2.user_id = user2_id)
    )
    JOIN auth.users u ON u.id = CASE 
        WHEN ua1.ally_id = ua2.ally_id THEN ua1.ally_id
        WHEN ua1.ally_id = ua2.user_id THEN ua1.ally_id
        WHEN ua1.user_id = ua2.ally_id THEN ua1.user_id
        ELSE ua1.user_id
    END
    WHERE ua1.status = 'accepted' AND ua2.status = 'accepted';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create default privacy settings for existing users
INSERT INTO public.user_privacy_settings (user_id)
SELECT id FROM auth.users 
WHERE id NOT IN (SELECT user_id FROM public.user_privacy_settings)
ON CONFLICT (user_id) DO NOTHING;

-- ============================================================================
-- ENHANCED NOTIFICATIONS
-- ============================================================================

-- Enhance notifications table for social features (if columns don't exist)
ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
ADD COLUMN IF NOT EXISTS category VARCHAR(50) DEFAULT 'general',
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS interaction_data JSONB DEFAULT '{}';

-- ============================================================================
-- COMMENTS
-- ============================================================================

-- ============================================================================
-- ENHANCED FEATURES FROM ALL MIGRATIONS
-- ============================================================================

-- Add file sharing system (from collaboration_system)
CREATE TABLE IF NOT EXISTS public.shared_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_url TEXT NOT NULL,
    file_hash VARCHAR(64),
    uploaded_by UUID NOT NULL REFERENCES auth.users(id),
    upload_source VARCHAR(50) DEFAULT 'direct',
    group_id UUID REFERENCES public.conversation_groups(id),
    project_id UUID REFERENCES public.projects(id),
    team_id UUID REFERENCES public.teams(id),
    description TEXT,
    tags JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    visibility VARCHAR(20) DEFAULT 'private',
    download_count INTEGER DEFAULT 0,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Add message reactions (from collaboration_system)
CREATE TABLE IF NOT EXISTS public.message_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reaction VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(message_id, user_id, reaction)
);

-- Add user achievements system (from recognition_analytics_system)
CREATE TABLE IF NOT EXISTS public.user_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_type VARCHAR(50) NOT NULL,
    achievement_level INTEGER DEFAULT 1 CHECK (achievement_level BETWEEN 1 AND 5),
    achievement_title VARCHAR(255) NOT NULL,
    achievement_description TEXT,
    criteria_met JSONB DEFAULT '{}',
    achievement_score DECIMAL(10,2) DEFAULT 0.0,
    evidence_data JSONB DEFAULT '{}',
    earned_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    is_featured BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    verified_by UUID REFERENCES auth.users(id),
    verified_at TIMESTAMP,
    verification_notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, achievement_type, achievement_level)
);

-- Add recognition rankings (from recognition_analytics_system)
CREATE TABLE IF NOT EXISTS public.recognition_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    ranking_category VARCHAR(50) NOT NULL,
    ranking_period VARCHAR(20) DEFAULT 'monthly',
    current_rank INTEGER NOT NULL,
    previous_rank INTEGER,
    rank_change INTEGER DEFAULT 0,
    score DECIMAL(10,4) NOT NULL,
    percentile DECIMAL(5,2),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_participants INTEGER DEFAULT 0,
    ranking_data JSONB DEFAULT '{}',
    calculated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, ranking_category, ranking_period, period_start)
);

-- Add enhanced collaboration metrics (from recognition_analytics_system)
CREATE TABLE IF NOT EXISTS public.collaboration_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    metric_period VARCHAR(20) DEFAULT 'monthly',
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_collaborations INTEGER DEFAULT 0,
    successful_collaborations INTEGER DEFAULT 0,
    collaboration_success_rate DECIMAL(5,2) DEFAULT 0.0,
    average_project_rating DECIMAL(3,2) DEFAULT 0.0,
    messages_sent INTEGER DEFAULT 0,
    messages_received INTEGER DEFAULT 0,
    response_time_avg INTEGER DEFAULT 0,
    files_shared INTEGER DEFAULT 0,
    new_connections INTEGER DEFAULT 0,
    total_active_connections INTEGER DEFAULT 0,
    network_growth_rate DECIMAL(5,2) DEFAULT 0.0,
    skills_endorsed INTEGER DEFAULT 0,
    endorsements_received INTEGER DEFAULT 0,
    skill_diversity_score DECIMAL(5,2) DEFAULT 0.0,
    projects_completed INTEGER DEFAULT 0,
    projects_on_time INTEGER DEFAULT 0,
    on_time_delivery_rate DECIMAL(5,2) DEFAULT 0.0,
    average_project_duration INTEGER DEFAULT 0,
    average_task_difficulty DECIMAL(3,2) DEFAULT 0.0,
    quality_score DECIMAL(5,2) DEFAULT 0.0,
    peer_rating_average DECIMAL(3,2) DEFAULT 0.0,
    activity_score DECIMAL(10,2) DEFAULT 0.0,
    contribution_frequency DECIMAL(5,2) DEFAULT 0.0,
    platform_engagement_score DECIMAL(5,2) DEFAULT 0.0,
    calculated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, metric_period, period_start)
);

-- Add notification preferences (from collaboration_system)
CREATE TABLE IF NOT EXISTS public.notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    direct_messages BOOLEAN DEFAULT TRUE,
    group_messages BOOLEAN DEFAULT TRUE,
    message_reactions BOOLEAN DEFAULT TRUE,
    friend_requests BOOLEAN DEFAULT TRUE,
    project_invitations BOOLEAN DEFAULT TRUE,
    task_assignments BOOLEAN DEFAULT TRUE,
    skill_endorsements BOOLEAN DEFAULT TRUE,
    alliance_updates BOOLEAN DEFAULT TRUE,
    alliance_invitations BOOLEAN DEFAULT TRUE,
    new_members BOOLEAN DEFAULT TRUE,
    file_shares BOOLEAN DEFAULT TRUE,
    file_comments BOOLEAN DEFAULT FALSE,
    email_notifications BOOLEAN DEFAULT TRUE,
    push_notifications BOOLEAN DEFAULT TRUE,
    in_app_notifications BOOLEAN DEFAULT TRUE,
    digest_frequency VARCHAR(20) DEFAULT 'daily',
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ============================================================================
-- ADDITIONAL INDEXES FOR NEW TABLES
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_shared_files_uploader ON public.shared_files(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_shared_files_group ON public.shared_files(group_id);
CREATE INDEX IF NOT EXISTS idx_shared_files_project ON public.shared_files(project_id);
CREATE INDEX IF NOT EXISTS idx_shared_files_team ON public.shared_files(team_id);
CREATE INDEX IF NOT EXISTS idx_shared_files_type ON public.shared_files(file_type);

CREATE INDEX IF NOT EXISTS idx_message_reactions_message ON public.message_reactions(message_id);
CREATE INDEX IF NOT EXISTS idx_message_reactions_user ON public.message_reactions(user_id);

CREATE INDEX IF NOT EXISTS idx_user_achievements_user ON public.user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_type ON public.user_achievements(achievement_type);
CREATE INDEX IF NOT EXISTS idx_user_achievements_earned ON public.user_achievements(earned_at DESC);

CREATE INDEX IF NOT EXISTS idx_recognition_rankings_user ON public.recognition_rankings(user_id);
CREATE INDEX IF NOT EXISTS idx_recognition_rankings_category ON public.recognition_rankings(ranking_category);
CREATE INDEX IF NOT EXISTS idx_recognition_rankings_rank ON public.recognition_rankings(ranking_category, current_rank);

CREATE INDEX IF NOT EXISTS idx_collaboration_metrics_user ON public.collaboration_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_metrics_period ON public.collaboration_metrics(metric_period, period_start DESC);

CREATE INDEX IF NOT EXISTS idx_notification_preferences_user ON public.notification_preferences(user_id);

-- ============================================================================
-- RLS POLICIES FOR NEW TABLES
-- ============================================================================

ALTER TABLE public.shared_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recognition_rankings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;

-- Shared files policies
CREATE POLICY "Users can view files they have access to" ON public.shared_files
    FOR SELECT USING (
        uploaded_by = auth.uid() OR
        visibility = 'public' OR
        (group_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.group_id = shared_files.group_id AND cp.user_id = auth.uid()
        ))
    );

CREATE POLICY "Users can upload files" ON public.shared_files
    FOR INSERT WITH CHECK (uploaded_by = auth.uid());

-- Message reactions policies
CREATE POLICY "Users can view reactions on messages they can see" ON public.message_reactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.messages m
            WHERE m.id = message_reactions.message_id
            AND (m.from_user_id = auth.uid() OR m.to_user_id = auth.uid() OR m.group_id IS NOT NULL)
        )
    );

CREATE POLICY "Users can add reactions" ON public.message_reactions
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- User achievements policies
CREATE POLICY "Users can view public achievements" ON public.user_achievements
    FOR SELECT USING (is_public = TRUE OR user_id = auth.uid());

CREATE POLICY "Users can manage their own achievements" ON public.user_achievements
    FOR ALL USING (user_id = auth.uid());

-- Recognition rankings policies
CREATE POLICY "Users can view all rankings" ON public.recognition_rankings
    FOR SELECT USING (TRUE);

-- Collaboration metrics policies
CREATE POLICY "Users can view their own metrics" ON public.collaboration_metrics
    FOR SELECT USING (user_id = auth.uid());

-- Notification preferences policies
CREATE POLICY "Users can manage their own notification preferences" ON public.notification_preferences
    FOR ALL USING (user_id = auth.uid());

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.user_privacy_settings IS 'User privacy controls for discovery and profile visibility';
COMMENT ON TABLE public.conversation_groups IS 'Group conversations for teams, projects, and alliances';
COMMENT ON TABLE public.conversation_participants IS 'Group membership and participation settings';
COMMENT ON TABLE public.shared_files IS 'File sharing system for collaboration and communication';
COMMENT ON TABLE public.message_reactions IS 'Emoji reactions and engagement on messages';
COMMENT ON TABLE public.user_achievements IS 'User achievements and recognition system with verification';
COMMENT ON TABLE public.recognition_rankings IS 'Leaderboards and ranking system for performance categories';
COMMENT ON TABLE public.collaboration_metrics IS 'Comprehensive collaboration performance metrics';
COMMENT ON TABLE public.notification_preferences IS 'User notification settings and preferences';
COMMENT ON FUNCTION public.are_users_allies IS 'Checks if two users are connected as allies';
COMMENT ON FUNCTION public.get_mutual_allies IS 'Returns mutual connections between two users';
