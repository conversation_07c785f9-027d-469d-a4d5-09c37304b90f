-- Migration: Error Logging System
-- Description: Comprehensive error logging and monitoring for production
-- Created: 2025-01-16
-- Integration & Services Agent

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Error logs table for application errors
CREATE TABLE IF NOT EXISTS error_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  error_id VARCHAR(255) UNIQUE NOT NULL, -- Client-generated error ID
  
  -- Error details
  message TEXT NOT NULL,
  stack_trace TEXT,
  component_stack TEXT,
  error_type VARCHAR(50) DEFAULT 'javascript', -- 'javascript', 'api', 'database', 'network'
  severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  
  -- User and session information
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  user_email VARCHAR(255),
  user_display_name VA<PERSON><PERSON><PERSON>(255),
  session_id VARCHAR(255),
  
  -- Request context
  url TEXT,
  user_agent TEXT,
  ip_address INET,
  referrer TEXT,
  
  -- <PERSON><PERSON>er and environment
  browser_info JSONB DEFAULT '{}'::jsonb,
  viewport_size JSONB DEFAULT '{}'::jsonb,
  screen_resolution JSONB DEFAULT '{}'::jsonb,
  
  -- Error context
  context JSONB DEFAULT '{}'::jsonb, -- Additional context data
  breadcrumbs JSONB DEFAULT '[]'::jsonb, -- User actions leading to error
  
  -- Resolution tracking
  resolved BOOLEAN DEFAULT false,
  resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolution_notes TEXT,
  
  -- Occurrence tracking
  occurrence_count INTEGER DEFAULT 1,
  first_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Metadata
  tags VARCHAR(255)[] DEFAULT '{}',
  fingerprint VARCHAR(255), -- For grouping similar errors
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Error patterns table for tracking recurring issues
CREATE TABLE error_patterns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  fingerprint VARCHAR(255) UNIQUE NOT NULL,
  
  -- Pattern details
  pattern_name VARCHAR(255) NOT NULL,
  error_message_pattern TEXT NOT NULL,
  stack_trace_pattern TEXT,
  
  -- Occurrence statistics
  total_occurrences INTEGER DEFAULT 0,
  unique_users INTEGER DEFAULT 0,
  first_occurrence TIMESTAMP WITH TIME ZONE,
  last_occurrence TIMESTAMP WITH TIME ZONE,
  
  -- Impact assessment
  severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  impact_score INTEGER DEFAULT 0 CHECK (impact_score >= 0 AND impact_score <= 100),
  
  -- Resolution tracking
  status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'ignored')),
  assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  resolution_priority INTEGER DEFAULT 3 CHECK (resolution_priority >= 1 AND resolution_priority <= 5),
  
  -- Pattern metadata
  affected_components VARCHAR(255)[] DEFAULT '{}',
  related_features VARCHAR(255)[] DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Error notifications table for alert management
CREATE TABLE error_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  error_log_id UUID REFERENCES error_logs(id) ON DELETE CASCADE,
  error_pattern_id UUID REFERENCES error_patterns(id) ON DELETE CASCADE,
  
  -- Notification details
  notification_type VARCHAR(50) NOT NULL, -- 'email', 'slack', 'discord', 'webhook'
  recipient VARCHAR(255) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'delivered')),
  
  -- Notification content
  subject VARCHAR(255),
  message TEXT,
  notification_data JSONB DEFAULT '{}'::jsonb,
  
  -- Delivery tracking
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  failure_reason TEXT,
  retry_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance metrics table for error correlation
CREATE TABLE error_performance_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  error_log_id UUID REFERENCES error_logs(id) ON DELETE CASCADE,
  
  -- Performance data at time of error
  page_load_time INTEGER, -- milliseconds
  memory_usage BIGINT, -- bytes
  cpu_usage DECIMAL(5,2), -- percentage
  network_latency INTEGER, -- milliseconds
  
  -- Browser performance
  dom_content_loaded INTEGER, -- milliseconds
  first_contentful_paint INTEGER, -- milliseconds
  largest_contentful_paint INTEGER, -- milliseconds
  cumulative_layout_shift DECIMAL(10,6),
  
  -- Resource metrics
  total_resources INTEGER,
  failed_resources INTEGER,
  slow_resources INTEGER, -- resources taking >2s
  
  -- JavaScript metrics
  heap_size_used BIGINT, -- bytes
  heap_size_total BIGINT, -- bytes
  script_execution_time INTEGER, -- milliseconds
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_error_logs_user_id ON error_logs(user_id);
CREATE INDEX idx_error_logs_severity ON error_logs(severity);
CREATE INDEX idx_error_logs_error_type ON error_logs(error_type);
CREATE INDEX idx_error_logs_created_at ON error_logs(created_at);
CREATE INDEX idx_error_logs_fingerprint ON error_logs(fingerprint);
CREATE INDEX idx_error_logs_resolved ON error_logs(resolved);
CREATE INDEX idx_error_logs_url ON error_logs(url);

CREATE INDEX idx_error_patterns_fingerprint ON error_patterns(fingerprint);
CREATE INDEX idx_error_patterns_severity ON error_patterns(severity);
CREATE INDEX idx_error_patterns_status ON error_patterns(status);
CREATE INDEX idx_error_patterns_last_occurrence ON error_patterns(last_occurrence);

CREATE INDEX idx_error_notifications_status ON error_notifications(status);
CREATE INDEX idx_error_notifications_type ON error_notifications(notification_type);
CREATE INDEX idx_error_notifications_created_at ON error_notifications(created_at);

CREATE INDEX idx_error_performance_error_id ON error_performance_metrics(error_log_id);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_error_logs_updated_at BEFORE UPDATE ON error_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_error_patterns_updated_at BEFORE UPDATE ON error_patterns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE error_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_performance_metrics ENABLE ROW LEVEL SECURITY;

-- RLS policies for error logs
CREATE POLICY "Admins can view all error logs" ON error_logs FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.admin_role IS NOT NULL
  )
);

CREATE POLICY "Users can view their own error logs" ON error_logs FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert error logs" ON error_logs FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can update error logs" ON error_logs FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.admin_role IS NOT NULL
  )
);

-- RLS policies for error patterns
CREATE POLICY "Admins can manage error patterns" ON error_patterns FOR ALL USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.admin_role IS NOT NULL
  )
);

-- RLS policies for error notifications
CREATE POLICY "Admins can view error notifications" ON error_notifications FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.admin_role IS NOT NULL
  )
);

CREATE POLICY "System can insert error notifications" ON error_notifications FOR INSERT WITH CHECK (true);

-- RLS policies for performance metrics
CREATE POLICY "Admins can view error performance metrics" ON error_performance_metrics FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.admin_role IS NOT NULL
  )
);

CREATE POLICY "System can insert error performance metrics" ON error_performance_metrics FOR INSERT WITH CHECK (true);

-- Functions for error management
CREATE OR REPLACE FUNCTION generate_error_fingerprint(
  error_message TEXT,
  stack_trace TEXT DEFAULT NULL
)
RETURNS VARCHAR(255) AS $$
DECLARE
  fingerprint_data TEXT;
BEGIN
  -- Create a fingerprint based on error message and stack trace
  fingerprint_data := COALESCE(error_message, '') || '|' || COALESCE(stack_trace, '');
  
  -- Return MD5 hash as fingerprint
  RETURN MD5(fingerprint_data);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_error_pattern_stats()
RETURNS TRIGGER AS $$
DECLARE
  pattern_fingerprint VARCHAR(255);
  pattern_exists BOOLEAN;
BEGIN
  -- Generate fingerprint for the error
  pattern_fingerprint := generate_error_fingerprint(NEW.message, NEW.stack_trace);
  NEW.fingerprint := pattern_fingerprint;
  
  -- Check if pattern exists
  SELECT EXISTS(SELECT 1 FROM error_patterns WHERE fingerprint = pattern_fingerprint) INTO pattern_exists;
  
  IF NOT pattern_exists THEN
    -- Create new pattern
    INSERT INTO error_patterns (
      fingerprint,
      pattern_name,
      error_message_pattern,
      stack_trace_pattern,
      total_occurrences,
      unique_users,
      first_occurrence,
      last_occurrence,
      severity
    ) VALUES (
      pattern_fingerprint,
      SUBSTRING(NEW.message FROM 1 FOR 100),
      NEW.message,
      NEW.stack_trace,
      1,
      CASE WHEN NEW.user_id IS NOT NULL THEN 1 ELSE 0 END,
      NEW.created_at,
      NEW.created_at,
      NEW.severity
    );
  ELSE
    -- Update existing pattern
    UPDATE error_patterns 
    SET 
      total_occurrences = total_occurrences + 1,
      last_occurrence = NEW.created_at,
      updated_at = NOW()
    WHERE fingerprint = pattern_fingerprint;
    
    -- Update unique users count if this is a new user for this pattern
    IF NEW.user_id IS NOT NULL AND NOT EXISTS(
      SELECT 1 FROM error_logs 
      WHERE fingerprint = pattern_fingerprint 
      AND user_id = NEW.user_id 
      AND id != NEW.id
    ) THEN
      UPDATE error_patterns 
      SET unique_users = unique_users + 1
      WHERE fingerprint = pattern_fingerprint;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update error patterns
CREATE TRIGGER update_error_pattern_stats_trigger
  BEFORE INSERT ON error_logs
  FOR EACH ROW
  EXECUTE FUNCTION update_error_pattern_stats();

-- Function to get error statistics
CREATE OR REPLACE FUNCTION get_error_statistics(
  time_range_hours INTEGER DEFAULT 24
)
RETURNS TABLE(
  total_errors BIGINT,
  unique_errors BIGINT,
  critical_errors BIGINT,
  resolved_errors BIGINT,
  top_error_types JSONB,
  error_trend JSONB
) AS $$
DECLARE
  start_time TIMESTAMP WITH TIME ZONE;
BEGIN
  start_time := NOW() - (time_range_hours || ' hours')::INTERVAL;
  
  RETURN QUERY
  SELECT 
    COUNT(*) as total_errors,
    COUNT(DISTINCT fingerprint) as unique_errors,
    COUNT(*) FILTER (WHERE severity = 'critical') as critical_errors,
    COUNT(*) FILTER (WHERE resolved = true) as resolved_errors,
    
    -- Top error types
    (SELECT jsonb_agg(
      jsonb_build_object(
        'error_type', error_type,
        'count', count
      )
    ) FROM (
      SELECT error_type, COUNT(*) as count
      FROM error_logs 
      WHERE created_at >= start_time
      GROUP BY error_type
      ORDER BY count DESC
      LIMIT 10
    ) top_types) as top_error_types,
    
    -- Error trend (hourly)
    (SELECT jsonb_agg(
      jsonb_build_object(
        'hour', hour,
        'count', count
      )
    ) FROM (
      SELECT 
        DATE_TRUNC('hour', created_at) as hour,
        COUNT(*) as count
      FROM error_logs 
      WHERE created_at >= start_time
      GROUP BY DATE_TRUNC('hour', created_at)
      ORDER BY hour
    ) trend) as error_trend
    
  FROM error_logs
  WHERE created_at >= start_time;
END;
$$ LANGUAGE plpgsql;

-- Table comments
COMMENT ON TABLE error_logs IS 'Comprehensive error logging for application monitoring and debugging';
COMMENT ON TABLE error_patterns IS 'Tracks recurring error patterns for efficient issue management';
COMMENT ON TABLE error_notifications IS 'Manages alert notifications for critical errors';
COMMENT ON TABLE error_performance_metrics IS 'Performance data correlation with errors for root cause analysis';
