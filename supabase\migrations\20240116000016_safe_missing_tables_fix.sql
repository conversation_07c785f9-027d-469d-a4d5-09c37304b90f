-- Safe Missing Tables Fix
-- This migration creates missing tables with safe column references

-- Create user_integrations table
CREATE TABLE IF NOT EXISTS user_integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    integration_type TEXT NOT NULL CHECK (integration_type IN ('github', 'gitlab', 'bitbucket', 'jira', 'slack', 'discord', 'notion', 'figma', 'linear')),
    integration_name TEXT NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP WITH TIME ZONE,
    integration_data JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'syncing', 'success', 'error')),
    sync_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, integration_type)
);

-- Create user_skills table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    level INTEGER DEFAULT 1 CHECK (level >= 1 AND level <= 5),
    proficiency_score INTEGER DEFAULT 0 CHECK (proficiency_score >= 0 AND proficiency_score <= 100),
    verified BOOLEAN DEFAULT false,
    verification_date TIMESTAMP WITH TIME ZONE,
    verification_method TEXT,
    endorsements INTEGER DEFAULT 0,
    learning_hours INTEGER DEFAULT 0,
    practice_hours INTEGER DEFAULT 0,
    project_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    skill_category TEXT,
    skill_tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- Create learning_progress table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    course_id TEXT NOT NULL,
    course_provider TEXT NOT NULL CHECK (course_provider IN ('linkedin_learning', 'youtube', 'udemy', 'coursera', 'pluralsight', 'custom')),
    course_title TEXT,
    course_url TEXT,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused')),
    completion_percentage DECIMAL(5,2) DEFAULT 0.0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    progress_data JSONB DEFAULT '{}'::jsonb,
    certificate_url TEXT,
    certificate_earned BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, course_id, course_provider)
);

-- Create user_activity_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    event_type TEXT NOT NULL,
    event_category TEXT NOT NULL,
    event_action TEXT NOT NULL,
    from_page TEXT,
    to_page TEXT,
    navigation_method TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learning_queues table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_queues (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    color TEXT DEFAULT '#3b82f6',
    icon TEXT DEFAULT 'bi-collection-play',
    is_public BOOLEAN DEFAULT false,
    auto_advance BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learning_queue_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_queue_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    queue_id UUID NOT NULL REFERENCES learning_queues(id) ON DELETE CASCADE,
    item_type TEXT NOT NULL CHECK (item_type IN ('video', 'course', 'article', 'project', 'assessment')),
    external_id TEXT,
    title TEXT NOT NULL,
    description TEXT,
    url TEXT,
    provider TEXT,
    duration_minutes INTEGER,
    difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
    skills TEXT[],
    sequence_order INTEGER DEFAULT 1,
    thumbnail_url TEXT,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'skipped')),
    progress_percentage INTEGER DEFAULT 0,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learning_content table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type TEXT NOT NULL CHECK (content_type IN ('tutorial', 'best_practice', 'documentation', 'video', 'article')),
    title TEXT NOT NULL,
    description TEXT,
    content_body TEXT,
    difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    category TEXT,
    tags TEXT[],
    vote_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create video_recommendations table if it doesn't exist
CREATE TABLE IF NOT EXISTS video_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    video_url TEXT NOT NULL,
    thumbnail_url TEXT,
    provider TEXT DEFAULT 'youtube',
    duration_minutes INTEGER,
    category TEXT,
    skills TEXT[],
    difficulty_level TEXT DEFAULT 'medium',
    recommended_by UUID REFERENCES auth.users(id),
    recommended_by_name TEXT, -- This is just a text field, not a foreign key
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT true,
    is_vetted BOOLEAN DEFAULT false,
    vetted_by UUID REFERENCES auth.users(id),
    vetted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_learning_progress table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_learning_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content_id UUID NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
    progress_percentage INTEGER DEFAULT 0,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, content_id)
);

-- Create content_votes table if it doesn't exist
CREATE TABLE IF NOT EXISTS content_votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content_id UUID NOT NULL REFERENCES learning_content(id) ON DELETE CASCADE,
    vote_type TEXT CHECK (vote_type IN ('upvote', 'downvote')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, content_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_integrations_user_id ON user_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_integrations_type ON user_integrations(integration_type);
CREATE INDEX IF NOT EXISTS idx_user_skills_user_id ON user_skills(user_id);
-- Skip user_skills name index - column doesn't exist
CREATE INDEX IF NOT EXISTS idx_learning_progress_user_id ON learning_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_status ON learning_progress(status);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_timestamp ON user_activity_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_learning_queues_user_id ON learning_queues(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_queue_items_queue_id ON learning_queue_items(queue_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON learning_content(content_type);
CREATE INDEX IF NOT EXISTS idx_video_recommendations_public ON video_recommendations(is_public) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_user_id ON user_learning_progress(user_id);

-- Enable RLS on all tables
ALTER TABLE user_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_skills ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_queues ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_queue_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_votes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
DROP POLICY IF EXISTS "Users can manage their own integrations" ON user_integrations;
CREATE POLICY "Users can manage their own integrations" ON user_integrations
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage their own skills" ON user_skills;
CREATE POLICY "Users can manage their own skills" ON user_skills
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view their own learning progress" ON learning_progress;
CREATE POLICY "Users can view their own learning progress" ON learning_progress
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view their own activity logs" ON user_activity_logs;
CREATE POLICY "Users can view their own activity logs" ON user_activity_logs
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own activity logs" ON user_activity_logs;
CREATE POLICY "Users can insert their own activity logs" ON user_activity_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage their own learning queues" ON learning_queues;
CREATE POLICY "Users can manage their own learning queues" ON learning_queues
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view public learning queues" ON learning_queues;
CREATE POLICY "Users can view public learning queues" ON learning_queues
    FOR SELECT USING (is_public = true OR auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage items in their queues" ON learning_queue_items;
CREATE POLICY "Users can manage items in their queues" ON learning_queue_items
    FOR ALL USING (
        queue_id IN (SELECT id FROM learning_queues WHERE user_id = auth.uid())
    );

DROP POLICY IF EXISTS "Anyone can view active learning content" ON learning_content;
CREATE POLICY "Anyone can view active learning content" ON learning_content
    FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Users can create learning content" ON learning_content;
CREATE POLICY "Users can create learning content" ON learning_content
    FOR INSERT WITH CHECK (auth.uid() = created_by);

DROP POLICY IF EXISTS "Anyone can view public video recommendations" ON video_recommendations;
CREATE POLICY "Anyone can view public video recommendations" ON video_recommendations
    FOR SELECT USING (is_public = true);

DROP POLICY IF EXISTS "Users can create video recommendations" ON video_recommendations;
CREATE POLICY "Users can create video recommendations" ON video_recommendations
    FOR INSERT WITH CHECK (auth.uid() = recommended_by);

DROP POLICY IF EXISTS "Users can view their own learning progress" ON user_learning_progress;
CREATE POLICY "Users can view their own learning progress" ON user_learning_progress
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage their own content votes" ON content_votes;
CREATE POLICY "Users can manage their own content votes" ON content_votes
    FOR ALL USING (auth.uid() = user_id);

-- Simple function to populate sample data for current user (safe version)
CREATE OR REPLACE FUNCTION populate_my_sample_data()
RETURNS TEXT AS $$
DECLARE
    v_user_id UUID;
    v_result TEXT := '';
BEGIN
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN 'Error: User must be authenticated to populate sample data';
    END IF;

    -- Insert sample user activity logs
    INSERT INTO user_activity_logs (user_id, session_id, event_type, event_category, event_action, from_page, to_page, navigation_method, timestamp)
    VALUES 
        (v_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_dashboard', null, '/dashboard', 'direct', NOW() - INTERVAL '2 hours'),
        (v_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_projects', '/dashboard', '/projects', 'click', NOW() - INTERVAL '1 hour'),
        (v_user_id, 'sample_session_1', 'interaction', 'click', 'created_project', '/projects', '/projects', 'click', NOW() - INTERVAL '45 minutes')
    ON CONFLICT DO NOTHING;
    v_result := v_result || 'Added activity logs. ';

    -- Insert sample learning progress
    INSERT INTO learning_progress (user_id, course_id, course_provider, course_title, status, completion_percentage, time_spent_minutes, started_at, last_accessed_at)
    VALUES 
        (v_user_id, 'react-fundamentals', 'linkedin_learning', 'React.js Essential Training', 'in_progress', 65.0, 180, NOW() - INTERVAL '7 days', NOW() - INTERVAL '1 day'),
        (v_user_id, 'javascript-advanced', 'youtube', 'Advanced JavaScript Concepts', 'completed', 100.0, 240, NOW() - INTERVAL '14 days', NOW() - INTERVAL '5 days')
    ON CONFLICT DO NOTHING;
    v_result := v_result || 'Added learning progress. ';

    -- Insert sample user skills
    INSERT INTO user_skills (user_id, name, level, proficiency_score, verified, verification_date, created_at)
    VALUES 
        (v_user_id, 'JavaScript', 3, 75, true, NOW(), NOW()),
        (v_user_id, 'React', 2, 60, false, null, NOW()),
        (v_user_id, 'CSS', 3, 80, true, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    v_result := v_result || 'Added user skills. ';

    -- Insert sample learning content
    INSERT INTO learning_content (content_type, title, description, difficulty_level, category, is_active, created_by, created_at)
    VALUES 
        ('tutorial', 'Getting Started with React', 'Learn the basics of React development', 'beginner', 'Development', true, v_user_id, NOW()),
        ('best_practice', 'Clean Code Principles', 'Best practices for writing maintainable code', 'intermediate', 'Development', true, v_user_id, NOW())
    ON CONFLICT DO NOTHING;
    v_result := v_result || 'Added learning content. ';

    RETURN 'Sample data populated successfully: ' || v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION populate_my_sample_data() TO authenticated;

-- Comments
COMMENT ON TABLE user_integrations IS 'User integrations with external services like GitHub, Slack, etc.';
COMMENT ON TABLE user_skills IS 'User skills and proficiency levels';
COMMENT ON TABLE learning_progress IS 'User progress through learning courses and content';
COMMENT ON TABLE user_activity_logs IS 'User navigation and interaction logs';
COMMENT ON TABLE learning_queues IS 'User-created learning queues for organizing educational content';
COMMENT ON TABLE learning_queue_items IS 'Individual items within learning queues';
COMMENT ON TABLE learning_content IS 'Educational content including tutorials, best practices, and documentation';
COMMENT ON TABLE video_recommendations IS 'Community-recommended educational videos';
COMMENT ON TABLE user_learning_progress IS 'User progress through learning content';
COMMENT ON TABLE content_votes IS 'User votes on learning content';
COMMENT ON FUNCTION populate_my_sample_data IS 'Populates sample data for the currently authenticated user (returns status message)';
