-- Fix permissions for revenue_entries table

-- Check if the revenue_entries table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'revenue_entries'
    ) THEN
        -- Enable Row Level Security if not already enabled
        ALTER TABLE public.revenue_entries ENABLE ROW LEVEL SECURITY;
        
        -- Drop existing policies to avoid conflicts
        DROP POLICY IF EXISTS "Project members can view revenue entries" ON public.revenue_entries;
        DROP POLICY IF EXISTS "Project admins can manage revenue entries" ON public.revenue_entries;
        
        -- Create a more permissive policy for viewing revenue entries during development
        CREATE POLICY "Anyone can view revenue entries" 
        ON public.revenue_entries FOR SELECT 
        USING (true);
        
        -- Create a policy for project members to view revenue entries
        CREATE POLICY "Project members can view revenue entries" 
        ON public.revenue_entries FOR SELECT 
        USING (
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_contributors.project_id = revenue_entries.project_id
                AND project_contributors.user_id = auth.uid()

            )
            OR
            EXISTS (
                SELECT 1 FROM public.projects
                WHERE projects.id = revenue_entries.project_id
                AND projects.created_by = auth.uid()
            )
        );
        
        -- Create a policy for project admins to manage revenue entries
        CREATE POLICY "Project admins can manage revenue entries" 
        ON public.revenue_entries FOR ALL 
        USING (
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_contributors.project_id = revenue_entries.project_id
                AND project_contributors.user_id = auth.uid()


            )
            OR
            EXISTS (
                SELECT 1 FROM public.projects
                WHERE projects.id = revenue_entries.project_id
                AND projects.created_by = auth.uid()
            )
        );
        
        -- Grant permissions to authenticated users
        GRANT SELECT, INSERT, UPDATE, DELETE ON public.revenue_entries TO authenticated;
        GRANT SELECT ON public.revenue_entries TO anon;
        
        RAISE NOTICE 'Fixed permissions for revenue_entries table';
    ELSE
        RAISE NOTICE 'revenue_entries table does not exist, skipping permission fixes';
    END IF;
END $$;
