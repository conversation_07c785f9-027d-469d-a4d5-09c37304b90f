import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Chip, Progress, Spinner,
  Tabs, Tab, Badge, Avatar, Divider
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  Brain, TrendingUp, Target, Zap, BookOpen, Users, 
  Clock, Star, Award, Lightbulb, ArrowRight, RefreshCw 
} from 'lucide-react';
import YouTubeCourseCard from './YouTubeCourseCard';

/**
 * Smart Suggestion Engine Component
 * 
 * Intelligent recommendation system that suggests learning content based on:
 * - User's gigwork activities and project context
 * - Skill gaps identified from profile and project requirements
 * - Learning history and progress patterns
 * - Community trends and popular content
 * - Career progression paths
 */
const SmartSuggestionEngine = ({ 
  currentUser: propCurrentUser,
  className = '',
  maxSuggestions = 12,
  showCategories = true 
}) => {
  const { currentUser: contextUser } = useContext(UserContext);
  const currentUser = propCurrentUser || contextUser;
  
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState('for-you');
  const [suggestions, setSuggestions] = useState({
    forYou: [],
    skillGaps: [],
    projectBased: [],
    trending: [],
    careerPath: []
  });
  const [userProfile, setUserProfile] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (currentUser) {
      loadSuggestions();
    }
  }, [currentUser]);

  const loadSuggestions = async () => {
    try {
      setLoading(true);
      
      // Load user profile and context data
      const profileData = await loadUserContext();
      
      // Generate different types of suggestions
      const [forYou, skillGaps, projectBased, trending, careerPath] = await Promise.all([
        generatePersonalizedSuggestions(profileData),
        generateSkillGapSuggestions(profileData),
        generateProjectBasedSuggestions(profileData),
        generateTrendingSuggestions(),
        generateCareerPathSuggestions(profileData)
      ]);

      setSuggestions({
        forYou,
        skillGaps,
        projectBased,
        trending,
        careerPath
      });

    } catch (error) {
      console.error('Error loading suggestions:', error);
      toast.error('Failed to load learning suggestions');
    } finally {
      setLoading(false);
    }
  };

  const loadUserContext = async () => {
    try {
      // Load user profile with skills and preferences
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', currentUser.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') throw profileError;

      // Load user's learning history
      const { data: learningHistory, error: historyError } = await supabase
        .from('learning_progress')
        .select(`
          *,
          course_catalog(skills, categories, difficulty_level)
        `)
        .eq('user_id', currentUser.id)
        .order('last_accessed_at', { ascending: false })
        .limit(50);

      if (historyError) throw historyError;

      // Load user's current projects and gigwork
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('required_skills, project_type, status')
        .or(`created_by.eq.${currentUser.id},team_members.cs.{${currentUser.id}}`)
        .eq('status', 'active');

      if (projectsError) throw projectsError;

      // Load user's skill verifications
      const { data: verifications, error: verificationsError } = await supabase
        .from('skill_verifications')
        .select('skill_name, verification_level, verified_at')
        .eq('user_id', currentUser.id)
        .eq('status', 'verified');

      if (verificationsError) throw verificationsError;

      const contextData = {
        profile: profile || {},
        learningHistory: learningHistory || [],
        projects: projects || [],
        verifiedSkills: verifications || []
      };

      setUserProfile(contextData);
      return contextData;

    } catch (error) {
      console.error('Error loading user context:', error);
      return {
        profile: {},
        learningHistory: [],
        projects: [],
        verifiedSkills: []
      };
    }
  };

  const generatePersonalizedSuggestions = async (profileData) => {
    try {
      // Analyze user's learning patterns and preferences
      const userSkills = new Set([
        ...(profileData.profile.skills || []),
        ...profileData.verifiedSkills.map(v => v.skill_name)
      ]);

      const learningCategories = new Set(
        profileData.learningHistory.flatMap(h => h.course_catalog?.categories || [])
      );

      const preferredDifficulty = calculatePreferredDifficulty(profileData.learningHistory);

      // Query for personalized content
      let query = supabase
        .from('course_catalog')
        .select('*')
        .eq('is_active', true);

      // Filter by user's skill interests and learning history
      if (userSkills.size > 0) {
        query = query.overlaps('skills', Array.from(userSkills));
      }

      if (learningCategories.size > 0) {
        query = query.overlaps('categories', Array.from(learningCategories));
      }

      if (preferredDifficulty) {
        query = query.eq('difficulty_level', preferredDifficulty);
      }

      const { data, error } = await query
        .order('rating', { ascending: false })
        .limit(maxSuggestions);

      if (error) throw error;

      return (data || []).map(item => ({
        ...item,
        reason: 'Based on your learning history and interests',
        confidence: calculateConfidenceScore(item, profileData)
      }));

    } catch (error) {
      console.error('Error generating personalized suggestions:', error);
      return [];
    }
  };

  const generateSkillGapSuggestions = async (profileData) => {
    try {
      // Identify skill gaps from current projects
      const projectSkills = new Set(
        profileData.projects.flatMap(p => p.required_skills || [])
      );

      const userSkills = new Set([
        ...(profileData.profile.skills || []),
        ...profileData.verifiedSkills.map(v => v.skill_name)
      ]);

      const skillGaps = Array.from(projectSkills).filter(skill => !userSkills.has(skill));

      if (skillGaps.length === 0) return [];

      const { data, error } = await supabase
        .from('course_catalog')
        .select('*')
        .overlaps('skills', skillGaps)
        .eq('is_active', true)
        .order('rating', { ascending: false })
        .limit(maxSuggestions);

      if (error) throw error;

      return (data || []).map(item => ({
        ...item,
        reason: 'Fill skill gaps for your current projects',
        confidence: 0.9,
        urgency: 'high'
      }));

    } catch (error) {
      console.error('Error generating skill gap suggestions:', error);
      return [];
    }
  };

  const generateProjectBasedSuggestions = async (profileData) => {
    try {
      // Suggest content based on current project types
      const projectTypes = new Set(profileData.projects.map(p => p.project_type));
      
      if (projectTypes.size === 0) return [];

      // Map project types to relevant categories
      const relevantCategories = Array.from(projectTypes).flatMap(type => {
        switch (type) {
          case 'web_development': return ['Web Development', 'Programming'];
          case 'mobile_app': return ['Mobile Development', 'Programming'];
          case 'data_analysis': return ['Data Science', 'Machine Learning'];
          case 'design': return ['Design', 'UI Design'];
          default: return ['Programming'];
        }
      });

      const { data, error } = await supabase
        .from('course_catalog')
        .select('*')
        .overlaps('categories', relevantCategories)
        .eq('is_active', true)
        .order('rating', { ascending: false })
        .limit(maxSuggestions);

      if (error) throw error;

      return (data || []).map(item => ({
        ...item,
        reason: 'Relevant to your current projects',
        confidence: 0.8
      }));

    } catch (error) {
      console.error('Error generating project-based suggestions:', error);
      return [];
    }
  };

  const generateTrendingSuggestions = async () => {
    try {
      // Get trending content based on recent enrollments and completions
      const { data, error } = await supabase
        .from('learning_content_analytics')
        .select('*')
        .gte('total_enrollments', 5)
        .order('total_enrollments', { ascending: false })
        .limit(maxSuggestions);

      if (error) throw error;

      return (data || []).map(item => ({
        ...item,
        reason: 'Trending in the community',
        confidence: 0.7,
        trending: true
      }));

    } catch (error) {
      console.error('Error generating trending suggestions:', error);
      return [];
    }
  };

  const generateCareerPathSuggestions = async (profileData) => {
    try {
      // Suggest content for career advancement
      const userSkills = profileData.profile.skills || [];
      const careerGoals = profileData.profile.career_goals || [];

      // Define career progression paths
      const careerPaths = {
        'frontend_developer': ['React', 'TypeScript', 'Next.js', 'UI Design'],
        'backend_developer': ['Node.js', 'Python', 'Database', 'API Design'],
        'fullstack_developer': ['React', 'Node.js', 'Database', 'DevOps'],
        'data_scientist': ['Python', 'Machine Learning', 'Statistics', 'SQL'],
        'devops_engineer': ['Docker', 'Kubernetes', 'AWS', 'CI/CD']
      };

      const suggestedSkills = careerGoals.flatMap(goal => careerPaths[goal] || []);
      
      if (suggestedSkills.length === 0) return [];

      const { data, error } = await supabase
        .from('course_catalog')
        .select('*')
        .overlaps('skills', suggestedSkills)
        .eq('is_active', true)
        .order('rating', { ascending: false })
        .limit(maxSuggestions);

      if (error) throw error;

      return (data || []).map(item => ({
        ...item,
        reason: 'Advance your career goals',
        confidence: 0.85,
        careerRelevant: true
      }));

    } catch (error) {
      console.error('Error generating career path suggestions:', error);
      return [];
    }
  };

  const calculatePreferredDifficulty = (learningHistory) => {
    if (learningHistory.length === 0) return 'beginner';
    
    const difficulties = learningHistory.map(h => h.course_catalog?.difficulty_level).filter(Boolean);
    const counts = difficulties.reduce((acc, diff) => {
      acc[diff] = (acc[diff] || 0) + 1;
      return acc;
    }, {});

    return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b, 'beginner');
  };

  const calculateConfidenceScore = (item, profileData) => {
    let score = 0.5; // Base score

    // Boost for skill overlap
    const userSkills = new Set([
      ...(profileData.profile.skills || []),
      ...profileData.verifiedSkills.map(v => v.skill_name)
    ]);
    
    const skillOverlap = (item.skills || []).filter(skill => userSkills.has(skill)).length;
    score += skillOverlap * 0.1;

    // Boost for appropriate difficulty
    const preferredDifficulty = calculatePreferredDifficulty(profileData.learningHistory);
    if (item.difficulty_level === preferredDifficulty) {
      score += 0.2;
    }

    // Boost for high rating
    if (item.rating >= 4.5) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  };

  const refreshSuggestions = async () => {
    setRefreshing(true);
    await loadSuggestions();
    setRefreshing(false);
    toast.success('Suggestions refreshed!');
  };

  const getSuggestionsByCategory = () => {
    switch (activeCategory) {
      case 'for-you': return suggestions.forYou;
      case 'skill-gaps': return suggestions.skillGaps;
      case 'project-based': return suggestions.projectBased;
      case 'trending': return suggestions.trending;
      case 'career-path': return suggestions.careerPath;
      default: return [];
    }
  };

  if (!currentUser) {
    return (
      <Card className={className}>
        <CardBody className="text-center p-8">
          <Brain className="w-16 h-16 mx-auto mb-4 text-default-400" />
          <h3 className="text-xl font-semibold mb-2">Smart Learning Suggestions</h3>
          <p className="text-default-600">
            Log in to get personalized learning recommendations based on your projects and goals.
          </p>
        </CardBody>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardBody className="text-center p-8">
          <Spinner size="lg" />
          <p className="mt-4 text-default-600">Analyzing your learning profile...</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`smart-suggestions ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Brain className="w-8 h-8 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">Smart Suggestions</h2>
            <p className="text-default-600">
              Personalized recommendations based on your learning journey
            </p>
          </div>
        </div>
        <Button
          variant="flat"
          onPress={refreshSuggestions}
          isLoading={refreshing}
          startContent={<RefreshCw className="w-4 h-4" />}
        >
          Refresh
        </Button>
      </div>

      {showCategories && (
        <Tabs 
          selectedKey={activeCategory} 
          onSelectionChange={setActiveCategory}
          className="mb-6"
        >
          <Tab key="for-you" title={
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              <span>For You</span>
              {suggestions.forYou.length > 0 && (
                <Badge content={suggestions.forYou.length} size="sm" color="primary" />
              )}
            </div>
          } />
          <Tab key="skill-gaps" title={
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              <span>Skill Gaps</span>
              {suggestions.skillGaps.length > 0 && (
                <Badge content={suggestions.skillGaps.length} size="sm" color="warning" />
              )}
            </div>
          } />
          <Tab key="project-based" title={
            <div className="flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              <span>Project Based</span>
              {suggestions.projectBased.length > 0 && (
                <Badge content={suggestions.projectBased.length} size="sm" color="secondary" />
              )}
            </div>
          } />
          <Tab key="trending" title={
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              <span>Trending</span>
              {suggestions.trending.length > 0 && (
                <Badge content={suggestions.trending.length} size="sm" color="success" />
              )}
            </div>
          } />
          <Tab key="career-path" title={
            <div className="flex items-center gap-2">
              <Award className="w-4 h-4" />
              <span>Career Path</span>
              {suggestions.careerPath.length > 0 && (
                <Badge content={suggestions.careerPath.length} size="sm" color="danger" />
              )}
            </div>
          } />
        </Tabs>
      )}

      {/* Suggestions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {getSuggestionsByCategory().map((suggestion, index) => (
          <motion.div
            key={suggestion.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <YouTubeCourseCard
              video={suggestion}
              showEnrollButton={true}
              showVettingInfo={true}
            />
            
            {/* Suggestion Reason */}
            <div className="mt-2 p-2 bg-default-50 rounded-lg">
              <div className="flex items-center gap-2 text-sm">
                <Lightbulb className="w-4 h-4 text-warning" />
                <span className="text-default-700">{suggestion.reason}</span>
              </div>
              {suggestion.confidence && (
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs text-default-500">Confidence:</span>
                  <Progress 
                    value={suggestion.confidence * 100} 
                    size="sm" 
                    color="primary"
                    className="flex-1"
                  />
                  <span className="text-xs text-default-500">
                    {Math.round(suggestion.confidence * 100)}%
                  </span>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {getSuggestionsByCategory().length === 0 && (
        <div className="text-center py-12">
          <Brain className="w-16 h-16 mx-auto mb-4 text-default-400" />
          <h3 className="text-xl font-semibold mb-2">No suggestions available</h3>
          <p className="text-default-600 mb-4">
            Complete your profile and start learning to get personalized recommendations.
          </p>
          <Button color="primary" onPress={refreshSuggestions}>
            Refresh Suggestions
          </Button>
        </div>
      )}
    </div>
  );
};

export default SmartSuggestionEngine;
