import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Chip, Progress, Avatar, Divider,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure,
  Tabs, Tab, Badge, Tooltip
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  BookOpen, Clock, User, Calendar, Star, Heart, Bookmark, 
  Share2, Eye, CheckCircle, ArrowLeft, ArrowRight, ExternalLink,
  Award, Target, TrendingUp, Users
} from 'lucide-react';

/**
 * Content Viewer Component
 * 
 * Comprehensive viewer for all types of learning content:
 * - Articles with rich text and media
 * - Tutorials with step-by-step instructions
 * - Guides with structured information
 * - External embedded content
 * - Royaltea-specific platform guides
 */
const ContentViewer = ({ contentId, onClose, showNavigation = true }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [content, setContent] = useState(null);
  const [progress, setProgress] = useState(null);
  const [interactions, setInteractions] = useState({});
  const [relatedContent, setRelatedContent] = useState([]);
  const [activeSection, setActiveSection] = useState(0);

  useEffect(() => {
    if (contentId) {
      loadContent();
      if (currentUser) {
        loadUserProgress();
        loadUserInteractions();
      }
    }
  }, [contentId, currentUser]);

  const loadContent = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('learning_content')
        .select(`
          *,
          category:content_categories(name, slug, icon, color),
          series:content_series(title, slug, total_parts),
          author:auth.users!author_id(user_metadata)
        `)
        .eq('id', contentId)
        .eq('status', 'published')
        .single();

      if (error) throw error;

      setContent(data);

      // Track view
      if (currentUser) {
        await supabase
          .from('content_interactions')
          .upsert({
            user_id: currentUser.id,
            content_id: contentId,
            interaction_type: 'view'
          }, { onConflict: 'user_id,content_id,interaction_type' });
      }

      // Load related content
      loadRelatedContent(data);

    } catch (error) {
      console.error('Error loading content:', error);
      toast.error('Failed to load content');
    } finally {
      setLoading(false);
    }
  };

  const loadUserProgress = async () => {
    try {
      const { data, error } = await supabase
        .from('content_progress')
        .select('*')
        .eq('user_id', currentUser.id)
        .eq('content_id', contentId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      setProgress(data);
    } catch (error) {
      console.error('Error loading progress:', error);
    }
  };

  const loadUserInteractions = async () => {
    try {
      const { data, error } = await supabase
        .from('content_interactions')
        .select('interaction_type, rating')
        .eq('user_id', currentUser.id)
        .eq('content_id', contentId);

      if (error) throw error;

      const interactionMap = {};
      data?.forEach(interaction => {
        interactionMap[interaction.interaction_type] = interaction.rating || true;
      });

      setInteractions(interactionMap);
    } catch (error) {
      console.error('Error loading interactions:', error);
    }
  };

  const loadRelatedContent = async (currentContent) => {
    try {
      let query = supabase
        .from('learning_content')
        .select('id, title, slug, description, thumbnail_url, difficulty_level, estimated_read_time_minutes, vetting_level')
        .eq('status', 'published')
        .neq('id', contentId)
        .limit(6);

      // Find related content by skills or category
      if (currentContent.skills_covered?.length > 0) {
        query = query.overlaps('skills_covered', currentContent.skills_covered);
      } else if (currentContent.category_id) {
        query = query.eq('category_id', currentContent.category_id);
      }

      const { data, error } = await query;

      if (error) throw error;

      setRelatedContent(data || []);
    } catch (error) {
      console.error('Error loading related content:', error);
    }
  };

  const handleInteraction = async (type, value = null) => {
    if (!currentUser) {
      toast.error('Please log in to interact with content');
      return;
    }

    try {
      const interactionData = {
        user_id: currentUser.id,
        content_id: contentId,
        interaction_type: type
      };

      if (type === 'rating' && value) {
        interactionData.rating = value;
      }

      if (interactions[type] && type !== 'rating') {
        // Remove interaction
        await supabase
          .from('content_interactions')
          .delete()
          .eq('user_id', currentUser.id)
          .eq('content_id', contentId)
          .eq('interaction_type', type);

        setInteractions(prev => ({ ...prev, [type]: false }));
        toast.success(`${type === 'like' ? 'Like' : 'Bookmark'} removed`);
      } else {
        // Add interaction
        await supabase
          .from('content_interactions')
          .upsert(interactionData, { onConflict: 'user_id,content_id,interaction_type' });

        setInteractions(prev => ({ ...prev, [type]: value || true }));
        toast.success(`${type === 'like' ? 'Liked' : type === 'bookmark' ? 'Bookmarked' : 'Rated'}!`);
      }

      // Reload content to update metrics
      loadContent();
    } catch (error) {
      console.error('Error handling interaction:', error);
      toast.error('Failed to update interaction');
    }
  };

  const updateProgress = async (percentage, section = null) => {
    if (!currentUser) return;

    try {
      const progressData = {
        user_id: currentUser.id,
        content_id: contentId,
        progress_percentage: percentage,
        current_section: section,
        last_accessed_at: new Date().toISOString()
      };

      if (percentage === 100) {
        progressData.status = 'completed';
        progressData.completed_at = new Date().toISOString();
        
        // Track completion interaction
        await supabase
          .from('content_interactions')
          .upsert({
            user_id: currentUser.id,
            content_id: contentId,
            interaction_type: 'completion'
          }, { onConflict: 'user_id,content_id,interaction_type' });
      } else if (percentage > 0) {
        progressData.status = 'in_progress';
        if (!progress?.started_at) {
          progressData.started_at = new Date().toISOString();
        }
      }

      const { data, error } = await supabase
        .from('content_progress')
        .upsert(progressData, { onConflict: 'user_id,content_id' })
        .select()
        .single();

      if (error) throw error;

      setProgress(data);

      if (percentage === 100) {
        toast.success('Content completed! 🎉');
      }
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  const getVettingLevelInfo = (level) => {
    const levels = {
      0: { name: 'Unverified', color: 'default', icon: '⚪' },
      1: { name: 'Learning', color: 'warning', icon: '🟡' },
      2: { name: 'Peer Verified', color: 'secondary', icon: '🟠' },
      3: { name: 'Project Verified', color: 'success', icon: '🟢' },
      4: { name: 'Expert Verified', color: 'primary', icon: '🔵' },
      5: { name: 'Master Verified', color: 'danger', icon: '🟣' }
    };
    return levels[level] || levels[0];
  };

  const getDifficultyColor = (difficulty) => {
    const colors = {
      'beginner': 'success',
      'intermediate': 'warning', 
      'advanced': 'danger',
      'expert': 'secondary'
    };
    return colors[difficulty] || 'default';
  };

  const renderContentBody = () => {
    if (!content?.content_body) return null;

    // For now, render as HTML (in production, you'd want proper sanitization)
    return (
      <div 
        className="prose prose-lg max-w-none dark:prose-invert"
        dangerouslySetInnerHTML={{ __html: content.content_body }}
      />
    );
  };

  const renderTableOfContents = () => {
    if (!content?.table_of_contents || content.table_of_contents.length === 0) return null;

    return (
      <Card className="mb-6">
        <CardHeader>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <BookOpen className="w-5 h-5" />
            Table of Contents
          </h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-2">
            {content.table_of_contents.map((section, index) => (
              <button
                key={index}
                className={`text-left w-full p-2 rounded-lg transition-colors ${
                  activeSection === index 
                    ? 'bg-primary/10 text-primary font-medium' 
                    : 'hover:bg-default-100'
                }`}
                onClick={() => setActiveSection(index)}
              >
                {section.title}
              </button>
            ))}
          </div>
        </CardBody>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading content...</p>
        </div>
      </div>
    );
  }

  if (!content) {
    return (
      <div className="text-center py-12">
        <BookOpen className="w-16 h-16 mx-auto mb-4 text-default-400" />
        <h3 className="text-xl font-semibold mb-2">Content not found</h3>
        <p className="text-default-600">The requested content could not be found.</p>
      </div>
    );
  }

  return (
    <div className="content-viewer max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-3">
              {content.category && (
                <Chip 
                  color="primary" 
                  variant="flat" 
                  size="sm"
                  startContent={<span>{content.category.icon}</span>}
                >
                  {content.category.name}
                </Chip>
              )}
              
              <Chip 
                color={getDifficultyColor(content.difficulty_level)} 
                variant="flat" 
                size="sm"
              >
                {content.difficulty_level}
              </Chip>

              {content.vetting_level && (
                <Chip 
                  color={getVettingLevelInfo(content.vetting_level).color}
                  variant="flat" 
                  size="sm"
                  startContent={<span>{getVettingLevelInfo(content.vetting_level).icon}</span>}
                >
                  {getVettingLevelInfo(content.vetting_level).name}
                </Chip>
              )}
            </div>

            <h1 className="text-3xl font-bold mb-4">{content.title}</h1>
            
            {content.description && (
              <p className="text-lg text-default-600 mb-4">{content.description}</p>
            )}

            {/* Metadata */}
            <div className="flex items-center gap-6 text-sm text-default-500">
              {content.author && (
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <span>{content.author_name || content.author.user_metadata?.full_name || 'Unknown Author'}</span>
                </div>
              )}
              
              {content.estimated_read_time_minutes && (
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>{content.estimated_read_time_minutes} min read</span>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{new Date(content.published_at || content.created_at).toLocaleDateString()}</span>
              </div>

              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                <span>{content.view_count || 0} views</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2 ml-4">
            <Button
              size="sm"
              variant={interactions.like ? "solid" : "flat"}
              color={interactions.like ? "danger" : "default"}
              startContent={<Heart className="w-4 h-4" />}
              onClick={() => handleInteraction('like')}
            >
              {content.like_count || 0}
            </Button>

            <Button
              size="sm"
              variant={interactions.bookmark ? "solid" : "flat"}
              color={interactions.bookmark ? "primary" : "default"}
              startContent={<Bookmark className="w-4 h-4" />}
              onClick={() => handleInteraction('bookmark')}
            >
              {interactions.bookmark ? 'Saved' : 'Save'}
            </Button>

            <Button
              size="sm"
              variant="flat"
              startContent={<Share2 className="w-4 h-4" />}
              onClick={() => {
                navigator.clipboard.writeText(window.location.href);
                toast.success('Link copied to clipboard!');
              }}
            >
              Share
            </Button>

            {onClose && (
              <Button
                size="sm"
                variant="flat"
                startContent={<ArrowLeft className="w-4 h-4" />}
                onClick={onClose}
              >
                Back
              </Button>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        {currentUser && progress && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-default-600">{progress.progress_percentage}%</span>
            </div>
            <Progress 
              value={progress.progress_percentage} 
              color="primary" 
              className="mb-2"
            />
            {progress.status === 'completed' && (
              <div className="flex items-center gap-2 text-success text-sm">
                <CheckCircle className="w-4 h-4" />
                <span>Completed on {new Date(progress.completed_at).toLocaleDateString()}</span>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Table of Contents */}
        <div className="lg:col-span-1">
          {renderTableOfContents()}
          
          {/* Skills Covered */}
          {content.skills_covered && content.skills_covered.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Skills Covered
                </h3>
              </CardHeader>
              <CardBody>
                <div className="flex flex-wrap gap-2">
                  {content.skills_covered.map((skill, index) => (
                    <Chip key={index} size="sm" variant="flat" color="primary">
                      {skill}
                    </Chip>
                  ))}
                </div>
              </CardBody>
            </Card>
          )}
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardBody className="p-8">
              {content.featured_image_url && (
                <img
                  src={content.featured_image_url}
                  alt={content.title}
                  className="w-full h-64 object-cover rounded-lg mb-8"
                />
              )}

              {renderContentBody()}

              {/* Learning Objectives */}
              {content.learning_objectives && content.learning_objectives.length > 0 && (
                <div className="mt-8 p-6 bg-primary/5 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Award className="w-5 h-5 text-primary" />
                    Learning Objectives
                  </h3>
                  <ul className="space-y-2">
                    {content.learning_objectives.map((objective, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-success mt-1 flex-shrink-0" />
                        <span>{objective}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Mark as Complete Button */}
              {currentUser && progress?.progress_percentage !== 100 && (
                <div className="mt-8 text-center">
                  <Button
                    color="success"
                    size="lg"
                    startContent={<CheckCircle className="w-5 h-5" />}
                    onClick={() => updateProgress(100)}
                  >
                    Mark as Complete
                  </Button>
                </div>
              )}
            </CardBody>
          </Card>

          {/* Related Content */}
          {relatedContent.length > 0 && (
            <Card className="mt-8">
              <CardHeader>
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Related Content
                </h3>
              </CardHeader>
              <CardBody>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {relatedContent.map((item) => (
                    <Card key={item.id} className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardBody className="p-4">
                        <h4 className="font-semibold mb-2 line-clamp-2">{item.title}</h4>
                        <p className="text-sm text-default-600 mb-3 line-clamp-2">{item.description}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Chip size="sm" variant="flat" color={getDifficultyColor(item.difficulty_level)}>
                              {item.difficulty_level}
                            </Chip>
                            {item.vetting_level && (
                              <Chip size="sm" variant="flat" color={getVettingLevelInfo(item.vetting_level).color}>
                                {getVettingLevelInfo(item.vetting_level).icon}
                              </Chip>
                            )}
                          </div>
                          {item.estimated_read_time_minutes && (
                            <span className="text-xs text-default-500">
                              {item.estimated_read_time_minutes} min
                            </span>
                          )}
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </CardBody>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContentViewer;
