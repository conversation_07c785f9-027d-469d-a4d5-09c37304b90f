import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright configuration specifically for AI Visual Testing with Applitools
 * This configuration is optimized for visual regression testing with limited concurrent tests
 */

// Environment-specific configurations are handled within the main config
const config = defineConfig({
  testDir: './tests',

  // Only run comprehensive flow tests
  testMatch: '**/e2e-comprehensive-*.spec.js',

  // Limit concurrent tests to stay within Applitools limits (5 concurrent)
  fullyParallel: false,
  workers: 1, // Single worker to ensure exactly 5 tests run sequentially

  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,

  // Retry on CI only
  retries: process.env.CI ? 3 : 0,

  // Reporter configuration for visual tests
  reporter: [
    ['html', { outputFolder: 'playwright-report-visual' }],
    ['json', { outputFile: 'test-results-visual.json' }],
    ['junit', { outputFile: 'test-results-visual.xml' }]
  ],

  // Global test settings optimized for visual testing
  use: {
    // Base URL for tests
    baseURL: 'http://localhost:5173',

    // Collect trace when retrying the failed test
    trace: 'on-first-retry',

    // Take screenshot on failure
    screenshot: 'only-on-failure',

    // Record video on failure
    video: 'retain-on-failure',

    // Ignore HTTPS errors
    ignoreHTTPSErrors: true,

    // Wait for network to be idle before proceeding
    waitForLoadState: 'networkidle',

    // Increase timeout for visual tests (they can be slower)
    actionTimeout: process.env.CI ? 20000 : (process.env.NODE_ENV === 'development' ? 10000 : 15000),
    navigationTimeout: process.env.CI ? 45000 : (process.env.NODE_ENV === 'development' ? 20000 : 30000),

    // Viewport settings for consistent visual testing
    viewport: { width: 1200, height: 800 },

    // Disable animations for consistent visual testing
    reducedMotion: 'reduce',

    // Force color scheme for consistent testing
    colorScheme: 'light',

    // Show browser in development mode
    headless: process.env.NODE_ENV === 'development' ? false : true,
    slowMo: process.env.NODE_ENV === 'development' ? 500 : 0,

    // Locale for consistent testing
    locale: 'en-US',
    timezoneId: 'America/New_York'
  },

  // Test timeout increased for visual tests
  timeout: 60000,
  expect: {
    timeout: 10000
  },

  // Single project configuration to ensure exactly 5 tests total
  projects: [
    {
      name: 'Comprehensive Visual Tests - 5 Tests Only',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1200, height: 800 }
      },
    }
  ],

  // Web server configuration
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    cwd: './client',
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
    stdout: 'ignore',
    stderr: 'pipe'
  },

  // Global setup and teardown
  globalSetup: './tests/global-setup.js',
  globalTeardown: './tests/global-teardown.js'
});

export default config;
