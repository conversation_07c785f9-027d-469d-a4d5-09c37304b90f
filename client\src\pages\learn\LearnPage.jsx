import React, { useState, useContext, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Tabs, Tab, Progress, Chip, Input, Select, SelectItem, Spinner, useDisclosure } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { supabase } from '../../utils/supabase/supabase.utils';
import { Search, Filter, BookOpen, Play, Users, Star, TrendingUp, Award, Shield } from 'lucide-react';

// Import learning components
import YouTubeCourseCard from '../../components/learning/YouTubeCourseCard';
import VideoSubmissionForm from '../../components/learning/VideoSubmissionForm';
import LearningQueue from '../../components/learning/LearningQueue';
import SmartSuggestionEngine from '../../components/learning/SmartSuggestionEngine';
import SkillVerificationDashboard from '../../components/vetting/SkillVerificationDashboard';
import VettingLearningIntegration from '../../components/learning/VettingLearningIntegration';

const LearnPage = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('discover');
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [displayLimit, setDisplayLimit] = useState(8);

  // Data states
  const [learningContent, setLearningContent] = useState({
    videos: [],
    learningPaths: [],
    userProgress: [],
    recommendations: []
  });

  // Modal states
  const { isOpen: isSubmissionOpen, onOpen: onSubmissionOpen, onClose: onSubmissionClose } = useDisclosure();
  const { isOpen: isQueueOpen, onOpen: onQueueOpen, onClose: onQueueClose } = useDisclosure();

  // Load learning data
  useEffect(() => {
    if (currentUser) {
      loadLearningData();
    }
  }, [currentUser]);

  const loadLearningData = async () => {
    try {
      setLoading(true);

      // Load course catalog (vetted videos) with better error handling
      const { data: videos, error: videosError } = await supabase
        .from('course_catalog')
        .select(`
          *,
          learning_progress:learning_progress!learning_progress_course_id_fkey(
            completion_percentage,
            status,
            started_at,
            completed_at
          )
        `)
        .eq('provider', 'youtube')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(50);

      if (videosError) {
        console.error('Error loading videos:', videosError);
        // Continue with empty array rather than failing completely
      }

      // Load learning paths with error handling
      const { data: learningPaths, error: pathsError } = await supabase
        .from('learning_paths')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (pathsError) {
        console.error('Error loading learning paths:', pathsError);
      }

      // Load user progress with error handling
      const { data: userProgress, error: progressError } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', currentUser.id);

      if (progressError) {
        console.error('Error loading user progress:', progressError);
      }

      // Load video recommendations with error handling
      const { data: recommendations, error: recError } = await supabase
        .from('video_recommendations')
        .select('*')
        .eq('is_public', true)
        .order('upvotes', { ascending: false })
        .limit(20);

      if (recError) {
        console.error('Error loading recommendations:', recError);
      }

      // Set data even if some queries failed
      setLearningContent({
        videos: videos || [],
        learningPaths: learningPaths || [],
        userProgress: userProgress || [],
        recommendations: recommendations || []
      });

      // Only show error if all critical data failed to load
      if (videosError && pathsError && recError) {
        toast.error('Failed to load learning content. Please try again.');
      } else if (videosError || pathsError || recError) {
        toast.error('Some content failed to load, but you can still browse available materials.');
      }

    } catch (error) {
      console.error('Unexpected error loading learning data:', error);
      toast.error('An unexpected error occurred. Please refresh the page.');

      // Set empty state to prevent crashes
      setLearningContent({
        videos: [],
        learningPaths: [],
        userProgress: [],
        recommendations: []
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle video enrollment with comprehensive error handling
  const handleVideoEnroll = async (video) => {
    if (!video || !video.external_id) {
      toast.error('Invalid video data');
      return;
    }

    try {
      // Check if already enrolled
      const { data: existingProgress, error: checkError } = await supabase
        .from('learning_progress')
        .select('id')
        .eq('user_id', currentUser.id)
        .eq('course_id', video.external_id)
        .eq('course_provider', 'youtube')
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existingProgress) {
        toast.error('You are already enrolled in this video');
        return;
      }

      const { error } = await supabase
        .from('learning_progress')
        .insert({
          user_id: currentUser.id,
          course_id: video.external_id,
          course_provider: 'youtube',
          status: 'in_progress',
          started_at: new Date().toISOString(),
          completion_percentage: 0,
          time_spent_minutes: 0
        });

      if (error) throw error;

      toast.success(`Enrolled in "${video.title}"`);
      loadLearningData(); // Refresh data
    } catch (error) {
      console.error('Error enrolling in video:', error);

      if (error.code === '23505') {
        toast.error('You are already enrolled in this video');
      } else if (error.code === '23503') {
        toast.error('Video not found in catalog');
      } else {
        toast.error('Failed to enroll in video. Please try again.');
      }

      throw error;
    }
  };

  // Handle video progress with validation and error handling
  const handleVideoProgress = async (progressData) => {
    if (!progressData || !progressData.videoId || progressData.percentage < 0 || progressData.percentage > 100) {
      console.error('Invalid progress data:', progressData);
      return;
    }

    try {
      const { error } = await supabase
        .from('learning_progress')
        .upsert({
          user_id: currentUser.id,
          course_id: progressData.videoId,
          course_provider: 'youtube',
          completion_percentage: Math.min(100, Math.max(0, progressData.percentage)),
          time_spent_minutes: Math.max(0, progressData.timeSpent || 0),
          last_accessed_at: new Date().toISOString(),
          status: progressData.percentage >= 100 ? 'completed' : 'in_progress'
        }, {
          onConflict: 'user_id,course_id,course_provider'
        });

      if (error) throw error;

      // Auto-complete if reaching 100%
      if (progressData.percentage >= 100) {
        await handleVideoComplete(progressData.videoId, false); // Don't show toast again
      }
    } catch (error) {
      console.error('Error updating progress:', error);
      // Don't show user error for progress updates as they happen frequently
    }
  };

  // Handle video completion with better feedback
  const handleVideoComplete = async (videoId, showToast = true) => {
    if (!videoId) {
      console.error('Invalid video ID for completion');
      return;
    }

    try {
      const { error } = await supabase
        .from('learning_progress')
        .update({
          completion_percentage: 100,
          status: 'completed',
          completed_at: new Date().toISOString(),
          last_accessed_at: new Date().toISOString()
        })
        .eq('user_id', currentUser.id)
        .eq('course_id', videoId)
        .eq('course_provider', 'youtube');

      if (error) throw error;

      if (showToast) {
        toast.success('Video completed! 🎉 Great job!');
      }

      // Refresh data to update UI
      loadLearningData();
    } catch (error) {
      console.error('Error completing video:', error);
      if (showToast) {
        toast.error('Failed to mark video as completed');
      }
    }
  };

  // Handle video sharing
  const handleVideoShare = (video) => {
    const shareData = {
      title: `Learning Video: ${video.title}`,
      text: `Check out this educational video on Royaltea: ${video.title}`,
      url: `${window.location.origin}/learn?video=${video.external_id}`
    };

    if (navigator.share) {
      navigator.share(shareData);
    } else {
      navigator.clipboard.writeText(`${shareData.text} - ${shareData.url}`);
      toast.success('Video link copied to clipboard!');
    }
  };

  // Handle learning path start
  const handleStartLearningPath = async (pathId) => {
    if (!currentUser) {
      toast.error('Please log in to start learning paths');
      return;
    }

    try {
      // Check if user is already enrolled in this path
      const { data: existingEnrollment, error: checkError } = await supabase
        .from('learning_path_enrollments')
        .select('id, progress')
        .eq('user_id', currentUser.id)
        .eq('learning_path_id', pathId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existingEnrollment) {
        toast.success('Continuing your learning path!');
        return;
      }

      // Create new enrollment
      const { error: enrollError } = await supabase
        .from('learning_path_enrollments')
        .insert({
          user_id: currentUser.id,
          learning_path_id: pathId,
          enrolled_at: new Date().toISOString(),
          progress: 0,
          status: 'active'
        });

      if (enrollError) throw enrollError;

      toast.success('Learning path started successfully!');
      loadLearningData(); // Refresh data
    } catch (error) {
      console.error('Error starting learning path:', error);
      toast.error('Failed to start learning path');
    }
  };

  // Filter videos based on search and filters
  const filteredVideos = learningContent.videos.filter(video => {
    const matchesSearch = !searchTerm ||
      video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      video.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      video.skills?.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' ||
      video.categories?.includes(selectedCategory) ||
      video.skills?.includes(selectedCategory);

    const matchesDifficulty = selectedDifficulty === 'all' ||
      video.difficulty_level === selectedDifficulty;

    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  // Get user's enrollment status for a video
  const isVideoEnrolled = (videoId) => {
    return learningContent.userProgress.some(
      progress => progress.course_id === videoId && progress.course_provider === 'youtube'
    );
  };

  // Get user's progress for a video
  const getVideoProgress = (videoId) => {
    const progress = learningContent.userProgress.find(
      p => p.course_id === videoId && p.course_provider === 'youtube'
    );
    return progress?.completion_percentage || 0;
  };

  // Get categories for filter
  const categories = [...new Set(
    learningContent.videos.flatMap(video => video.categories || [])
  )];

  if (!currentUser) {
    return (
      <div className="learn-page min-h-screen bg-background flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardBody className="text-center p-8">
            <BookOpen className="w-16 h-16 mx-auto mb-4 text-primary" />
            <h2 className="text-2xl font-bold mb-2">Learning Center</h2>
            <p className="text-default-600 mb-4">
              Please log in to access the learning center and track your progress.
            </p>
            <Button color="primary" onPress={() => navigate('/login')}>
              Log In
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="learn-page min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">Learning Center</h1>
          <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
            Enhance your skills with our comprehensive learning platform featuring vetted content,
            personalized learning paths, and community-driven recommendations.
          </p>
        </div>

        {/* Action Bar */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1">
            <Input
              placeholder="Search videos, skills, or topics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              startContent={<Search className="w-4 h-4 text-default-400" />}
              className="w-full"
            />
          </div>
          <div className="flex gap-2">
            <Select
              placeholder="Category"
              selectedKeys={selectedCategory !== 'all' ? [selectedCategory] : []}
              onSelectionChange={(keys) => setSelectedCategory(Array.from(keys)[0] || 'all')}
              className="w-40"
            >
              <SelectItem key="all">All Categories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category}>{category}</SelectItem>
              ))}
            </Select>
            <Select
              placeholder="Difficulty"
              selectedKeys={selectedDifficulty !== 'all' ? [selectedDifficulty] : []}
              onSelectionChange={(keys) => setSelectedDifficulty(Array.from(keys)[0] || 'all')}
              className="w-40"
            >
              <SelectItem key="all">All Levels</SelectItem>
              <SelectItem key="beginner">Beginner</SelectItem>
              <SelectItem key="intermediate">Intermediate</SelectItem>
              <SelectItem key="advanced">Advanced</SelectItem>
            </Select>
            <Button
              color="primary"
              variant="flat"
              onPress={onSubmissionOpen}
              startContent={<Play className="w-4 h-4" />}
            >
              Submit Video
            </Button>
            <Button
              color="secondary"
              variant="flat"
              onPress={onQueueOpen}
              startContent={<BookOpen className="w-4 h-4" />}
            >
              My Queues
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <Tabs
          selectedKey={activeTab}
          onSelectionChange={setActiveTab}
          className="w-full"
          classNames={{
            tabList: "grid w-full grid-cols-5",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
            tabContent: "group-data-[selected=true]:text-primary-foreground"
          }}
        >
          <Tab key="discover" title={
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4" />
              <span>Discover</span>
            </div>
          }>
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Spinner size="lg" />
              </div>
            ) : (
              <div className="space-y-8">
                {/* Smart Suggestions */}
                <SmartSuggestionEngine
                  currentUser={currentUser}
                  maxSuggestions={8}
                  showCategories={true}
                />

                {/* Featured Content */}
                <div>
                  <h2 className="text-2xl font-bold mb-4">All Learning Content</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredVideos.slice(0, displayLimit).map((video) => (
                      <YouTubeCourseCard
                        key={video.id}
                        video={video}
                        progress={getVideoProgress(video.external_id)}
                        isEnrolled={isVideoEnrolled(video.external_id)}
                        onEnroll={handleVideoEnroll}
                        onProgress={handleVideoProgress}
                        onComplete={handleVideoComplete}
                        onShare={handleVideoShare}
                        showVettingInfo={true}
                      />
                    ))}
                  </div>

                  {filteredVideos.length === 0 && (
                    <div className="text-center py-12">
                      <BookOpen className="w-16 h-16 mx-auto mb-4 text-default-400" />
                      <h3 className="text-xl font-semibold mb-2">No content found</h3>
                      <p className="text-default-600 mb-4">
                        Try adjusting your search or filters, or be the first to submit content!
                      </p>
                      <Button color="primary" onPress={onSubmissionOpen}>
                        Submit a Video
                      </Button>
                    </div>
                  )}
                </div>

                {/* Show more button */}
                {filteredVideos.length > displayLimit && (
                  <div className="text-center">
                    <Button
                      color="primary"
                      variant="flat"
                      onPress={() => setDisplayLimit(prev => prev + 8)}
                    >
                      Load More Content
                    </Button>
                  </div>
                )}
              </div>
            )}
          </Tab>

          <Tab key="paths" title={
            <div className="flex items-center space-x-2">
              <BookOpen className="w-4 h-4" />
              <span>Learning Paths</span>
            </div>
          }>
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold mb-2">Official Learning Paths</h2>
                <p className="text-default-600">
                  Structured learning sequences curated by our team and community experts.
                </p>
              </div>

              {learningContent.learningPaths.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {learningContent.learningPaths.map((path) => (
                    <Card key={path.id} className="hover:shadow-lg transition-shadow">
                      <CardBody className="p-6">
                        <div className="flex items-start gap-4">
                          <div className="bg-primary/10 p-3 rounded-lg">
                            <BookOpen className="w-6 h-6 text-primary" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg mb-2">{path.title}</h3>
                            <p className="text-default-600 text-sm mb-4 line-clamp-3">
                              {path.description}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-default-500 mb-4">
                              <span>{path.estimated_duration_hours}h</span>
                              <span>{path.difficulty_level}</span>
                            </div>
                            <div className="flex flex-wrap gap-1 mb-4">
                              {path.skills?.slice(0, 3).map((skill, index) => (
                                <Chip key={index} size="sm" variant="flat" color="primary">
                                  {skill}
                                </Chip>
                              ))}
                            </div>
                            <Button
                              color="primary"
                              size="sm"
                              className="w-full"
                              onPress={() => handleStartLearningPath(path.id)}
                            >
                              Start Learning Path
                            </Button>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <BookOpen className="w-16 h-16 mx-auto mb-4 text-default-400" />
                  <h3 className="text-xl font-semibold mb-2">No learning paths yet</h3>
                  <p className="text-default-600">
                    Learning paths are being created. Check back soon!
                  </p>
                </div>
              )}
            </div>
          </Tab>

          <Tab key="community" title={
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>Community</span>
            </div>
          }>
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold mb-2">Community Recommendations</h2>
                <p className="text-default-600">
                  Videos recommended by the Royaltea community, awaiting review and voting.
                </p>
              </div>

              {learningContent.recommendations.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {learningContent.recommendations.map((recommendation) => (
                    <Card key={recommendation.id} className="hover:shadow-lg transition-shadow">
                      <CardBody className="p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                            <Users className="w-4 h-4 text-primary" />
                          </div>
                          <div>
                            <span className="text-sm font-medium">
                              {recommendation.recommended_by_name || 'Community Member'}
                            </span>
                            <span className="text-xs text-default-500 ml-2">recommended</span>
                          </div>
                        </div>

                        <h3 className="font-semibold mb-2 line-clamp-2">
                          {recommendation.title}
                        </h3>

                        {recommendation.description && (
                          <p className="text-sm text-default-600 mb-3 line-clamp-3">
                            {recommendation.description}
                          </p>
                        )}

                        <div className="flex items-center gap-4 text-xs text-default-500 mb-4">
                          {recommendation.duration_minutes && (
                            <span>{recommendation.duration_minutes}m</span>
                          )}
                          {recommendation.difficulty_level && (
                            <span>{recommendation.difficulty_level}</span>
                          )}
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Button size="sm" variant="light" color="success">
                              👍 {recommendation.upvotes || 0}
                            </Button>
                            <Button size="sm" variant="light" color="danger">
                              👎 {recommendation.downvotes || 0}
                            </Button>
                          </div>
                          <Button
                            size="sm"
                            color="primary"
                            variant="flat"
                            as="a"
                            href={recommendation.video_url}
                            target="_blank"
                          >
                            Watch
                          </Button>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="w-16 h-16 mx-auto mb-4 text-default-400" />
                  <h3 className="text-xl font-semibold mb-2">No community recommendations yet</h3>
                  <p className="text-default-600 mb-4">
                    Be the first to recommend a video to the community!
                  </p>
                  <Button color="primary" onPress={onSubmissionOpen}>
                    Submit a Video
                  </Button>
                </div>
              )}
            </div>
          </Tab>

          <Tab key="progress" title={
            <div className="flex items-center space-x-2">
              <Award className="w-4 h-4" />
              <span>My Progress</span>
            </div>
          }>
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold mb-2">Learning Progress</h2>
                <p className="text-default-600">
                  Track your learning journey and achievements.
                </p>
              </div>

              {/* Progress Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardBody className="text-center p-6">
                    <div className="text-3xl font-bold text-primary mb-2">
                      {learningContent.userProgress.filter(p => p.status === 'completed').length}
                    </div>
                    <div className="text-sm text-default-600">Completed Videos</div>
                  </CardBody>
                </Card>
                <Card>
                  <CardBody className="text-center p-6">
                    <div className="text-3xl font-bold text-warning mb-2">
                      {learningContent.userProgress.filter(p => p.status === 'in_progress').length}
                    </div>
                    <div className="text-sm text-default-600">In Progress</div>
                  </CardBody>
                </Card>
                <Card>
                  <CardBody className="text-center p-6">
                    <div className="text-3xl font-bold text-success mb-2">
                      {Math.round(
                        learningContent.userProgress.reduce((acc, p) => acc + (p.completion_percentage || 0), 0) /
                        Math.max(learningContent.userProgress.length, 1)
                      )}%
                    </div>
                    <div className="text-sm text-default-600">Average Progress</div>
                  </CardBody>
                </Card>
              </div>

              {/* Recent Activity */}
              {learningContent.userProgress.length > 0 ? (
                <div>
                  <h3 className="text-xl font-semibold mb-4">Recent Activity</h3>
                  <div className="space-y-4">
                    {learningContent.userProgress
                      .sort((a, b) => new Date(b.last_accessed_at || b.started_at) - new Date(a.last_accessed_at || a.started_at))
                      .slice(0, 5)
                      .map((progress) => {
                        const video = learningContent.videos.find(v => v.external_id === progress.course_id);
                        if (!video) return null;

                        return (
                          <Card key={progress.id} className="hover:shadow-lg transition-shadow">
                            <CardBody className="p-4">
                              <div className="flex items-center gap-4">
                                <img
                                  src={video.thumbnail_url || `https://img.youtube.com/vi/${video.external_id}/mqdefault.jpg`}
                                  alt={video.title}
                                  className="w-16 h-12 object-cover rounded"
                                />
                                <div className="flex-1">
                                  <h4 className="font-semibold line-clamp-1">{video.title}</h4>
                                  <div className="flex items-center gap-4 mt-2">
                                    <Progress
                                      value={progress.completion_percentage || 0}
                                      color="primary"
                                      size="sm"
                                      className="flex-1"
                                    />
                                    <span className="text-sm text-default-600">
                                      {progress.completion_percentage || 0}%
                                    </span>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  color="primary"
                                  variant="flat"
                                >
                                  {progress.completion_percentage === 100 ? 'Review' : 'Continue'}
                                </Button>
                              </div>
                            </CardBody>
                          </Card>
                        );
                      })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Award className="w-16 h-16 mx-auto mb-4 text-default-400" />
                  <h3 className="text-xl font-semibold mb-2">Start your learning journey</h3>
                  <p className="text-default-600 mb-4">
                    Enroll in videos to track your progress and achievements.
                  </p>
                  <Button color="primary" onPress={() => setActiveTab('discover')}>
                    Explore Content
                  </Button>
                </div>
              )}
            </div>
          </Tab>

          <Tab key="vetting" title={
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4" />
              <span>Vetting</span>
            </div>
          }>
            <VettingLearningIntegration
              currentUser={currentUser}
              userSkillLevel={0} // TODO: Get from user data
              targetSkill={null}
              onSuggestForVetting={(videoData) => {
                toast.success('Video suggested for vetting review!');
                loadLearningData();
              }}
            />
          </Tab>
        </Tabs>

        {/* Modals */}
        <VideoSubmissionForm
          isOpen={isSubmissionOpen}
          onClose={onSubmissionClose}
          onSubmissionSuccess={() => {
            loadLearningData();
            toast.success('Video submitted for review!');
          }}
        />

        <LearningQueue
          isOpen={isQueueOpen}
          onClose={onQueueClose}
          currentUser={currentUser}
        />
      </div>
    </div>
  );
};

export default LearnPage;
