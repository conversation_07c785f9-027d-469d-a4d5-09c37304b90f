#!/usr/bin/env node

// Test Database Schema - Verify all required tables and columns exist
// This script tests the database schema to ensure frontend compatibility

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseSchema() {
  console.log('🔍 Testing Database Schema for Frontend Compatibility\n');

  const tests = [
    {
      name: 'Users table with is_premium column',
      test: async () => {
        const { data, error } = await supabase
          .from('users')
          .select('id, email, display_name, is_premium, bio, social_links, stats')
          .limit(1);
        return { success: !error, error: error?.message };
      }
    },
    {
      name: 'Projects table with is_active column',
      test: async () => {
        const { data, error } = await supabase
          .from('projects')
          .select('id, name, title, description, status, created_by, team_id, is_active, total_revenue, revenue_distribution_model')
          .limit(1);
        return { success: !error, error: error?.message };
      }
    },
    {
      name: 'Project contributors with is_admin column',
      test: async () => {
        const { data, error } = await supabase
          .from('project_contributors')
          .select('user_id, is_admin, role, status, permission_level')
          .limit(1);
        return { success: !error, error: error?.message };
      }
    },
    {
      name: 'Team members with status column',
      test: async () => {
        const { data, error } = await supabase
          .from('team_members')
          .select('*')
          .eq('status', 'active')
          .limit(1);
        return { success: !error, error: error?.message };
      }
    },
    {
      name: 'Active timers table',
      test: async () => {
        const { data, error } = await supabase
          .from('active_timers')
          .select('*, tasks(id, title, description)')
          .eq('is_active', true)
          .limit(1);
        return { success: !error, error: error?.message };
      }
    },
    {
      name: 'Contribution tracking config table',
      test: async () => {
        const { data, error } = await supabase
          .from('contribution_tracking_config')
          .select('*')
          .limit(1);
        return { success: !error, error: error?.message };
      }
    },
    {
      name: 'Contributions table',
      test: async () => {
        const { data, error } = await supabase
          .from('contributions')
          .select('id, user_id, project_id, contribution_type, hours_worked, status, projects!inner(name)')
          .eq('status', 'active')
          .limit(1);
        return { success: !error, error: error?.message };
      }
    },
    {
      name: 'Team invitations table',
      test: async () => {
        const { data, error } = await supabase
          .from('team_invitations')
          .select('*')
          .eq('status', 'pending')
          .limit(1);
        return { success: !error, error: error?.message };
      }
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`🧪 Testing: ${test.name}`);
      const result = await test.test();
      
      if (result.success) {
        console.log(`   ✅ PASSED\n`);
        passedTests++;
      } else {
        console.log(`   ❌ FAILED: ${result.error}\n`);
      }
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}\n`);
    }
  }

  console.log('📊 Test Results:');
  console.log(`   Passed: ${passedTests}/${totalTests}`);
  console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Database schema is ready for frontend.');
  } else {
    console.log('\n⚠️  Some tests failed. Database schema needs attention.');
  }

  return passedTests === totalTests;
}

// Run the tests
testDatabaseSchema()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Test runner error:', error);
    process.exit(1);
  });
