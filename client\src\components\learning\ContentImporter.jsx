import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Input, Textarea, Select, SelectItem,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure,
  Progress, Chip, Divider, Tabs, Tab, Table, TableHeader, TableColumn,
  TableBody, TableRow, TableCell, Spinner
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  Download, ExternalLink, Eye, RefreshCw, CheckCircle, XCircle, 
  Clock, AlertTriangle, Plus, Trash2, Upload, Link, Globe
} from 'lucide-react';

/**
 * Content Importer Component
 * 
 * Interface for importing content from external sources:
 * - Single URL import with preview
 * - Batch URL import
 * - Import job monitoring
 * - Content source management
 */
const ContentImporter = ({ onImportComplete }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('single');
  const [importJobs, setImportJobs] = useState([]);
  const [contentSources, setContentSources] = useState([]);
  const [previewData, setPreviewData] = useState(null);

  const [singleImport, setSingleImport] = useState({
    url: '',
    title: '',
    description: '',
    category: '',
    vettingLevel: 1,
    autoPublish: false
  });

  const [batchImport, setBatchImport] = useState({
    urls: [''],
    category: '',
    vettingLevel: 1,
    autoPublish: false
  });

  const { isOpen: isPreviewOpen, onOpen: onPreviewOpen, onClose: onPreviewClose } = useDisclosure();

  useEffect(() => {
    if (currentUser) {
      loadContentSources();
      loadImportJobs();
    }
  }, [currentUser]);

  const loadContentSources = async () => {
    try {
      const response = await fetch('/.netlify/functions/content-ingestion/sources');
      const data = await response.json();
      
      if (data.success) {
        setContentSources(data.sources);
      }
    } catch (error) {
      console.error('Error loading content sources:', error);
    }
  };

  const loadImportJobs = async () => {
    try {
      const response = await fetch(`/.netlify/functions/content-ingestion/jobs?userId=${currentUser.id}&limit=20`);
      const data = await response.json();
      
      if (data.success) {
        setImportJobs(data.jobs);
      }
    } catch (error) {
      console.error('Error loading import jobs:', error);
    }
  };

  const handlePreviewContent = async () => {
    if (!singleImport.url) {
      toast.error('Please enter a URL to preview');
      return;
    }

    try {
      setLoading(true);

      const response = await fetch('/.netlify/functions/content-ingestion/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url: singleImport.url
        })
      });

      const data = await response.json();

      if (data.success) {
        setPreviewData(data.preview);
        setSingleImport(prev => ({
          ...prev,
          title: data.preview.title || prev.title,
          description: data.preview.description || prev.description
        }));
        onPreviewOpen();
      } else {
        toast.error('Failed to preview content');
      }
    } catch (error) {
      console.error('Preview error:', error);
      toast.error('Failed to preview content');
    } finally {
      setLoading(false);
    }
  };

  const handleSingleImport = async () => {
    if (!singleImport.url) {
      toast.error('Please enter a URL to import');
      return;
    }

    try {
      setLoading(true);

      const response = await fetch('/.netlify/functions/content-ingestion/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url: singleImport.url,
          userId: currentUser.id,
          importSettings: {
            title: singleImport.title,
            description: singleImport.description,
            category: singleImport.category,
            vettingLevel: singleImport.vettingLevel,
            autoPublish: singleImport.autoPublish
          }
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Import job created successfully!');
        setSingleImport({ url: '', title: '', description: '', category: '', vettingLevel: 1, autoPublish: false });
        loadImportJobs();
        
        if (onImportComplete) {
          onImportComplete(data.jobId);
        }
      } else {
        toast.error('Failed to create import job');
      }
    } catch (error) {
      console.error('Import error:', error);
      toast.error('Failed to import content');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchImport = async () => {
    const validUrls = batchImport.urls.filter(url => url.trim());
    
    if (validUrls.length === 0) {
      toast.error('Please enter at least one URL to import');
      return;
    }

    try {
      setLoading(true);

      const response = await fetch('/.netlify/functions/content-ingestion/batch-import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          urls: validUrls,
          userId: currentUser.id,
          importSettings: {
            category: batchImport.category,
            vettingLevel: batchImport.vettingLevel,
            autoPublish: batchImport.autoPublish
          }
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Created ${data.jobs.length} import jobs!`);
        setBatchImport({ urls: [''], category: '', vettingLevel: 1, autoPublish: false });
        loadImportJobs();
        
        if (onImportComplete) {
          onImportComplete(data.jobs.map(job => job.id));
        }
      } else {
        toast.error('Failed to create batch import jobs');
      }
    } catch (error) {
      console.error('Batch import error:', error);
      toast.error('Failed to import content');
    } finally {
      setLoading(false);
    }
  };

  const addBatchUrl = () => {
    setBatchImport(prev => ({
      ...prev,
      urls: [...prev.urls, '']
    }));
  };

  const removeBatchUrl = (index) => {
    setBatchImport(prev => ({
      ...prev,
      urls: prev.urls.filter((_, i) => i !== index)
    }));
  };

  const updateBatchUrl = (index, value) => {
    setBatchImport(prev => ({
      ...prev,
      urls: prev.urls.map((url, i) => i === index ? value : url)
    }));
  };

  const getJobStatusColor = (status) => {
    const colors = {
      'pending': 'warning',
      'processing': 'primary',
      'completed': 'success',
      'failed': 'danger',
      'cancelled': 'default'
    };
    return colors[status] || 'default';
  };

  const getJobStatusIcon = (status) => {
    const icons = {
      'pending': Clock,
      'processing': RefreshCw,
      'completed': CheckCircle,
      'failed': XCircle,
      'cancelled': AlertTriangle
    };
    const Icon = icons[status] || Clock;
    return <Icon className="w-4 h-4" />;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderSingleImportTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Link className="w-5 h-5" />
            Import Single URL
          </h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <Input
            label="Content URL"
            placeholder="https://example.com/article"
            value={singleImport.url}
            onChange={(e) => setSingleImport(prev => ({ ...prev, url: e.target.value }))}
            endContent={
              <Button
                size="sm"
                variant="flat"
                startContent={<Eye className="w-4 h-4" />}
                onClick={handlePreviewContent}
                isLoading={loading}
              >
                Preview
              </Button>
            }
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Title (optional)"
              placeholder="Override extracted title..."
              value={singleImport.title}
              onChange={(e) => setSingleImport(prev => ({ ...prev, title: e.target.value }))}
            />

            <Select
              label="Vetting Level"
              placeholder="Select vetting level"
              selectedKeys={[singleImport.vettingLevel.toString()]}
              onSelectionChange={(keys) => setSingleImport(prev => ({ 
                ...prev, 
                vettingLevel: parseInt(Array.from(keys)[0]) 
              }))}
            >
              <SelectItem key="1">🟡 Level 1 - Learning</SelectItem>
              <SelectItem key="2">🟠 Level 2 - Peer Verified</SelectItem>
              <SelectItem key="3">🟢 Level 3 - Project Verified</SelectItem>
              <SelectItem key="4">🔵 Level 4 - Expert Verified</SelectItem>
              <SelectItem key="5">🟣 Level 5 - Master Verified</SelectItem>
            </Select>
          </div>

          <Textarea
            label="Description (optional)"
            placeholder="Override extracted description..."
            value={singleImport.description}
            onChange={(e) => setSingleImport(prev => ({ ...prev, description: e.target.value }))}
            minRows={3}
          />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={singleImport.autoPublish}
                  onChange={(e) => setSingleImport(prev => ({ ...prev, autoPublish: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-sm">Auto-publish after import</span>
              </label>
            </div>

            <Button
              color="primary"
              startContent={<Download className="w-4 h-4" />}
              onClick={handleSingleImport}
              isLoading={loading}
            >
              Import Content
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Supported Sources */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Globe className="w-5 h-5" />
            Supported Sources
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {contentSources.map((source) => (
              <div key={source.id} className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Globe className="w-4 h-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">{source.name}</p>
                  <p className="text-xs text-default-500">{source.import_method}</p>
                </div>
              </div>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );

  const renderBatchImportTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Batch Import URLs
          </h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="space-y-3">
            {batchImport.urls.map((url, index) => (
              <div key={index} className="flex items-center gap-2">
                <Input
                  placeholder={`URL ${index + 1}`}
                  value={url}
                  onChange={(e) => updateBatchUrl(index, e.target.value)}
                  className="flex-1"
                />
                <Button
                  size="sm"
                  variant="flat"
                  color="danger"
                  isIconOnly
                  onClick={() => removeBatchUrl(index)}
                  isDisabled={batchImport.urls.length === 1}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>

          <Button
            variant="flat"
            startContent={<Plus className="w-4 h-4" />}
            onClick={addBatchUrl}
          >
            Add Another URL
          </Button>

          <Divider />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              label="Default Vetting Level"
              placeholder="Select vetting level"
              selectedKeys={[batchImport.vettingLevel.toString()]}
              onSelectionChange={(keys) => setBatchImport(prev => ({ 
                ...prev, 
                vettingLevel: parseInt(Array.from(keys)[0]) 
              }))}
            >
              <SelectItem key="1">🟡 Level 1 - Learning</SelectItem>
              <SelectItem key="2">🟠 Level 2 - Peer Verified</SelectItem>
              <SelectItem key="3">🟢 Level 3 - Project Verified</SelectItem>
              <SelectItem key="4">🔵 Level 4 - Expert Verified</SelectItem>
              <SelectItem key="5">🟣 Level 5 - Master Verified</SelectItem>
            </Select>

            <div className="flex items-end">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={batchImport.autoPublish}
                  onChange={(e) => setBatchImport(prev => ({ ...prev, autoPublish: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-sm">Auto-publish all imports</span>
              </label>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              color="primary"
              startContent={<Upload className="w-4 h-4" />}
              onClick={handleBatchImport}
              isLoading={loading}
            >
              Import All URLs
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );

  const renderJobsTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Import Jobs</h3>
          <Button
            size="sm"
            variant="flat"
            startContent={<RefreshCw className="w-4 h-4" />}
            onClick={loadImportJobs}
          >
            Refresh
          </Button>
        </CardHeader>
        <CardBody>
          {importJobs.length === 0 ? (
            <div className="text-center py-8">
              <Download className="w-16 h-16 mx-auto mb-4 text-default-400" />
              <h3 className="text-lg font-semibold mb-2">No import jobs yet</h3>
              <p className="text-default-600">
                Start importing content from external sources to see jobs here.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {importJobs.map((job) => (
                <Card key={job.id} className="border">
                  <CardBody className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Chip 
                            color={getJobStatusColor(job.status)}
                            variant="flat"
                            size="sm"
                            startContent={getJobStatusIcon(job.status)}
                          >
                            {job.status}
                          </Chip>
                          <span className="text-sm text-default-500">
                            {formatDate(job.created_at)}
                          </span>
                        </div>
                        
                        <p className="font-medium mb-1 break-all">{job.external_url}</p>
                        
                        {job.error_message && (
                          <p className="text-sm text-danger mb-2">{job.error_message}</p>
                        )}
                        
                        <div className="flex items-center gap-4 text-sm text-default-500">
                          <span>Method: {job.import_method}</span>
                          {job.processing_time_seconds && (
                            <span>Time: {job.processing_time_seconds}s</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          size="sm"
                          variant="flat"
                          startContent={<ExternalLink className="w-4 h-4" />}
                          onClick={() => window.open(job.external_url, '_blank')}
                        >
                          View Source
                        </Button>
                        
                        {job.content_id && (
                          <Button
                            size="sm"
                            color="primary"
                            variant="flat"
                            startContent={<Eye className="w-4 h-4" />}
                            onClick={() => {
                              // Navigate to content view
                              console.log('View content:', job.content_id);
                            }}
                          >
                            View Content
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );

  return (
    <div className="content-importer space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold mb-2">Content Importer</h2>
        <p className="text-default-600">
          Import learning content from external sources like blogs, documentation, and educational platforms
        </p>
      </div>

      {/* Main Interface */}
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={setActiveTab}
        className="w-full"
      >
        <Tab key="single" title="Single Import">
          {renderSingleImportTab()}
        </Tab>
        
        <Tab key="batch" title="Batch Import">
          {renderBatchImportTab()}
        </Tab>
        
        <Tab key="jobs" title="Import Jobs">
          {renderJobsTab()}
        </Tab>
      </Tabs>

      {/* Preview Modal */}
      <Modal isOpen={isPreviewOpen} onClose={onPreviewClose} size="4xl">
        <ModalContent>
          <ModalHeader>
            <h2 className="text-xl font-bold">Content Preview</h2>
          </ModalHeader>
          <ModalBody>
            {previewData && (
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2">{previewData.title}</h3>
                  <p className="text-default-600 mb-4">{previewData.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Source:</span> {previewData.sourceName}
                    </div>
                    <div>
                      <span className="font-medium">Author:</span> {previewData.author || 'Unknown'}
                    </div>
                    {previewData.publishDate && (
                      <div>
                        <span className="font-medium">Published:</span> {formatDate(previewData.publishDate)}
                      </div>
                    )}
                    <div>
                      <span className="font-medium">Estimated read time:</span> {Math.ceil(previewData.content?.split(' ').length / 200) || 0} min
                    </div>
                  </div>
                </div>
                
                <Divider />
                
                <div className="max-h-96 overflow-y-auto">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <div dangerouslySetInnerHTML={{ 
                      __html: previewData.content?.substring(0, 2000).replace(/\n/g, '<br>') + '...' 
                    }} />
                  </div>
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onPreviewClose}>
              Close Preview
            </Button>
            <Button 
              color="primary" 
              onPress={() => {
                onPreviewClose();
                handleSingleImport();
              }}
            >
              Import This Content
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ContentImporter;
