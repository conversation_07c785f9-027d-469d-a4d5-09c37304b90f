// Test Invitation System
// Simple script to test the invitation system functionality

import { createClient } from '@supabase/supabase-js';

// Supabase configuration (replace with your actual values)
const SUPABASE_URL = 'https://your-project.supabase.co';
const SUPABASE_ANON_KEY = 'your-anon-key';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const TEST_INVITATION_EMAIL = '<EMAIL>';

async function testInvitationSystem() {
  console.log('🧪 Testing Invitation System...\n');

  try {
    // Step 1: Login as test user
    console.log('1️⃣ Logging in as test user...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: TEST_USER.email,
      password: TEST_USER.password
    });

    if (authError) {
      console.error('❌ Login failed:', authError.message);
      return;
    }

    console.log('✅ Login successful');
    const currentUser = authData.user;

    // Step 2: Check if we have a project to invite to
    console.log('\n2️⃣ Finding a project to invite to...');
    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('created_by', currentUser.id)
      .limit(1);

    if (projectError) {
      console.error('❌ Error fetching projects:', projectError.message);
      return;
    }

    if (!projects || projects.length === 0) {
      console.log('⚠️ No projects found. Creating a test project...');
      
      const { data: newProject, error: createError } = await supabase
        .from('projects')
        .insert({
          name: 'Invitation Test Project',
          description: 'Test project for invitation system',
          created_by: currentUser.id,
          status: 'active'
        })
        .select()
        .single();

      if (createError) {
        console.error('❌ Error creating project:', createError.message);
        return;
      }

      console.log('✅ Test project created:', newProject.name);
      projects[0] = newProject;
    }

    const testProject = projects[0];
    console.log('✅ Using project:', testProject.name);

    // Step 3: Test invitation creation
    console.log('\n3️⃣ Creating invitation for non-user...');
    const { data: invitation, error: inviteError } = await supabase
      .from('project_contributors')
      .insert({
        project_id: testProject.id,
        email: TEST_INVITATION_EMAIL,
        display_name: TEST_INVITATION_EMAIL.split('@')[0],
        role: 'Contributor',
        permission_level: 'Contributor',
        is_admin: false,
        status: 'pending',
        invitation_sent_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (inviteError) {
      console.error('❌ Error creating invitation:', inviteError.message);
      return;
    }

    console.log('✅ Invitation created successfully');
    console.log('   Invitation ID:', invitation.id);
    console.log('   Email:', invitation.email);
    console.log('   Status:', invitation.status);

    // Step 4: Test invitation token generation (if available)
    console.log('\n4️⃣ Testing invitation token generation...');
    try {
      // Try to update with token (this will fail gracefully if columns don't exist)
      const testToken = 'test-token-' + Date.now();
      const { error: tokenError } = await supabase
        .from('project_contributors')
        .update({
          invitation_token: testToken,
          token_generated_at: new Date().toISOString()
        })
        .eq('id', invitation.id);

      if (tokenError) {
        if (tokenError.code === '42703') {
          console.log('⚠️ Token columns not yet available (run fix-invitation-system.sql)');
        } else {
          console.error('❌ Token update error:', tokenError.message);
        }
      } else {
        console.log('✅ Invitation token updated successfully');
      }
    } catch (error) {
      console.log('⚠️ Token system not fully available yet');
    }

    // Step 5: Test invitation lookup
    console.log('\n5️⃣ Testing invitation lookup...');
    const { data: foundInvitation, error: lookupError } = await supabase
      .from('project_contributors')
      .select('*')
      .eq('id', invitation.id)
      .eq('status', 'pending')
      .single();

    if (lookupError) {
      console.error('❌ Error looking up invitation:', lookupError.message);
      return;
    }

    console.log('✅ Invitation lookup successful');
    console.log('   Found invitation for:', foundInvitation.email);

    // Step 6: Test friend request creation (if table exists)
    console.log('\n6️⃣ Testing friend request creation...');
    try {
      const { data: friendRequest, error: friendError } = await supabase
        .from('friend_requests')
        .insert({
          sender_id: currentUser.id,
          recipient_email: TEST_INVITATION_EMAIL,
          message: 'Test friend request from invitation system',
          status: 'pending',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (friendError) {
        if (friendError.code === '42P01') {
          console.log('⚠️ Friend requests table not available (run fix-invitation-system.sql)');
        } else {
          console.error('❌ Friend request error:', friendError.message);
        }
      } else {
        console.log('✅ Friend request created successfully');
        console.log('   Request ID:', friendRequest.id);
      }
    } catch (error) {
      console.log('⚠️ Friend request system not fully available yet');
    }

    // Step 7: Test email service endpoint
    console.log('\n7️⃣ Testing email service...');
    try {
      const response = await fetch('/.netlify/functions/email-service/templates');
      if (response.ok) {
        const templates = await response.json();
        if (templates.success) {
          console.log('✅ Email service is working');
          console.log('   Available templates:', templates.data.map(t => t.name).join(', '));
        } else {
          console.log('⚠️ Email service responded but with errors');
        }
      } else {
        console.log('⚠️ Email service not accessible (may be production-only)');
      }
    } catch (error) {
      console.log('⚠️ Email service test failed (expected in local environment)');
    }

    // Step 8: Cleanup test data
    console.log('\n8️⃣ Cleaning up test data...');
    
    // Delete test invitation
    await supabase
      .from('project_contributors')
      .delete()
      .eq('id', invitation.id);

    // Delete test friend request if it was created
    await supabase
      .from('friend_requests')
      .delete()
      .eq('recipient_email', TEST_INVITATION_EMAIL);

    console.log('✅ Test data cleaned up');

    // Final summary
    console.log('\n🎉 Invitation System Test Complete!');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('✅ Basic invitation creation: WORKING');
    console.log('✅ Invitation lookup: WORKING');
    console.log('⚠️  Token system: Run fix-invitation-system.sql for full functionality');
    console.log('⚠️  Friend requests: Run fix-invitation-system.sql for full functionality');
    console.log('⚠️  Email service: Production deployment required');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('\n📋 Next Steps:');
    console.log('1. Run scripts/database/fix-invitation-system.sql in your database');
    console.log('2. Deploy to production to test email functionality');
    console.log('3. Test invitation acceptance flow with real non-users');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  } finally {
    // Logout
    await supabase.auth.signOut();
  }
}

// Run the test
testInvitationSystem();
