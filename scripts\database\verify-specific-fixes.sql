-- Verify Specific Frontend Error Fixes
-- This script checks the exact columns and tables that were causing frontend errors

-- ============================================================================
-- 1. CHECK USERS TABLE - is_premium column
-- ============================================================================
SELECT 'USERS TABLE - is_premium column' as check_name;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'users' 
            AND column_name = 'is_premium' 
            AND table_schema = 'public'
        ) THEN '✅ users.is_premium EXISTS'
        ELSE '❌ users.is_premium MISSING'
    END as status;

-- ============================================================================
-- 2. CHECK PROJECT_CONTRIBUTORS TABLE - is_admin column
-- ============================================================================
SELECT 'PROJECT_CONTRIBUTORS TABLE - is_admin column' as check_name;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'project_contributors' 
            AND column_name = 'is_admin' 
            AND table_schema = 'public'
        ) THEN '✅ project_contributors.is_admin EXISTS'
        ELSE '❌ project_contributors.is_admin MISSING'
    END as status;

-- ============================================================================
-- 3. CHECK TEAM_MEMBERS TABLE - status column
-- ============================================================================
SELECT 'TEAM_MEMBERS TABLE - status column' as check_name;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'team_members' 
            AND column_name = 'status' 
            AND table_schema = 'public'
        ) THEN '✅ team_members.status EXISTS'
        ELSE '❌ team_members.status MISSING'
    END as status;

-- ============================================================================
-- 4. CHECK PROJECTS TABLE - is_active column
-- ============================================================================
SELECT 'PROJECTS TABLE - is_active column' as check_name;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'projects' 
            AND column_name = 'is_active' 
            AND table_schema = 'public'
        ) THEN '✅ projects.is_active EXISTS'
        ELSE '❌ projects.is_active MISSING'
    END as status;

-- ============================================================================
-- 5. CHECK ACTIVE_TIMERS TABLE
-- ============================================================================
SELECT 'ACTIVE_TIMERS TABLE' as check_name;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'active_timers' 
            AND table_schema = 'public'
        ) THEN '✅ active_timers TABLE EXISTS'
        ELSE '❌ active_timers TABLE MISSING'
    END as status;

-- ============================================================================
-- 6. CHECK CONTRIBUTION_TRACKING_CONFIG TABLE
-- ============================================================================
SELECT 'CONTRIBUTION_TRACKING_CONFIG TABLE' as check_name;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'contribution_tracking_config' 
            AND table_schema = 'public'
        ) THEN '✅ contribution_tracking_config TABLE EXISTS'
        ELSE '❌ contribution_tracking_config TABLE MISSING'
    END as status;

-- ============================================================================
-- 7. CHECK CONTRIBUTIONS TABLE
-- ============================================================================
SELECT 'CONTRIBUTIONS TABLE' as check_name;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'contributions' 
            AND table_schema = 'public'
        ) THEN '✅ contributions TABLE EXISTS'
        ELSE '❌ contributions TABLE MISSING'
    END as status;

-- ============================================================================
-- 8. SUMMARY REPORT
-- ============================================================================
SELECT 'FRONTEND ERROR FIXES SUMMARY' as summary;

WITH fix_status AS (
    SELECT 
        COUNT(*) as total_checks,
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_premium' AND table_schema = 'public')
            THEN 1 ELSE 0 END) +
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'project_contributors' AND column_name = 'is_admin' AND table_schema = 'public')
            THEN 1 ELSE 0 END) +
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'status' AND table_schema = 'public')
            THEN 1 ELSE 0 END) +
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'is_active' AND table_schema = 'public')
            THEN 1 ELSE 0 END) +
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'active_timers' AND table_schema = 'public')
            THEN 1 ELSE 0 END) +
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'contribution_tracking_config' AND table_schema = 'public')
            THEN 1 ELSE 0 END) +
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'contributions' AND table_schema = 'public')
            THEN 1 ELSE 0 END) as fixes_applied
    FROM (SELECT 1) as dummy
)
SELECT 
    fixes_applied || '/7 fixes applied' as fix_status,
    CASE 
        WHEN fixes_applied = 7 THEN '🎉 ALL FRONTEND ERRORS FIXED!'
        ELSE '⚠️ SOME FIXES STILL NEEDED'
    END as overall_status
FROM fix_status;

-- ============================================================================
-- 9. TEST SPECIFIC QUERIES THAT WERE FAILING
-- ============================================================================

-- Test the exact query that was failing for users
SELECT 'TESTING USERS QUERY' as test_name;
-- This should not fail now
SELECT COUNT(*) as user_count 
FROM users 
WHERE is_premium IS NOT NULL;

-- Test the exact query that was failing for project_contributors  
SELECT 'TESTING PROJECT_CONTRIBUTORS QUERY' as test_name;
-- This should not fail now
SELECT COUNT(*) as contributor_count 
FROM project_contributors 
WHERE is_admin IS NOT NULL;

-- Test the exact query that was failing for team_members
SELECT 'TESTING TEAM_MEMBERS QUERY' as test_name;
-- This should not fail now
SELECT COUNT(*) as member_count 
FROM team_members 
WHERE status = 'active';

-- Test the exact query that was failing for projects
SELECT 'TESTING PROJECTS QUERY' as test_name;
-- This should not fail now
SELECT COUNT(*) as project_count 
FROM projects 
WHERE is_active = true;

-- Test the exact query that was failing for active_timers
SELECT 'TESTING ACTIVE_TIMERS QUERY' as test_name;
-- This should not fail now
SELECT COUNT(*) as timer_count 
FROM active_timers 
WHERE is_active = true;

SELECT '🎯 VERIFICATION COMPLETE - All queries should work now!' as final_status;
