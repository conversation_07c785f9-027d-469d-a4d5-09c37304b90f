-- Commission & Recurring Billing System Migration
-- Backend Specialist: Automated commission calculation and recurring billing infrastructure

-- Create commission_schedules table for automated commission calculation
CREATE TABLE IF NOT EXISTS public.commission_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Commission Configuration
    name TEXT NOT NULL,
    description TEXT,
    commission_type TEXT NOT NULL CHECK (commission_type IN ('percentage', 'fixed', 'tiered', 'hybrid')),
    
    -- Rate Configuration
    base_rate DECIMAL(5,2) CHECK (base_rate >= 0 AND base_rate <= 100), -- For percentage
    fixed_amount DECIMAL(12,2) CHECK (fixed_amount >= 0), -- For fixed amount
    tier_config JSONB DEFAULT '[]'::jsonb, -- For tiered commissions
    
    -- Applicability
    applies_to TEXT NOT NULL CHECK (applies_to IN ('all_sales', 'specific_products', 'specific_clients', 'alliance_revenue')),
    product_categories TEXT[], -- Array of applicable product categories
    client_types TEXT[], -- Array of applicable client types
    alliance_types TEXT[], -- Array of applicable alliance types
    
    -- Timing and Conditions
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiration_date DATE,
    minimum_sale_amount DECIMAL(12,2) DEFAULT 0,
    maximum_commission_amount DECIMAL(12,2),
    
    -- Payment Terms
    payment_frequency TEXT DEFAULT 'monthly' CHECK (payment_frequency IN ('immediate', 'weekly', 'monthly', 'quarterly')),
    payment_delay_days INTEGER DEFAULT 0 CHECK (payment_delay_days >= 0),
    
    -- Status and Metadata
    is_active BOOLEAN DEFAULT true,
    auto_calculate BOOLEAN DEFAULT true,
    requires_approval BOOLEAN DEFAULT false,
    
    -- Audit Trail
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create commission_calculations table for tracking calculated commissions
CREATE TABLE IF NOT EXISTS public.commission_calculations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Links
    commission_schedule_id UUID NOT NULL REFERENCES public.commission_schedules(id) ON DELETE CASCADE,
    revenue_transaction_id UUID REFERENCES public.revenue_entries(id) ON DELETE SET NULL,
    financial_transaction_id UUID REFERENCES public.financial_transactions(id) ON DELETE SET NULL,
    sales_rep_id UUID NOT NULL REFERENCES auth.users(id),
    
    -- Calculation Details
    base_amount DECIMAL(12,2) NOT NULL CHECK (base_amount >= 0),
    commission_rate DECIMAL(5,2) CHECK (commission_rate >= 0 AND commission_rate <= 100),
    calculated_amount DECIMAL(12,2) NOT NULL CHECK (calculated_amount >= 0),
    
    -- Calculation Context
    calculation_method TEXT NOT NULL CHECK (calculation_method IN ('percentage', 'fixed', 'tiered', 'manual')),
    tier_breakdown JSONB DEFAULT '{}'::jsonb, -- For tiered calculations
    adjustments JSONB DEFAULT '[]'::jsonb, -- Manual adjustments
    
    -- Status and Processing
    status TEXT DEFAULT 'calculated' CHECK (status IN ('calculated', 'approved', 'paid', 'disputed', 'cancelled')),
    calculation_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
    approval_date TIMESTAMP WITH TIME ZONE,
    payment_date TIMESTAMP WITH TIME ZONE,
    
    -- Payment Information
    payment_method TEXT,
    payment_reference TEXT,
    payment_notes TEXT,
    
    -- Audit
    approved_by UUID REFERENCES auth.users(id),
    paid_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create recurring_billing_schedules table for subscription and fee management
CREATE TABLE IF NOT EXISTS public.recurring_billing_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Billing Entity
    customer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    alliance_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    venture_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    
    -- Billing Configuration
    billing_type TEXT NOT NULL CHECK (billing_type IN ('subscription', 'talent_fee', 'alliance_fee', 'platform_fee', 'maintenance')),
    service_description TEXT NOT NULL,
    
    -- Amount and Currency
    amount DECIMAL(12,2) NOT NULL CHECK (amount >= 0),
    currency TEXT DEFAULT 'USD',
    
    -- Billing Frequency
    frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'semi_annually', 'annually')),
    billing_day INTEGER DEFAULT 1 CHECK (billing_day >= 1 AND billing_day <= 31), -- Day of month/week
    
    -- Schedule Dates
    start_date DATE NOT NULL,
    end_date DATE, -- NULL for indefinite
    next_billing_date DATE NOT NULL,
    last_billing_date DATE,
    
    -- Billing Behavior
    auto_charge BOOLEAN DEFAULT true,
    send_invoice BOOLEAN DEFAULT true,
    grace_period_days INTEGER DEFAULT 7 CHECK (grace_period_days >= 0),
    late_fee_amount DECIMAL(12,2) DEFAULT 0,
    late_fee_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- Payment Configuration
    payment_method_id TEXT, -- Reference to stored payment method
    backup_payment_method_id TEXT,
    
    -- Status and Control
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'cancelled', 'expired', 'failed')),
    pause_until DATE,
    cancellation_reason TEXT,
    
    -- Metadata
    billing_metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Audit Trail
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create recurring_billing_transactions table for tracking billing events
CREATE TABLE IF NOT EXISTS public.recurring_billing_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Links
    billing_schedule_id UUID NOT NULL REFERENCES public.recurring_billing_schedules(id) ON DELETE CASCADE,
    financial_transaction_id UUID REFERENCES public.financial_transactions(id) ON DELETE SET NULL,
    
    -- Transaction Details
    billing_period_start DATE NOT NULL,
    billing_period_end DATE NOT NULL,
    amount_due DECIMAL(12,2) NOT NULL CHECK (amount_due >= 0),
    amount_paid DECIMAL(12,2) DEFAULT 0 CHECK (amount_paid >= 0),
    
    -- Fees and Adjustments
    late_fees DECIMAL(12,2) DEFAULT 0,
    adjustments DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) GENERATED ALWAYS AS (amount_due + late_fees + adjustments) STORED,
    
    -- Status and Timing
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'paid', 'failed', 'cancelled', 'refunded')),
    due_date DATE NOT NULL,
    paid_date TIMESTAMP WITH TIME ZONE,
    
    -- Payment Information
    payment_method TEXT,
    payment_reference TEXT,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    next_retry_date TIMESTAMP WITH TIME ZONE,
    
    -- Invoice Information
    invoice_number TEXT UNIQUE,
    invoice_url TEXT,
    invoice_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create payment_automation_rules table for automated payment processing
CREATE TABLE IF NOT EXISTS public.payment_automation_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Rule Configuration
    rule_name TEXT NOT NULL,
    rule_type TEXT NOT NULL CHECK (rule_type IN ('commission_payment', 'recurring_billing', 'late_fee_application', 'payment_retry')),
    
    -- Trigger Conditions
    trigger_conditions JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Actions
    actions JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Execution Settings
    is_active BOOLEAN DEFAULT true,
    execution_order INTEGER DEFAULT 0,
    max_executions_per_day INTEGER,
    
    -- Timing
    execution_schedule TEXT, -- Cron-like schedule
    last_executed_at TIMESTAMP WITH TIME ZONE,
    next_execution_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_commission_schedules_active ON public.commission_schedules(is_active, effective_date) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_commission_schedules_applies_to ON public.commission_schedules(applies_to);

CREATE INDEX IF NOT EXISTS idx_commission_calculations_sales_rep ON public.commission_calculations(sales_rep_id);
CREATE INDEX IF NOT EXISTS idx_commission_calculations_status ON public.commission_calculations(status);
CREATE INDEX IF NOT EXISTS idx_commission_calculations_schedule ON public.commission_calculations(commission_schedule_id);

CREATE INDEX IF NOT EXISTS idx_recurring_billing_schedules_customer ON public.recurring_billing_schedules(customer_id) WHERE customer_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_recurring_billing_schedules_alliance ON public.recurring_billing_schedules(alliance_id) WHERE alliance_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_recurring_billing_schedules_next_billing ON public.recurring_billing_schedules(next_billing_date, status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_recurring_billing_transactions_schedule ON public.recurring_billing_transactions(billing_schedule_id);
CREATE INDEX IF NOT EXISTS idx_recurring_billing_transactions_status ON public.recurring_billing_transactions(status);
CREATE INDEX IF NOT EXISTS idx_recurring_billing_transactions_due_date ON public.recurring_billing_transactions(due_date, status) WHERE status IN ('pending', 'processing');

CREATE INDEX IF NOT EXISTS idx_payment_automation_rules_active ON public.payment_automation_rules(is_active, next_execution_at) WHERE is_active = true;

-- Enable Row Level Security
ALTER TABLE public.commission_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commission_calculations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurring_billing_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurring_billing_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_automation_rules ENABLE ROW LEVEL SECURITY;

-- RLS Policies for commission_schedules
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'commission_schedules' AND policyname = 'Platform admins can manage commission schedules') THEN
        CREATE POLICY "Platform admins can manage commission schedules" ON public.commission_schedules
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM public.users u
                    WHERE u.id = auth.uid() AND (u.is_admin = true OR u.admin_role IN ('super_admin', 'financial_admin'))
                )
            );
    END IF;
END $$;

-- RLS Policies for commission_calculations
CREATE POLICY "Sales reps can view their own commissions" ON public.commission_calculations
    FOR SELECT USING (sales_rep_id = auth.uid());

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'commission_calculations' AND policyname = 'Finance admins can manage all commissions') THEN
        CREATE POLICY "Finance admins can manage all commissions" ON public.commission_calculations
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM public.users u
                    WHERE u.id = auth.uid() AND (u.is_admin = true OR u.admin_role IN ('super_admin', 'financial_admin'))
                )
            );
    END IF;
END $$;

-- RLS Policies for recurring_billing_schedules
CREATE POLICY "Users can view their own billing schedules" ON public.recurring_billing_schedules
    FOR SELECT USING (
        customer_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = alliance_id AND tm.user_id = auth.uid() AND tm.role IN ('founder', 'owner', 'admin')
        )
    );

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'recurring_billing_schedules' AND policyname = 'Finance admins can manage all billing schedules') THEN
        CREATE POLICY "Finance admins can manage billing schedules" ON public.recurring_billing_schedules
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM public.users u
                    WHERE u.id = auth.uid() AND (u.is_admin = true OR u.admin_role IN ('super_admin', 'financial_admin'))
                )
            );
    END IF;
END $$;

-- RLS Policies for recurring_billing_transactions
CREATE POLICY "Users can view their billing transactions" ON public.recurring_billing_transactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.recurring_billing_schedules rbs
            WHERE rbs.id = billing_schedule_id AND (
                rbs.customer_id = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM public.team_members tm
                    WHERE tm.team_id = rbs.alliance_id AND tm.user_id = auth.uid() AND tm.role IN ('founder', 'owner', 'admin')
                )
            )
        )
    );

-- RLS Policies for payment_automation_rules
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'payment_automation_rules' AND policyname = 'Finance admins can manage automation rules') THEN
        CREATE POLICY "Finance admins can manage automation rules" ON public.payment_automation_rules
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM public.users u
                    WHERE u.id = auth.uid() AND (u.is_admin = true OR u.admin_role IN ('super_admin', 'financial_admin'))
                )
            );
    END IF;
END $$;

-- Add triggers for updated_at timestamps
DROP TRIGGER IF EXISTS update_commission_schedules_updated_at ON public.commission_schedules;
CREATE TRIGGER update_commission_schedules_updated_at
    BEFORE UPDATE ON public.commission_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_commission_calculations_updated_at ON public.commission_calculations;
CREATE TRIGGER update_commission_calculations_updated_at
    BEFORE UPDATE ON public.commission_calculations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_recurring_billing_schedules_updated_at ON public.recurring_billing_schedules;
CREATE TRIGGER update_recurring_billing_schedules_updated_at
    BEFORE UPDATE ON public.recurring_billing_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_recurring_billing_transactions_updated_at ON public.recurring_billing_transactions;
CREATE TRIGGER update_recurring_billing_transactions_updated_at
    BEFORE UPDATE ON public.recurring_billing_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payment_automation_rules_updated_at ON public.payment_automation_rules;
CREATE TRIGGER update_payment_automation_rules_updated_at
    BEFORE UPDATE ON public.payment_automation_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.commission_schedules IS 'Commission calculation rules and schedules for automated commission processing';
COMMENT ON TABLE public.commission_calculations IS 'Calculated commission amounts with approval and payment tracking';
COMMENT ON TABLE public.recurring_billing_schedules IS 'Recurring billing schedules for subscriptions and fees';
COMMENT ON TABLE public.recurring_billing_transactions IS 'Individual billing transactions and payment tracking';
COMMENT ON TABLE public.payment_automation_rules IS 'Automated payment processing rules and workflows';
