// Create Simple Content - Minimal fields only
// Creates content using only the absolutely required fields

import { createClient } from '@supabase/supabase-js';

// Database configuration
const SUPABASE_URL = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function createSimpleContent() {
  console.log('🎯 Creating Simple Royaltea Learning Content...\n');

  try {
    // Try with just the absolute minimum fields
    console.log('1️⃣ Creating Revenue Sharing Guide...');
    
    const revenueContent = {
      title: 'Understanding Revenue Sharing in Royaltea',
      description: 'Learn the fundamentals of how revenue sharing works in the Royaltea platform, including royalty models and payment distribution.',
      content_type: 'tutorial',  // Try tutorial since it seemed to work in some cases
      content_body: `# Understanding Revenue Sharing in Royaltea

## What is Revenue Sharing?

Revenue sharing in Royaltea is a transparent system that automatically distributes project earnings among contributors based on their contributions and agreed-upon terms. Unlike traditional employment models, revenue sharing creates true partnership opportunities where everyone benefits from project success.

## Core Concepts

### 1. Royalty Models
Royaltea supports multiple royalty distribution models:

- **Equal Split**: All contributors receive equal shares
- **Contribution-Based**: Shares based on time, effort, or value contributed  
- **Role-Based**: Different percentages for different roles (developer, designer, manager)
- **Hybrid Models**: Combination of the above approaches

### 2. Tranche System
Projects can be divided into tranches (phases) with different revenue sharing rules:

- **Development Tranche**: Revenue from initial development work
- **Maintenance Tranche**: Ongoing revenue from updates and support
- **Growth Tranche**: Revenue from scaling and new features
- **Legacy Tranche**: Long-term passive revenue

### 3. Contribution Points
The platform tracks contribution through various metrics:

- **Time Tracking**: Hours worked on specific tasks
- **Deliverable Completion**: Finished features, designs, or content
- **Quality Metrics**: Code reviews, testing, documentation
- **Leadership Activities**: Project management, team coordination

## How It Works

### Step 1: Project Setup
When creating a project, the project owner defines:
- Revenue sharing model
- Contributor roles and percentages
- Tranche structure
- Payment thresholds

### Step 2: Contributor Onboarding
New contributors:
- Review and sign revenue sharing agreements
- Understand their role and expected contribution
- Set up payment preferences
- Begin tracking their work

### Step 3: Revenue Tracking
The platform automatically:
- Monitors project revenue from connected accounts
- Tracks individual contributions
- Calculates distribution amounts
- Handles tax documentation

### Step 4: Distribution
Revenue is distributed:
- Automatically when thresholds are met
- Manually by project administrators
- According to predefined schedules
- With full transparency and audit trails

## Benefits

### For Project Owners
- **Attract Top Talent**: Offer equity-like participation without giving up ownership
- **Align Incentives**: Contributors are motivated by project success
- **Reduce Upfront Costs**: Pay contributors from revenue rather than upfront
- **Scale Efficiently**: Add contributors without fixed salary commitments

### For Contributors
- **Unlimited Earning Potential**: No salary cap - earn based on project success
- **Portfolio Diversification**: Work on multiple revenue-generating projects
- **Skill Development**: Learn while earning from real projects
- **Network Building**: Connect with other professionals and entrepreneurs

## Getting Started

Ready to implement revenue sharing in your project? Here's how to begin:

1. **Define Your Model**: Choose the revenue sharing approach that fits your project
2. **Set Up Your Project**: Create your project in Royaltea with revenue sharing enabled
3. **Invite Contributors**: Send invitations with clear revenue sharing terms
4. **Connect Revenue Sources**: Link your payment processors and revenue streams
5. **Start Tracking**: Begin monitoring contributions and revenue
6. **Distribute Earnings**: Set up automatic or manual distribution schedules

This comprehensive system ensures fair compensation while maintaining transparency and trust among all project contributors.`
    };

    const { data: revenueResult, error: revenueError } = await supabase
      .from('learning_content')
      .insert([revenueContent])
      .select();

    if (revenueError) {
      console.log('❌ Revenue content error:', revenueError.message);
      
      // Try without content_body
      console.log('   Trying without content_body...');
      const simpleRevenue = {
        title: 'Understanding Revenue Sharing in Royaltea',
        description: 'Learn the fundamentals of how revenue sharing works in the Royaltea platform, including royalty models and payment distribution.',
        content_type: 'tutorial'
      };
      
      const { data: simpleResult, error: simpleError } = await supabase
        .from('learning_content')
        .insert([simpleRevenue])
        .select();
        
      if (simpleError) {
        console.log('   ❌ Simple version error:', simpleError.message);
      } else {
        console.log('   ✅ Simple version created:', simpleResult[0].id);
      }
    } else {
      console.log('✅ Revenue sharing guide created successfully!');
      console.log('   ID:', revenueResult[0].id);
      console.log('   Title:', revenueResult[0].title);
    }

    // Try a different approach - check what content already exists
    console.log('\n2️⃣ Checking existing content...');
    
    const { data: existingContent, error: checkError } = await supabase
      .from('learning_content')
      .select('*')
      .limit(5);

    if (checkError) {
      console.log('❌ Check error:', checkError.message);
    } else {
      console.log('✅ Found', existingContent.length, 'existing content items');
      if (existingContent.length > 0) {
        console.log('   Sample content structure:', Object.keys(existingContent[0]));
        console.log('   Sample content:', existingContent[0].title);
      }
    }

    // Try to insert using curl directly
    console.log('\n3️⃣ Trying direct REST API insertion...');
    
    const curlCommand = `curl -X POST 'https://hqqlrrqvjcetoxbdjgzx.supabase.co/rest/v1/learning_content' \\
      -H "apikey: ${SUPABASE_SERVICE_KEY}" \\
      -H "Authorization: Bearer ${SUPABASE_SERVICE_KEY}" \\
      -H "Content-Type: application/json" \\
      -d '{"title": "Project Management Best Practices", "description": "Learn project management in Royaltea", "content_type": "tutorial"}'`;
    
    console.log('   Command to try manually:');
    console.log('   ' + curlCommand);

    console.log('\n🎉 Content Creation Attempt Complete!');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('⚠️  Database constraints are preventing content creation');
    console.log('✅ Connection to database is working');
    console.log('✅ learning_content table exists and is accessible');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('\n📋 Next Steps:');
    console.log('• The table has strict constraints that need to be understood');
    console.log('• We can work with existing content or modify constraints');
    console.log('• The learning center components are ready to display content');
    console.log('• Manual content insertion via curl might work');

  } catch (error) {
    console.error('❌ Content creation failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the content creation
createSimpleContent();
