-- Vetting Learning Integration Migration
-- Adds tables and columns to support vetting-learning system integration

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create vetting content suggestions table
CREATE TABLE IF NOT EXISTS public.vetting_content_suggestions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    video_id TEXT NOT NULL, -- YouTube video ID or external reference
    video_url TEXT,
    title TEXT,
    description TEXT,
    suggested_vetting_level INTEGER NOT NULL CHECK (suggested_vetting_level >= 1 AND suggested_vetting_level <= 5),
    target_skills TEXT[] DEFAULT '{}',
    justification TEXT NOT NULL,
    estimated_completion_hours INTEGER,
    prerequisites TEXT[] DEFAULT '{}',
    learning_objectives TEXT[] DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'pending_review' CHECK (status IN ('pending_review', 'under_review', 'approved', 'rejected', 'needs_revision')),
    
    -- Review fields
    reviewed_by UUID REFERENCES auth.users(id),
    reviewed_at TIMESTAMPTZ,
    review_notes TEXT,
    admin_rating INTEGER CHECK (admin_rating >= 1 AND admin_rating <= 5),
    
    -- Approval fields
    approved_vetting_level INTEGER CHECK (approved_vetting_level >= 1 AND approved_vetting_level <= 5),
    approved_skills TEXT[] DEFAULT '{}',
    final_justification TEXT,
    
    -- Metadata
    submission_source VARCHAR(50) DEFAULT 'user_suggestion',
    priority_level VARCHAR(20) DEFAULT 'normal' CHECK (priority_level IN ('low', 'normal', 'high', 'urgent')),
    community_votes INTEGER DEFAULT 0,
    expert_endorsements INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_vetting_content_suggestions_user_id ON public.vetting_content_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_vetting_content_suggestions_status ON public.vetting_content_suggestions(status);
CREATE INDEX IF NOT EXISTS idx_vetting_content_suggestions_vetting_level ON public.vetting_content_suggestions(suggested_vetting_level);
CREATE INDEX IF NOT EXISTS idx_vetting_content_suggestions_skills ON public.vetting_content_suggestions USING GIN(target_skills);

-- Add vetting-related columns to existing video_submissions table
DO $$ 
BEGIN
    -- Add suggest_for_vetting column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_submissions' AND column_name = 'suggest_for_vetting') THEN
        ALTER TABLE public.video_submissions ADD COLUMN suggest_for_vetting BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add suggested_vetting_level column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_submissions' AND column_name = 'suggested_vetting_level') THEN
        ALTER TABLE public.video_submissions ADD COLUMN suggested_vetting_level INTEGER CHECK (suggested_vetting_level >= 1 AND suggested_vetting_level <= 5);
    END IF;
    
    -- Add vetting_skills column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_submissions' AND column_name = 'vetting_skills') THEN
        ALTER TABLE public.video_submissions ADD COLUMN vetting_skills TEXT[] DEFAULT '{}';
    END IF;
    
    -- Add vetting_justification column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_submissions' AND column_name = 'vetting_justification') THEN
        ALTER TABLE public.video_submissions ADD COLUMN vetting_justification TEXT;
    END IF;
    
    -- Add estimated_completion_time column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_submissions' AND column_name = 'estimated_completion_time') THEN
        ALTER TABLE public.video_submissions ADD COLUMN estimated_completion_time INTEGER;
    END IF;
    
    -- Add vetting_approved column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_submissions' AND column_name = 'vetting_approved') THEN
        ALTER TABLE public.video_submissions ADD COLUMN vetting_approved BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add approved_vetting_level column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_submissions' AND column_name = 'approved_vetting_level') THEN
        ALTER TABLE public.video_submissions ADD COLUMN approved_vetting_level INTEGER CHECK (approved_vetting_level >= 1 AND approved_vetting_level <= 5);
    END IF;
END $$;

-- Create learning path recommendations table
CREATE TABLE IF NOT EXISTS public.learning_path_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    current_skill_level INTEGER NOT NULL CHECK (current_skill_level >= 0 AND current_skill_level <= 5),
    target_skill_level INTEGER NOT NULL CHECK (target_skill_level >= 1 AND target_skill_level <= 5),
    technology VARCHAR(100) NOT NULL,
    
    -- Recommended content
    recommended_videos JSONB DEFAULT '[]'::jsonb,
    estimated_total_hours INTEGER,
    difficulty_progression TEXT[] DEFAULT '{}',
    
    -- Progress tracking
    completed_videos TEXT[] DEFAULT '{}',
    current_progress_percentage INTEGER DEFAULT 0,
    started_at TIMESTAMPTZ,
    estimated_completion_date TIMESTAMPTZ,
    
    -- Personalization
    learning_style VARCHAR(50), -- visual, auditory, kinesthetic, reading
    preferred_content_length VARCHAR(20), -- short, medium, long
    availability_hours_per_week INTEGER,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, technology, target_skill_level)
);

-- Create indexes for learning path recommendations
CREATE INDEX IF NOT EXISTS idx_learning_path_recommendations_user_id ON public.learning_path_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_path_recommendations_technology ON public.learning_path_recommendations(technology);
CREATE INDEX IF NOT EXISTS idx_learning_path_recommendations_levels ON public.learning_path_recommendations(current_skill_level, target_skill_level);

-- Create vetting content votes table for community input
CREATE TABLE IF NOT EXISTS public.vetting_content_votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    suggestion_id UUID NOT NULL REFERENCES public.vetting_content_suggestions(id) ON DELETE CASCADE,
    vote_type VARCHAR(20) NOT NULL CHECK (vote_type IN ('upvote', 'downvote', 'helpful', 'not_helpful', 'appropriate_level', 'wrong_level')),
    comment TEXT,
    expertise_level INTEGER CHECK (expertise_level >= 0 AND expertise_level <= 5),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, suggestion_id, vote_type)
);

-- Create indexes for vetting content votes
CREATE INDEX IF NOT EXISTS idx_vetting_content_votes_suggestion_id ON public.vetting_content_votes(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_vetting_content_votes_vote_type ON public.vetting_content_votes(vote_type);

-- Create function to update vetting content suggestion vote counts
CREATE OR REPLACE FUNCTION update_vetting_suggestion_vote_counts()
RETURNS TRIGGER AS $$
BEGIN
    -- Update community votes count
    UPDATE public.vetting_content_suggestions 
    SET community_votes = (
        SELECT COUNT(*) 
        FROM public.vetting_content_votes 
        WHERE suggestion_id = COALESCE(NEW.suggestion_id, OLD.suggestion_id)
        AND vote_type IN ('upvote', 'helpful', 'appropriate_level')
    ) - (
        SELECT COUNT(*) 
        FROM public.vetting_content_votes 
        WHERE suggestion_id = COALESCE(NEW.suggestion_id, OLD.suggestion_id)
        AND vote_type IN ('downvote', 'not_helpful', 'wrong_level')
    ),
    updated_at = NOW()
    WHERE id = COALESCE(NEW.suggestion_id, OLD.suggestion_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for vote count updates
DROP TRIGGER IF EXISTS trigger_update_vetting_suggestion_vote_counts ON public.vetting_content_votes;
CREATE TRIGGER trigger_update_vetting_suggestion_vote_counts
    AFTER INSERT OR UPDATE OR DELETE ON public.vetting_content_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_vetting_suggestion_vote_counts();

-- Create function to automatically update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
DROP TRIGGER IF EXISTS trigger_update_vetting_content_suggestions_updated_at ON public.vetting_content_suggestions;
CREATE TRIGGER trigger_update_vetting_content_suggestions_updated_at
    BEFORE UPDATE ON public.vetting_content_suggestions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_learning_path_recommendations_updated_at ON public.learning_path_recommendations;
CREATE TRIGGER trigger_update_learning_path_recommendations_updated_at
    BEFORE UPDATE ON public.learning_path_recommendations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.vetting_content_suggestions IS 'User suggestions for content to be included in specific vetting levels';
COMMENT ON TABLE public.learning_path_recommendations IS 'Personalized learning path recommendations based on current and target skill levels';
COMMENT ON TABLE public.vetting_content_votes IS 'Community votes and feedback on vetting content suggestions';

COMMENT ON COLUMN public.vetting_content_suggestions.suggested_vetting_level IS 'Level 1-5 where user suggests this content should be placed';
COMMENT ON COLUMN public.vetting_content_suggestions.target_skills IS 'Array of skills this content helps develop';
COMMENT ON COLUMN public.vetting_content_suggestions.justification IS 'User explanation for why content fits the suggested level';
COMMENT ON COLUMN public.vetting_content_suggestions.community_votes IS 'Net community vote score (upvotes - downvotes)';

-- Insert sample vetting level content guidelines
-- Insert sample vetting content suggestions only if users exist
DO $$
DECLARE
    sample_user_id UUID;
BEGIN
    -- Get a sample user ID if any users exist
    SELECT id INTO sample_user_id FROM auth.users LIMIT 1;

    -- Only insert if we have a user
    IF sample_user_id IS NOT NULL THEN
        INSERT INTO public.vetting_content_suggestions (
            user_id, video_id, title, suggested_vetting_level, target_skills, justification, status, submission_source
        ) VALUES
        (
            sample_user_id,
            'dQw4w9WgXcQ',
            'Sample: JavaScript Fundamentals',
            1,
            ARRAY['javascript', 'programming'],
            'Excellent foundational content for Level 1 learning with clear explanations and practical examples',
            'approved',
            'admin_seed'
        ) ON CONFLICT DO NOTHING;
    END IF;
END $$;

-- Vetting Learning Integration tables created successfully!
-- Tables created: vetting_content_suggestions, learning_path_recommendations, vetting_content_votes
-- Enhanced video_submissions table with vetting integration columns
-- Added triggers for automatic vote counting and timestamp updates
